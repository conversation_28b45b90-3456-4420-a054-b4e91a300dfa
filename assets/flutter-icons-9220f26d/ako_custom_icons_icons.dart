/// Flutter icons AkoCustomIcons
/// Copyright (C) 2025 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  AkoCustomIcons
///      fonts:
///       - asset: fonts/AkoCustomIcons.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class AkoCustomIcons {
  AkoCustomIcons._();

  static const _kFontFam = 'AkoCustomIcons';
  static const String? _kFontPkg = null;

  static const IconData subtract = IconData(0xe800, fontFamily: _kFontFam, fontPackage: _kFontPkg);
}
