{"about": "About", "aboutUs": "About Us", "absenceDeduction": "Absence Deduction", "accountInfo": "Account Info", "active": "Active", "activity": "Activity", "add": "Add", "addAComment": "Add a Comment", "addAnnouncement": "Add Announcement", "addATask": "Add a Task", "addBreak": "Add Break", "addComment": "Add Comment", "addDepartment": "Add Department", "addedSuccessfully": "Added Successfully", "addEmployee": "Add Employee", "additionalNotes": "Additional Notes", "addLeave": "Add Leave", "addNewOffice": "Add New Office", "addNote": "Add a Note", "addPolicy": "Add Policy", "address": "Address", "addressInformation": "Address Information", "addShift": "Add Shift", "addTask": "Add Task", "adminContactMessage": "If you wish to edit, please contact the administration", "ai": "AI", "aiCapablityLine1": "Would you like me to organise a task?", "aiCapablityLine2": "Look up an employee’s info?", "aiCapablityLine3": "Or update something for you?", "aiCapablityLine4": "Just tell me what you need – I’m here to help!", "aiChatGreeting": "Hello and welcome to <PERSON><PERSON>!", "aiGreetingText": "How can I help you today?", "akoBasma": "<PERSON><PERSON>", "all": "All", "allDay": "All Day", "allEmployees": "All Employees", "allOffices": "All Offices", "allowances": "Allowances", "allProjects": "All Projects", "allTime": "All Time", "amount": "Amount", "amountOfDecrease": "Amount of Decrease", "amountOfIncrease": "Amount of Increase", "amountOfIncreaseOrDecrease": "Amount of Increase or Decrease", "announcement": "Announcement", "approve": "Approve", "approvedBy": "Approved By", "approveTask": "Approve Task", "approximateToDistanceMetres": "Approximate to ${distance} metres", "arabic": "Arabic", "archive": "Archive", "archiveEmployee": "Archive Employee", "archiveEmployeeWarning": "If the employee is archived, their account will be deactivated", "archiveEmployeeWarningSubtitle": "ARE YOU SURE YOU WANT TO ARCHIVE THE EMPLOYEE?", "archives": "Archives", "asiaBaghdad": "Asia/Baghdad", "assign": "Assign", "assignee": "Assignee", "assignToMe": "Assign To Me", "attachment": "Attachment", "attendance": "Attendance", "availableOnWeekends": "Available on Weekends", "back": "Back", "backlog": "Backlog", "basicSalary": "Basic Salary", "branch": "Branch", "breakTime": "Break Time:", "breakTimeLabel": "Break Time", "call": "Call", "camera": "Camera", "cancel": "Cancel", "canIGetMoreInfo": "Can I get more info?", "changesSavedSuccessfully": "Changes saved successfully", "chat": "Cha<PERSON>", "chatRequestConfirmation": "Are You Sure You Want to Send the Request?", "chatWithAi": "Chat With AI", "chatWithHr": "Chat With HR", "checkedOutHelpText": "Checked Out", "checkInTime": "Check-In Time", "checkOutTime": "Check-Out Time", "checkYourMessages": "Check your messages", "city": "City", "clear": "Clear", "clearSelection": "Clear Selection", "clickToUpload": "Click to Upload", "clockIn": "Clock In", "clockInTime": "Clock in Time", "clockOut": "Clock Out", "clockOutTime": "Clock Out Time", "close": "Close", "closingTimeMustBeAfterOpeningTime": "Closing time must be after opening time.", "comments": "Comments", "companyDirector": "Company Director", "companyInformation": "Company Information", "companyJobNumberStart": "Company Job Number Start", "companyName": "Company Name", "companyNews": "Company News", "companyPolicy": "Company Policy", "completed": "Completed", "completedTasks": "Completed Tasks", "confirm": "Confirm", "confirmed": "Confirmed", "confirmHours": "Confirm Hours", "confirmPassword": "Confirm Password", "contact": "Contact", "contactNumber": "Contact Number", "contractType": "Contract Type", "country": "Country", "create": "Create", "createAProject": "Create a Project", "createATask": "Create a Task", "createProject": "Create Project", "createTask": "Create Task", "critical": "Critical", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currentShift": "Current Shift", "customDateRange": "Custom Date Range", "daily": "Daily", "dailyShifts": "Daily Shifts", "dark": "Dark", "date": "Date", "dateOfBirth": "Date of Birth", "dateOfJoining": "Date of Joining", "dateOfResignation": "Date of Resignation", "days": "Days", "decrease": "Decrease", "deductions": "Deductions", "delay": "Delay:", "delete": "Delete", "deleteEmployee": "Delete Employee", "deleteEmployeeWarning": "This Action Cannot be Undone. All Employee Data and Related Information Will be Removed From The System", "delivered": "Delivered", "department": "Department", "departmentInformation": "Department Information", "departmentName": "Department Name", "description": "Description", "design": "Design", "disciplinaryFine": "Disciplinary Fine", "document": "Document", "documents": "Documents", "downloadAll": "Download All", "draft": "Draft", "drawSignature": "Draw Signature", "dueDate": "Due Date", "duration": "Duration", "edit": "Edit", "editAccountInfo": "Edit Account Info", "editAddress": "Edit Address", "editAnnouncement": "Edit Announcement", "editGroup": "Edit Group", "editInfo": "Edit Info", "editMembers": "Edit Members", "editName": "Edit Name", "editPolicies": "Edit Policies", "editSalary": "<PERSON>", "email": "Email", "emailAddress": "Email Address", "emp": "EMP", "employeeAddedSuccessfully": "Employee added successfully", "employeeIdCopySuccessMsg": "Employee ID copied to clipboard", "employeeManagement": "Employee Management", "employeeOfTheMonth": "Employee of the Month", "employeeRatingQuestionTitle": "How Do You Rate The Employee?", "employees": "Employees", "employeesOfTheMonth": "Employees of the Month", "employeesWorkingToday": "Employees Working Today", "employeeTimeOff": "Employee Time-off", "employeeTracking": "Employee Tracking", "employementType": "Employement Type", "endChat": "End <PERSON>", "endShift": "End Shift", "endWork": "End Work", "english": "English", "enterGroupDescription": "Enter group description", "enterGroupName": "Enter group name", "enterVerificationCode": "Please Enter The Verification Code Sent To {phonenumber}", "@enterVerificationCode": {"placeholders": {"phonenumber": {"type": "String"}}}, "enterYourNoteHere": "Enter Your Note Here", "error": "Error", "expenseRequests": "Expense Requests", "expenses": "Expenses", "failedToSelectImage": "Failed to select image. Please try again.", "feedback": "<PERSON><PERSON><PERSON>", "fileSelected": "File Selected", "filter": "Filter", "firstName": "First Name", "fixed": "Fixed", "forgotYourPassword": "Forgot your password", "from": "From", "fullAccess": "Full access", "gallery": "Gallery", "generalSettings": "الإعدادات العامة", "generatePayslip": "Generate Payslip", "good": "Good", "gross": "Gross", "grossSalary": "Gross Salary", "groupDescription": "Group Description", "groupMembersCount": "{membercount} Members", "@groupMembersCount": {"placeholders": {"membercount": {"type": "int"}}}, "groupName": "Group Name", "headquarters": "Headquarters", "helloHowCanIHelpYouToday": "Hello! How can I help you today?", "helloWorld": "مرحباً بالعالم!", "high": "High", "holidays": "Holidays", "home": "Home", "hourly": "Hourly", "hourlyRate": "Hourly Rate", "hours": "Hours", "hrApprovalMessage": "Your request has been approved.", "hrManager": "HR Manager", "ifYouClockOutNow": "If You Clock Out Now, You'll be Clock Out at", "increase": "Increase", "inProgress": "In Progress", "iqd": "IQD ", "iraq": "Iraq", "jobInformation": "Job Information", "jobTitle": "Job Title", "joined": "Joined", "language": "Language", "lastName": "Last Name", "lastSeenOn": "Last seen on {dateTime}", "@lastSeenOn": {"placeholders": {"dateTime": {"type": "String"}}}, "lastSeenOnLastseen": "Last seen on $lastSeen", "lastWorkingDay": "Last Working Day", "leave": "Leave", "leaveFrom": "Leave From", "leaveManagement": "Leave Management", "leavesHistory": "Leaves History", "leavesTaken": "Leaves Taken", "leaveTo": "Leave To", "leaveType": "Leave Type", "light": "Light", "location": "Location", "logIn": "Log in", "logo": "Logo", "logout": "Logout", "logOut": "Log Out", "logoutConfirmation": "Are You Sure You Want to Log Out?", "low": "Low", "maaxFileSizeInMB": "(Max. File size: {sizeInMb} MB)", "manageDepartment": "Manage Department", "manageEmployees": "Manage Employees", "management": "Management", "manageOffices": "Manage Offices", "manageProject": "Manage Project", "manager": "Manager", "managerInformation": "Manager Information", "managersFeedback": "Manager's <PERSON><PERSON><PERSON>", "managersOnly": "Managers only", "manageSalaries": "Manage Salaries", "manageYourEmployees": "Manage your employees", "manualAdditions": "Manual Additions", "manualDeductions": "Manual Deductions", "maxFileImageVideoSize": "(Max. File /Image / Video size: {sizeinmb} MB)", "@maxFileImageVideoSize": {"placeholders": {"sizeinmb": {"type": "int"}}}, "maxFileSizeInMB": "(Max. File size: {sizeInMb} MB)", "@maxFileSizeInMB": {"placeholders": {"sizeInMb": {"type": "int"}}}, "medium": "Medium", "members": "Members", "membersSelectedOfTotal": "MEMBERS : {selected} OF {total}", "@membersSelectedOfTotal": {"placeholders": {"selected": {"type": "int"}, "total": {"type": "int"}}}, "middleName": "Middle Name", "month": "Month", "monthly": "Monthly", "myLocation": "My Location", "myTasks": "My Tasks", "name": "Name", "nameClockedOutTime": "{name} Clocked Out One Hour Ago", "@nameClockedOutTime": {"placeholders": {"name": {"type": "String"}}}, "nameCreatedGroup": "{name} created group", "@nameCreatedGroup": {"placeholders": {"name": {"type": "String"}}}, "nationalId": "National ID", "nearbyPlaces": "Nearby Places", "needHelp": "Need Help", "netSalary": "Net Salary", "newChannel": "New Channel", "newChannelFunctionality": "New Channel functionality will be implemented", "newGroup": "New Group", "newPassword": "New Password", "news": "News", "next": "Next", "no": "No", "noContactsFound": "No Contacts Found", "noData": "No data available", "noIssues": "No issues", "noProjectFound": "No Project Found", "noTasksFound": "No Tasks Found For This Project", "notAvailable": "Not Available", "notEditable": "Not Editable", "notes": "Notes", "notification": "Notification", "notifications": "Notifications", "numberOfEmployees": "Number of Employees", "officeAddress": "Office Address", "officeContactInformation": "Office Contact Information", "officeDetails": "Office Details", "officeEmployees": "Office Employees", "officeInformation": "Office Information", "officeLocation": "Office Location", "officeLocationDescription": "Office Location Description", "officeName": "Office Name", "officeOfficeName": "Office: {officename}", "@officeOfficeName": {"placeholders": {"officename": {"type": "String"}}}, "offices": "Offices", "officeType": "Office Type", "ok": "OK", "online": "Online", "operatingHours": "Operating Hours", "optionalNote": "Optional Note", "other": "Other", "otherDocuments": "Other Documents", "otherInfo": "Other Info", "overtimeHours": "{hours} Hours this month", "@overtimeHours": {"placeholders": {"hours": {"type": "int"}}}, "overtimePay": "Overtime Pay", "overview": "Overview", "paid": "Paid", "password": "Password", "payForThisPeriod": "Pay for this period", "paymentStatus": "Payment Status", "payNow": "Pay Now", "payrollSummary": "Payroll Summary", "payslipConfirmationWarning": "By clicking Send, This Payslip Will be Sent To <PERSON><PERSON><PERSON>", "pending": "Pending", "pendingLeaveRequests": "Pending Leave Requests", "pendingRequest": "Pending Request", "pendingRequests": "Pending Requests", "performance": "Performance", "performanceAndAchievements": "Performance & Achievements", "performanceBonus": "Performance Bonus", "permissionsDepartment": "Permissions Department", "personalInformation": "Personal Information", "phone": "Phone", "phoneNumber": "Phone Number", "phoneNumberOrEmailAddress": "Phone Number Or Email Address", "pleaseCreateAProject": "Please Create A Project First To Add Tasks", "pleaseCreateATask": "Please Create a Task", "pleaseDrawSignature": "Please draw your signature", "pleaseEnterAGroupName": "Please enter a group name", "policy": "Policy", "policyDescription": "Policy Description", "policyName": "Policy Name", "poor": "Poor", "previousSalaries": "Previous Salaries", "priority": "Priority", "proceedConfirmation": "ARE YOU SURE YOU WANT TO PROCEED?", "profile": "Profile", "project": "Project", "projects": "Projects", "promoteConfirmationTitle": "Are You Sure You Want To Promote This Employee To The New Position?", "promotion": "Promotion", "publish": "Publish", "publishedByNAME": "Published By {name}", "@publishedByNAME": {"placeholders": {"name": {"type": "String"}}}, "read": "Read", "reasonForResignation": "Reason for Resignation", "recentlyViewed": "Recently Viewed", "record": "Record", "reject": "Reject", "rejected": "Rejected", "remainingLeaves": "Remaining Leaves", "rememberMe": "Remember Me", "reportAProblem": "Report A Problem", "requestedEdit": "Requested Edit", "requestForExpenses": "Request For Expenses", "requestForLeave": "Request for Leave", "requestSentSuccessfully": "Request sent successfully", "requestSubmittedSuccessfully": "Your request has been submitted successfully", "requestToChatWithHr": "Request to Chat with HR", "resendCode": "Resend Code", "resignation": "Resignation", "resignationRequest": "Resignation Request", "resigned": "Resigned", "restaurant": "Restaurant", "retry": "Retry", "review": "Review", "reward": "<PERSON><PERSON>", "role": "Role", "salary": "Salary", "salaryDetails": "Salary Details", "salaryHistory": "Salary History", "salaryManagement": "Salary Management", "salaryPaymentConfirmation": "Salary Payment Confirmation", "save": "Save", "savedSuccessfully": "Saved Successfully", "saveSend": "Save & Send", "schedule": "Schedule", "schedules": "Schedules", "search": "Search", "searchForATask": "Search for a task", "searching": "Searching", "searchingForATask": "searching for a task", "selectAudience": "Select Audience", "selectClosingTime": "Select Closing Time", "selectContacts": "Select Contacts", "selectDocument": "Select Document", "selectDocuments": "Select Documents", "selectOpeningTime": "Select Opening Time", "selectTimeFrom": "Select Time From", "selectTimeTo": "Select Time To", "send": "Send", "sendShiftChangeRequest": "Send Shift Change Request", "sendYourCurrentLocation": "Send Your Current Location", "sendYourLiveLocation": "Send Your Live Location", "settings": "Settings", "shiftDetails": "Shift Details", "shiftEdit": "Shift Edit", "shiftEditRequests": "Shift Edit Requests", "shiftHistory": "Shift History", "shiftWorkingTime": "Shift Working Time", "showAll": "Show all", "sinceStartOfYear": "Since Start of Year", "someErrorOcurred": "Some error ocurred", "sorrySomeErrorOccured": "Sorry, Some error occured", "startBreak": "Start Break", "startDate": "Start Date", "startWork": "Start Work", "status": "Status", "statusUpdate": "Status Update", "submit": "Submit", "submitResignation": "Submit Resignation", "success": "Success", "successfullyCompletedShift": "Successfully completed your shift", "successfullyPaid": "Successfully Paid", "sundayToFriday": "Sunday To Friday", "support": "Support", "systemGeneratedAdditions": "System-Generated Additions ", "systemGeneratedDeductions": "System-Generated Deductions", "taskAddedSuccessfully": "Task added successfully", "taskCompleted": "Task Completed", "taskDescription": "Task Description", "taskDetails": "Task Details", "tasks": "Tasks", "taxes": "Taxes", "team": "Team", "teams": "Teams", "termsOfUse": "Terms of Use", "theme": "Theme", "themeMode": "Theme Mode", "theNewPosition": "The New Position", "theNewSalary": "The New Salary", "thereAreNoNotifications": "There are no notifications", "theReason": "The Reason", "thisMonth": "this month", "thisMonthFilter": "This Month", "thisMonthsPayroll": "This Month's Payroll", "thisMonthsSalary": "This Month's Salary", "thisMonthsSalaryDetails": "This Month's Salary Details", "thisWeek": "This Week", "timeClock": "Time Clock", "timeOffRequests": "Time Off Requests", "timeOffTaken": "Time Off Taken", "timesheet": "Timesheet", "timeZone": "Time Zone", "timingsError": "The timings look incorrect", "title": "Title", "to": "To", "today": "Today", "totalAdditions": "Total Additions", "totalDeductions": "Total Deductions", "totalEmployees": "Total Employees", "totalHours": "Total Hours", "totalShiftTime": "Total Shift Time", "typing": "Typing...", "unavailable": "Unavailable", "unionFees": "Union Fees", "unpaid": "Unpaid", "unread": "Unread", "updatedSuccessfully": "Updated Successfully", "updatePolicy": "Update Policy", "uploadDocuments": "Upload Documents", "valueHours": "{hours} Hours", "@valueHours": {"placeholders": {"hours": {"type": "num"}}}, "verificationCode": "Please Enter The Verification Code Sent To", "viewActivity": "View Activity", "viewAll": "View All", "waitingForManagerApproval": "Waiting for Manager <PERSON><PERSON><PERSON><PERSON>", "website": "Website", "weekly": "Weekly", "welcome": "Welcome", "workDayYesNo": "Work Day(Yes/No)", "workingHours": "Working Hours", "workingSchedule": "Working Schedule", "workspace": "Workspace", "writeAMessage": "Write a message...", "writeANoteForTheEmployee": "Write a note for the employee", "writeFeedback": "Write Feedback", "year": "Year", "yearly": "Yearly", "yes": "Yes", "yesterday": "Yesterday", "you": "You", "youCreatedGroup": "{name} created this group", "@youCreatedGroup": {"placeholders": {"name": {"type": "String"}}}, "zipCode": "Zip Code"}