import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_ku.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('ku')
  ];

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @aboutUs.
  ///
  /// In en, this message translates to:
  /// **'About Us'**
  String get aboutUs;

  /// No description provided for @absenceDeduction.
  ///
  /// In en, this message translates to:
  /// **'Absence Deduction'**
  String get absenceDeduction;

  /// No description provided for @accountInfo.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInfo;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @activity.
  ///
  /// In en, this message translates to:
  /// **'Activity'**
  String get activity;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @addAComment.
  ///
  /// In en, this message translates to:
  /// **'Add a Comment'**
  String get addAComment;

  /// No description provided for @addAnnouncement.
  ///
  /// In en, this message translates to:
  /// **'Add Announcement'**
  String get addAnnouncement;

  /// No description provided for @addATask.
  ///
  /// In en, this message translates to:
  /// **'Add a Task'**
  String get addATask;

  /// No description provided for @addBreak.
  ///
  /// In en, this message translates to:
  /// **'Add Break'**
  String get addBreak;

  /// No description provided for @addComment.
  ///
  /// In en, this message translates to:
  /// **'Add Comment'**
  String get addComment;

  /// No description provided for @addDepartment.
  ///
  /// In en, this message translates to:
  /// **'Add Department'**
  String get addDepartment;

  /// No description provided for @addedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Added Successfully'**
  String get addedSuccessfully;

  /// No description provided for @addEmployee.
  ///
  /// In en, this message translates to:
  /// **'Add Employee'**
  String get addEmployee;

  /// No description provided for @additionalNotes.
  ///
  /// In en, this message translates to:
  /// **'Additional Notes'**
  String get additionalNotes;

  /// No description provided for @addLeave.
  ///
  /// In en, this message translates to:
  /// **'Add Leave'**
  String get addLeave;

  /// No description provided for @addNewOffice.
  ///
  /// In en, this message translates to:
  /// **'Add New Office'**
  String get addNewOffice;

  /// No description provided for @addNote.
  ///
  /// In en, this message translates to:
  /// **'Add a Note'**
  String get addNote;

  /// No description provided for @addPolicy.
  ///
  /// In en, this message translates to:
  /// **'Add Policy'**
  String get addPolicy;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @addressInformation.
  ///
  /// In en, this message translates to:
  /// **'Address Information'**
  String get addressInformation;

  /// No description provided for @addShift.
  ///
  /// In en, this message translates to:
  /// **'Add Shift'**
  String get addShift;

  /// No description provided for @addTask.
  ///
  /// In en, this message translates to:
  /// **'Add Task'**
  String get addTask;

  /// No description provided for @adminContactMessage.
  ///
  /// In en, this message translates to:
  /// **'If You Wish to Edit, Please Contact The Administration'**
  String get adminContactMessage;

  /// No description provided for @ai.
  ///
  /// In en, this message translates to:
  /// **'AI'**
  String get ai;

  /// No description provided for @aiCapablityLine1.
  ///
  /// In en, this message translates to:
  /// **'Would You Like Me to Organise a Task?'**
  String get aiCapablityLine1;

  /// No description provided for @aiCapablityLine2.
  ///
  /// In en, this message translates to:
  /// **'Look Up An Employee’s Information?'**
  String get aiCapablityLine2;

  /// No description provided for @aiCapablityLine3.
  ///
  /// In en, this message translates to:
  /// **'Or Update Something For You?'**
  String get aiCapablityLine3;

  /// No description provided for @aiCapablityLine4.
  ///
  /// In en, this message translates to:
  /// **'Just Tell Me What You Need, I Am Here To Help!'**
  String get aiCapablityLine4;

  /// No description provided for @aiChatGreeting.
  ///
  /// In en, this message translates to:
  /// **'Hello and Welcome to Ako Basma AI!'**
  String get aiChatGreeting;

  /// No description provided for @aiGreetingText.
  ///
  /// In en, this message translates to:
  /// **'How Can I Help You Today?'**
  String get aiGreetingText;

  /// No description provided for @akoBasma.
  ///
  /// In en, this message translates to:
  /// **'Ako Basma'**
  String get akoBasma;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @allDay.
  ///
  /// In en, this message translates to:
  /// **'All Day'**
  String get allDay;

  /// No description provided for @allEmployees.
  ///
  /// In en, this message translates to:
  /// **'All Employees'**
  String get allEmployees;

  /// No description provided for @allOffices.
  ///
  /// In en, this message translates to:
  /// **'All Offices'**
  String get allOffices;

  /// No description provided for @allowances.
  ///
  /// In en, this message translates to:
  /// **'Allowances'**
  String get allowances;

  /// No description provided for @allProjects.
  ///
  /// In en, this message translates to:
  /// **'All Projects'**
  String get allProjects;

  /// No description provided for @allTime.
  ///
  /// In en, this message translates to:
  /// **'All Time'**
  String get allTime;

  /// No description provided for @am.
  ///
  /// In en, this message translates to:
  /// **'AM'**
  String get am;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @amountIqd.
  ///
  /// In en, this message translates to:
  /// **'Amount IQD'**
  String get amountIqd;

  /// No description provided for @amountOfDecrease.
  ///
  /// In en, this message translates to:
  /// **'Amount of Decrease'**
  String get amountOfDecrease;

  /// No description provided for @amountOfIncrease.
  ///
  /// In en, this message translates to:
  /// **'Amount of Increase'**
  String get amountOfIncrease;

  /// No description provided for @amountOfIncreaseOrDecrease.
  ///
  /// In en, this message translates to:
  /// **'Amount of Increase or Decrease'**
  String get amountOfIncreaseOrDecrease;

  /// No description provided for @announcement.
  ///
  /// In en, this message translates to:
  /// **'Announcement'**
  String get announcement;

  /// No description provided for @approve.
  ///
  /// In en, this message translates to:
  /// **'Approve'**
  String get approve;

  /// No description provided for @approved.
  ///
  /// In en, this message translates to:
  /// **'Approved'**
  String get approved;

  /// No description provided for @approvedBy.
  ///
  /// In en, this message translates to:
  /// **'Approved By'**
  String get approvedBy;

  /// No description provided for @approveTask.
  ///
  /// In en, this message translates to:
  /// **'Approve Task'**
  String get approveTask;

  /// No description provided for @approximateToDistanceMetres.
  ///
  /// In en, this message translates to:
  /// **'Approximately {distance} Metres Away'**
  String approximateToDistanceMetres(Object distance);

  /// No description provided for @apr.
  ///
  /// In en, this message translates to:
  /// **'Apr'**
  String get apr;

  /// No description provided for @april.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get april;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabic;

  /// No description provided for @archive.
  ///
  /// In en, this message translates to:
  /// **'Archive'**
  String get archive;

  /// No description provided for @archiveEmployee.
  ///
  /// In en, this message translates to:
  /// **'Archive Employee'**
  String get archiveEmployee;

  /// No description provided for @archiveEmployeeWarning.
  ///
  /// In en, this message translates to:
  /// **'If The Employee Is Archived, Their Account Will Be Deactivated!'**
  String get archiveEmployeeWarning;

  /// No description provided for @archiveEmployeeWarningSubtitle.
  ///
  /// In en, this message translates to:
  /// **'ARE YOU SURE YOU WANT TO ARCHIVE THIS EMPLOYEE?'**
  String get archiveEmployeeWarningSubtitle;

  /// No description provided for @archives.
  ///
  /// In en, this message translates to:
  /// **'Archives'**
  String get archives;

  /// No description provided for @asiaBaghdad.
  ///
  /// In en, this message translates to:
  /// **'Asia/Baghdad'**
  String get asiaBaghdad;

  /// No description provided for @assign.
  ///
  /// In en, this message translates to:
  /// **'Assign'**
  String get assign;

  /// No description provided for @assignee.
  ///
  /// In en, this message translates to:
  /// **'Assignee'**
  String get assignee;

  /// No description provided for @assignToMe.
  ///
  /// In en, this message translates to:
  /// **'Assign to Me'**
  String get assignToMe;

  /// No description provided for @attachment.
  ///
  /// In en, this message translates to:
  /// **'Attachment'**
  String get attachment;

  /// No description provided for @attendance.
  ///
  /// In en, this message translates to:
  /// **'Attendance'**
  String get attendance;

  /// No description provided for @aug.
  ///
  /// In en, this message translates to:
  /// **'Aug'**
  String get aug;

  /// No description provided for @august.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get august;

  /// No description provided for @availableOnWeekends.
  ///
  /// In en, this message translates to:
  /// **'Available on Weekends'**
  String get availableOnWeekends;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @backlog.
  ///
  /// In en, this message translates to:
  /// **'Backlog'**
  String get backlog;

  /// No description provided for @basicSalary.
  ///
  /// In en, this message translates to:
  /// **'Basic Salary'**
  String get basicSalary;

  /// No description provided for @branch.
  ///
  /// In en, this message translates to:
  /// **'Branch'**
  String get branch;

  /// No description provided for @breakTime.
  ///
  /// In en, this message translates to:
  /// **'Break Time:'**
  String get breakTime;

  /// No description provided for @breakTimeLabel.
  ///
  /// In en, this message translates to:
  /// **'Break Time'**
  String get breakTimeLabel;

  /// No description provided for @call.
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get call;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @canEdit.
  ///
  /// In en, this message translates to:
  /// **'Can Edit'**
  String get canEdit;

  /// No description provided for @canIGetMoreInfo.
  ///
  /// In en, this message translates to:
  /// **'Can I Get More Information?'**
  String get canIGetMoreInfo;

  /// No description provided for @canView.
  ///
  /// In en, this message translates to:
  /// **'Can View'**
  String get canView;

  /// No description provided for @changesSavedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Changes Saved Successfully'**
  String get changesSavedSuccessfully;

  /// No description provided for @chat.
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get chat;

  /// No description provided for @chatRequestConfirmation.
  ///
  /// In en, this message translates to:
  /// **'ARE YOU SURE YOU WANT TO SEND THIS REQUEST?'**
  String get chatRequestConfirmation;

  /// No description provided for @chatWithAi.
  ///
  /// In en, this message translates to:
  /// **'Chat with AI'**
  String get chatWithAi;

  /// No description provided for @chatWithHr.
  ///
  /// In en, this message translates to:
  /// **'Chat with HR'**
  String get chatWithHr;

  /// No description provided for @checkedOutHelpText.
  ///
  /// In en, this message translates to:
  /// **'Checked Out'**
  String get checkedOutHelpText;

  /// No description provided for @checkInTime.
  ///
  /// In en, this message translates to:
  /// **'Check-In Time'**
  String get checkInTime;

  /// No description provided for @checkOutTime.
  ///
  /// In en, this message translates to:
  /// **'Check-Out Time'**
  String get checkOutTime;

  /// No description provided for @checkYourMessages.
  ///
  /// In en, this message translates to:
  /// **'Check your messages'**
  String get checkYourMessages;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @clearSelection.
  ///
  /// In en, this message translates to:
  /// **'Clear Selection'**
  String get clearSelection;

  /// No description provided for @clickToUpload.
  ///
  /// In en, this message translates to:
  /// **'Click to Upload'**
  String get clickToUpload;

  /// No description provided for @clockIn.
  ///
  /// In en, this message translates to:
  /// **'Clock In'**
  String get clockIn;

  /// No description provided for @clockInTime.
  ///
  /// In en, this message translates to:
  /// **'Clock-In Time'**
  String get clockInTime;

  /// No description provided for @clockOut.
  ///
  /// In en, this message translates to:
  /// **'Clock Out'**
  String get clockOut;

  /// No description provided for @clockOutTime.
  ///
  /// In en, this message translates to:
  /// **'Clock-Out Time'**
  String get clockOutTime;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @closingTimeMustBeAfterOpeningTime.
  ///
  /// In en, this message translates to:
  /// **'Closing Time Must be After Opening Time.'**
  String get closingTimeMustBeAfterOpeningTime;

  /// No description provided for @comments.
  ///
  /// In en, this message translates to:
  /// **'Comments'**
  String get comments;

  /// No description provided for @companyDirector.
  ///
  /// In en, this message translates to:
  /// **'Company Director'**
  String get companyDirector;

  /// No description provided for @companyInformation.
  ///
  /// In en, this message translates to:
  /// **'Company Information'**
  String get companyInformation;

  /// No description provided for @companyJobNumberStart.
  ///
  /// In en, this message translates to:
  /// **'Company Employee Number Sequence'**
  String get companyJobNumberStart;

  /// No description provided for @companyName.
  ///
  /// In en, this message translates to:
  /// **'Company Name'**
  String get companyName;

  /// No description provided for @companyNews.
  ///
  /// In en, this message translates to:
  /// **'Company News'**
  String get companyNews;

  /// No description provided for @companyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Company Policy'**
  String get companyPolicy;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @completedTasks.
  ///
  /// In en, this message translates to:
  /// **'Completed Tasks'**
  String get completedTasks;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @confirmed.
  ///
  /// In en, this message translates to:
  /// **'Confirmed'**
  String get confirmed;

  /// No description provided for @confirmHours.
  ///
  /// In en, this message translates to:
  /// **'Confirm Hours'**
  String get confirmHours;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get contact;

  /// No description provided for @contactNumber.
  ///
  /// In en, this message translates to:
  /// **'Contact Number'**
  String get contactNumber;

  /// No description provided for @contractType.
  ///
  /// In en, this message translates to:
  /// **'Contract Type'**
  String get contractType;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// No description provided for @createAProject.
  ///
  /// In en, this message translates to:
  /// **'Create a Project'**
  String get createAProject;

  /// No description provided for @createATask.
  ///
  /// In en, this message translates to:
  /// **'Create a Task'**
  String get createATask;

  /// No description provided for @createProject.
  ///
  /// In en, this message translates to:
  /// **'Create Project'**
  String get createProject;

  /// No description provided for @createTask.
  ///
  /// In en, this message translates to:
  /// **'Create Task'**
  String get createTask;

  /// No description provided for @critical.
  ///
  /// In en, this message translates to:
  /// **'Critical'**
  String get critical;

  /// No description provided for @currency.
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// No description provided for @currentShift.
  ///
  /// In en, this message translates to:
  /// **'Current Shift'**
  String get currentShift;

  /// No description provided for @customDateRange.
  ///
  /// In en, this message translates to:
  /// **'Custom Date Range'**
  String get customDateRange;

  /// No description provided for @daily.
  ///
  /// In en, this message translates to:
  /// **'Daily'**
  String get daily;

  /// No description provided for @dailyShifts.
  ///
  /// In en, this message translates to:
  /// **'Daily Shifts'**
  String get dailyShifts;

  /// No description provided for @dark.
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get dark;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @dateOfBirth.
  ///
  /// In en, this message translates to:
  /// **'Date of Birth'**
  String get dateOfBirth;

  /// No description provided for @dateOfJoining.
  ///
  /// In en, this message translates to:
  /// **'Date of Joining'**
  String get dateOfJoining;

  /// No description provided for @dateOfResignation.
  ///
  /// In en, this message translates to:
  /// **'Date of Resignation'**
  String get dateOfResignation;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'Days'**
  String get days;

  /// No description provided for @dec.
  ///
  /// In en, this message translates to:
  /// **'Dec'**
  String get dec;

  /// No description provided for @december.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get december;

  /// No description provided for @decrease.
  ///
  /// In en, this message translates to:
  /// **'Decrease'**
  String get decrease;

  /// No description provided for @deductions.
  ///
  /// In en, this message translates to:
  /// **'Deductions'**
  String get deductions;

  /// No description provided for @delay.
  ///
  /// In en, this message translates to:
  /// **'Delay:'**
  String get delay;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @deleteEmployee.
  ///
  /// In en, this message translates to:
  /// **'Delete Employee'**
  String get deleteEmployee;

  /// No description provided for @deleteEmployeeWarning.
  ///
  /// In en, this message translates to:
  /// **'This Action Cannot be Undone. All Employee Data and Related Information Will be Removed From The System'**
  String get deleteEmployeeWarning;

  /// No description provided for @delivered.
  ///
  /// In en, this message translates to:
  /// **'Delivered'**
  String get delivered;

  /// No description provided for @department.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;

  /// No description provided for @departmentInformation.
  ///
  /// In en, this message translates to:
  /// **'Department Information'**
  String get departmentInformation;

  /// No description provided for @departmentName.
  ///
  /// In en, this message translates to:
  /// **'Department Name'**
  String get departmentName;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @design.
  ///
  /// In en, this message translates to:
  /// **'Design'**
  String get design;

  /// No description provided for @disciplinaryFine.
  ///
  /// In en, this message translates to:
  /// **'Disciplinary Fine'**
  String get disciplinaryFine;

  /// No description provided for @document.
  ///
  /// In en, this message translates to:
  /// **'Document'**
  String get document;

  /// No description provided for @documents.
  ///
  /// In en, this message translates to:
  /// **'Documents'**
  String get documents;

  /// No description provided for @downloadAll.
  ///
  /// In en, this message translates to:
  /// **'Download All'**
  String get downloadAll;

  /// No description provided for @draft.
  ///
  /// In en, this message translates to:
  /// **'Draft'**
  String get draft;

  /// No description provided for @drawSignature.
  ///
  /// In en, this message translates to:
  /// **'Draw Signature'**
  String get drawSignature;

  /// No description provided for @dueDate.
  ///
  /// In en, this message translates to:
  /// **'Due Date'**
  String get dueDate;

  /// No description provided for @duration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @editAccountInfo.
  ///
  /// In en, this message translates to:
  /// **'Edit Account Information'**
  String get editAccountInfo;

  /// No description provided for @editAddress.
  ///
  /// In en, this message translates to:
  /// **'Edit Address'**
  String get editAddress;

  /// No description provided for @editAnnouncement.
  ///
  /// In en, this message translates to:
  /// **'Edit Announcement'**
  String get editAnnouncement;

  /// No description provided for @editGroup.
  ///
  /// In en, this message translates to:
  /// **'Edit Group'**
  String get editGroup;

  /// No description provided for @editInfo.
  ///
  /// In en, this message translates to:
  /// **'Edit Info'**
  String get editInfo;

  /// No description provided for @editMembers.
  ///
  /// In en, this message translates to:
  /// **'Edit Members'**
  String get editMembers;

  /// No description provided for @editName.
  ///
  /// In en, this message translates to:
  /// **'Edit Name'**
  String get editName;

  /// No description provided for @editPolicies.
  ///
  /// In en, this message translates to:
  /// **'Edit Policies'**
  String get editPolicies;

  /// No description provided for @editSalary.
  ///
  /// In en, this message translates to:
  /// **'Edit Salary'**
  String get editSalary;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emailAddress.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// No description provided for @emp.
  ///
  /// In en, this message translates to:
  /// **'Employee No'**
  String get emp;

  /// No description provided for @employeeAddedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Employee Added Successfully'**
  String get employeeAddedSuccessfully;

  /// No description provided for @employeeIdCopySuccessMsg.
  ///
  /// In en, this message translates to:
  /// **'Employee No Copied to Clipboard'**
  String get employeeIdCopySuccessMsg;

  /// No description provided for @employeeManagement.
  ///
  /// In en, this message translates to:
  /// **'Employee Management'**
  String get employeeManagement;

  /// No description provided for @employeeOfTheMonth.
  ///
  /// In en, this message translates to:
  /// **'Employee of The Month'**
  String get employeeOfTheMonth;

  /// No description provided for @employeeRatingQuestionTitle.
  ///
  /// In en, this message translates to:
  /// **'How Do You Rate This Employee?'**
  String get employeeRatingQuestionTitle;

  /// No description provided for @employees.
  ///
  /// In en, this message translates to:
  /// **'Employees'**
  String get employees;

  /// No description provided for @employeesOfTheMonth.
  ///
  /// In en, this message translates to:
  /// **'Employees of The Month'**
  String get employeesOfTheMonth;

  /// No description provided for @employeesWorkingToday.
  ///
  /// In en, this message translates to:
  /// **'Employees Working Today'**
  String get employeesWorkingToday;

  /// No description provided for @employeeTimeOff.
  ///
  /// In en, this message translates to:
  /// **'Employee Time-Off'**
  String get employeeTimeOff;

  /// No description provided for @employeeTracking.
  ///
  /// In en, this message translates to:
  /// **'Employee Tracking'**
  String get employeeTracking;

  /// No description provided for @employementType.
  ///
  /// In en, this message translates to:
  /// **'Employment Type'**
  String get employementType;

  /// No description provided for @endChat.
  ///
  /// In en, this message translates to:
  /// **'End Chat'**
  String get endChat;

  /// No description provided for @endShift.
  ///
  /// In en, this message translates to:
  /// **'End Shift'**
  String get endShift;

  /// No description provided for @endWork.
  ///
  /// In en, this message translates to:
  /// **'End Work'**
  String get endWork;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @enterGroupDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter Group Description'**
  String get enterGroupDescription;

  /// No description provided for @enterGroupName.
  ///
  /// In en, this message translates to:
  /// **'Enter Group Name'**
  String get enterGroupName;

  /// No description provided for @enterVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Please Enter The Verification Code Sent To {phonenumber}'**
  String enterVerificationCode(String phonenumber);

  /// No description provided for @enterYourNoteHere.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Note Here'**
  String get enterYourNoteHere;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @expenseRequests.
  ///
  /// In en, this message translates to:
  /// **'Expense Requests'**
  String get expenseRequests;

  /// No description provided for @expenses.
  ///
  /// In en, this message translates to:
  /// **'Expenses'**
  String get expenses;

  /// No description provided for @failed.
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get failed;

  /// No description provided for @failedToSelectImage.
  ///
  /// In en, this message translates to:
  /// **'Failed to Select Image, Please Try Again.'**
  String get failedToSelectImage;

  /// No description provided for @feb.
  ///
  /// In en, this message translates to:
  /// **'Feb'**
  String get feb;

  /// No description provided for @february.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get february;

  /// No description provided for @feedback.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// No description provided for @fileSelected.
  ///
  /// In en, this message translates to:
  /// **'File Selected'**
  String get fileSelected;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @firstName.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// No description provided for @fixed.
  ///
  /// In en, this message translates to:
  /// **'Fixed'**
  String get fixed;

  /// No description provided for @forgotYourPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Your Password?'**
  String get forgotYourPassword;

  /// No description provided for @from.
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// No description provided for @fullAccess.
  ///
  /// In en, this message translates to:
  /// **'Full Access'**
  String get fullAccess;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @generalSettings.
  ///
  /// In en, this message translates to:
  /// **'General Settings'**
  String get generalSettings;

  /// No description provided for @generatePayslip.
  ///
  /// In en, this message translates to:
  /// **'Generate Payslip'**
  String get generatePayslip;

  /// No description provided for @good.
  ///
  /// In en, this message translates to:
  /// **'Good'**
  String get good;

  /// No description provided for @gross.
  ///
  /// In en, this message translates to:
  /// **'Gross'**
  String get gross;

  /// No description provided for @grossSalary.
  ///
  /// In en, this message translates to:
  /// **'Gross Salary'**
  String get grossSalary;

  /// No description provided for @groupDescription.
  ///
  /// In en, this message translates to:
  /// **'Group Description'**
  String get groupDescription;

  /// No description provided for @groupMembersCount.
  ///
  /// In en, this message translates to:
  /// **'{membercount} Members'**
  String groupMembersCount(int membercount);

  /// No description provided for @groupName.
  ///
  /// In en, this message translates to:
  /// **'Group Name'**
  String get groupName;

  /// No description provided for @headquarters.
  ///
  /// In en, this message translates to:
  /// **'Headquarters'**
  String get headquarters;

  /// No description provided for @helloHowCanIHelpYouToday.
  ///
  /// In en, this message translates to:
  /// **'Hello! How Can I Help You Today?'**
  String get helloHowCanIHelpYouToday;

  /// No description provided for @helloWorld.
  ///
  /// In en, this message translates to:
  /// **'Hello World!'**
  String get helloWorld;

  /// No description provided for @high.
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// No description provided for @holidays.
  ///
  /// In en, this message translates to:
  /// **'Holidays'**
  String get holidays;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @hourly.
  ///
  /// In en, this message translates to:
  /// **'Hourly'**
  String get hourly;

  /// No description provided for @hourlyRate.
  ///
  /// In en, this message translates to:
  /// **'Hourly Rate'**
  String get hourlyRate;

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours;

  /// No description provided for @hoursForToday.
  ///
  /// In en, this message translates to:
  /// **'{hours} Hours for Today'**
  String hoursForToday(Object hours);

  /// No description provided for @hoursLetter.
  ///
  /// In en, this message translates to:
  /// **'{hours} H'**
  String hoursLetter(Object hours);

  /// No description provided for @hrApprovalMessage.
  ///
  /// In en, this message translates to:
  /// **'Your Request Has Been Approved.'**
  String get hrApprovalMessage;

  /// No description provided for @hrManager.
  ///
  /// In en, this message translates to:
  /// **'HR Manager'**
  String get hrManager;

  /// No description provided for @ifYouClockOutNow.
  ///
  /// In en, this message translates to:
  /// **'If You Clock Out Now, You\'ll be Clock Out at'**
  String get ifYouClockOutNow;

  /// No description provided for @increase.
  ///
  /// In en, this message translates to:
  /// **'Increase'**
  String get increase;

  /// No description provided for @inProgress.
  ///
  /// In en, this message translates to:
  /// **'In Progress'**
  String get inProgress;

  /// No description provided for @iqd.
  ///
  /// In en, this message translates to:
  /// **'IQD'**
  String get iqd;

  /// No description provided for @iraq.
  ///
  /// In en, this message translates to:
  /// **'Iraq'**
  String get iraq;

  /// No description provided for @jan.
  ///
  /// In en, this message translates to:
  /// **'Jan'**
  String get jan;

  /// No description provided for @january.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get january;

  /// No description provided for @jobInformation.
  ///
  /// In en, this message translates to:
  /// **'Job Information'**
  String get jobInformation;

  /// No description provided for @jobTitle.
  ///
  /// In en, this message translates to:
  /// **'Job Title'**
  String get jobTitle;

  /// No description provided for @joined.
  ///
  /// In en, this message translates to:
  /// **'Joined'**
  String get joined;

  /// No description provided for @jul.
  ///
  /// In en, this message translates to:
  /// **'Jul'**
  String get jul;

  /// No description provided for @july.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get july;

  /// No description provided for @jun.
  ///
  /// In en, this message translates to:
  /// **'Jun'**
  String get jun;

  /// No description provided for @june.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get june;

  /// No description provided for @kurdish.
  ///
  /// In en, this message translates to:
  /// **'Kurdish'**
  String get kurdish;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @lastName.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// No description provided for @lastSeenOn.
  ///
  /// In en, this message translates to:
  /// **'Last Seen on {dateTime}'**
  String lastSeenOn(String dateTime);

  /// No description provided for @lastSeenOnLastseen.
  ///
  /// In en, this message translates to:
  /// **'Last Seen on {lastSeen}'**
  String lastSeenOnLastseen(Object lastSeen);

  /// No description provided for @lastWorkingDay.
  ///
  /// In en, this message translates to:
  /// **'Last Working Day'**
  String get lastWorkingDay;

  /// No description provided for @leave.
  ///
  /// In en, this message translates to:
  /// **'Leave'**
  String get leave;

  /// No description provided for @leaveFrom.
  ///
  /// In en, this message translates to:
  /// **'Leave From'**
  String get leaveFrom;

  /// No description provided for @leaveManagement.
  ///
  /// In en, this message translates to:
  /// **'Leave Management'**
  String get leaveManagement;

  /// No description provided for @leavesHistory.
  ///
  /// In en, this message translates to:
  /// **'Leaves History'**
  String get leavesHistory;

  /// No description provided for @leavesTaken.
  ///
  /// In en, this message translates to:
  /// **'Leaves Taken'**
  String get leavesTaken;

  /// No description provided for @leaveTo.
  ///
  /// In en, this message translates to:
  /// **'Leave To'**
  String get leaveTo;

  /// No description provided for @leaveType.
  ///
  /// In en, this message translates to:
  /// **'Leave Type'**
  String get leaveType;

  /// No description provided for @light.
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @logIn.
  ///
  /// In en, this message translates to:
  /// **'Log In'**
  String get logIn;

  /// No description provided for @logo.
  ///
  /// In en, this message translates to:
  /// **'Logo'**
  String get logo;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logout;

  /// No description provided for @logOut.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logOut;

  /// No description provided for @logoutConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are You Sure You Want to Log Out?'**
  String get logoutConfirmation;

  /// No description provided for @low.
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// No description provided for @manageDepartment.
  ///
  /// In en, this message translates to:
  /// **'Manage Department'**
  String get manageDepartment;

  /// No description provided for @manageEmployees.
  ///
  /// In en, this message translates to:
  /// **'Manage Employees'**
  String get manageEmployees;

  /// No description provided for @management.
  ///
  /// In en, this message translates to:
  /// **'Management'**
  String get management;

  /// No description provided for @manageOffices.
  ///
  /// In en, this message translates to:
  /// **'Manage Offices'**
  String get manageOffices;

  /// No description provided for @manageProject.
  ///
  /// In en, this message translates to:
  /// **'Manage Project'**
  String get manageProject;

  /// No description provided for @manager.
  ///
  /// In en, this message translates to:
  /// **'Manager'**
  String get manager;

  /// No description provided for @managerInformation.
  ///
  /// In en, this message translates to:
  /// **'Manager Information'**
  String get managerInformation;

  /// No description provided for @managersFeedback.
  ///
  /// In en, this message translates to:
  /// **'Manager\'s Feedback'**
  String get managersFeedback;

  /// No description provided for @managersOnly.
  ///
  /// In en, this message translates to:
  /// **'Managers Only'**
  String get managersOnly;

  /// No description provided for @manageSalaries.
  ///
  /// In en, this message translates to:
  /// **'Manage Salaries'**
  String get manageSalaries;

  /// No description provided for @manageYourEmployees.
  ///
  /// In en, this message translates to:
  /// **'Manage Your Employees'**
  String get manageYourEmployees;

  /// No description provided for @manualAdditions.
  ///
  /// In en, this message translates to:
  /// **'Manual Additions'**
  String get manualAdditions;

  /// No description provided for @manualDeductions.
  ///
  /// In en, this message translates to:
  /// **'Manual Deductions'**
  String get manualDeductions;

  /// No description provided for @mar.
  ///
  /// In en, this message translates to:
  /// **'Mar'**
  String get mar;

  /// No description provided for @march.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get march;

  /// No description provided for @maxFileImageVideoSize.
  ///
  /// In en, this message translates to:
  /// **'(Max. File /Image / Video size: {sizeinmb} MB)'**
  String maxFileImageVideoSize(int sizeinmb);

  /// No description provided for @maxFileSizeInMB.
  ///
  /// In en, this message translates to:
  /// **'(Max. File size: {sizeInMb} MB)'**
  String maxFileSizeInMB(int sizeInMb);

  /// No description provided for @may.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get may;

  /// No description provided for @medium.
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// No description provided for @members.
  ///
  /// In en, this message translates to:
  /// **'Members'**
  String get members;

  /// No description provided for @membersSelectedOfTotal.
  ///
  /// In en, this message translates to:
  /// **'MEMBERS: {selected} of {total}'**
  String membersSelectedOfTotal(int selected, int total);

  /// No description provided for @middleName.
  ///
  /// In en, this message translates to:
  /// **'Middle Name'**
  String get middleName;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get month;

  /// No description provided for @monthly.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// No description provided for @multiFileLargeError.
  ///
  /// In en, this message translates to:
  /// **'{count} File(s) Were Too Large and Not Included.'**
  String multiFileLargeError(Object count);

  /// No description provided for @myLocation.
  ///
  /// In en, this message translates to:
  /// **'My Location'**
  String get myLocation;

  /// No description provided for @myTasks.
  ///
  /// In en, this message translates to:
  /// **'My Tasks'**
  String get myTasks;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @nameClockedOutTime.
  ///
  /// In en, this message translates to:
  /// **'{name} Clocked Out One Hour Ago'**
  String nameClockedOutTime(String name);

  /// No description provided for @nameCreatedGroup.
  ///
  /// In en, this message translates to:
  /// **'{name} Created Group'**
  String nameCreatedGroup(String name);

  /// No description provided for @nationalId.
  ///
  /// In en, this message translates to:
  /// **'National ID'**
  String get nationalId;

  /// No description provided for @nearbyPlaces.
  ///
  /// In en, this message translates to:
  /// **'Nearby Places'**
  String get nearbyPlaces;

  /// No description provided for @needHelp.
  ///
  /// In en, this message translates to:
  /// **'Need Help'**
  String get needHelp;

  /// No description provided for @netSalary.
  ///
  /// In en, this message translates to:
  /// **'Net Salary'**
  String get netSalary;

  /// No description provided for @newChannel.
  ///
  /// In en, this message translates to:
  /// **'New Channel'**
  String get newChannel;

  /// No description provided for @newChannelFunctionality.
  ///
  /// In en, this message translates to:
  /// **'New Channel Functionality Will be Implemented'**
  String get newChannelFunctionality;

  /// No description provided for @newGroup.
  ///
  /// In en, this message translates to:
  /// **'New Group'**
  String get newGroup;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @news.
  ///
  /// In en, this message translates to:
  /// **'News'**
  String get news;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @noContactsFound.
  ///
  /// In en, this message translates to:
  /// **'No Contacts Found'**
  String get noContactsFound;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No Data Available'**
  String get noData;

  /// No description provided for @noIssues.
  ///
  /// In en, this message translates to:
  /// **'No Issues'**
  String get noIssues;

  /// No description provided for @noProjectFound.
  ///
  /// In en, this message translates to:
  /// **'No Project Found'**
  String get noProjectFound;

  /// No description provided for @noShiftOnDate.
  ///
  /// In en, this message translates to:
  /// **'No Shift Recorded on {date}'**
  String noShiftOnDate(Object date);

  /// No description provided for @noTasksFound.
  ///
  /// In en, this message translates to:
  /// **'No Tasks Found for This Project'**
  String get noTasksFound;

  /// No description provided for @notAvailable.
  ///
  /// In en, this message translates to:
  /// **'Not Available'**
  String get notAvailable;

  /// No description provided for @notEditable.
  ///
  /// In en, this message translates to:
  /// **'Not Editable'**
  String get notEditable;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// No description provided for @notification.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @nov.
  ///
  /// In en, this message translates to:
  /// **'Nov'**
  String get nov;

  /// No description provided for @november.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get november;

  /// No description provided for @numberOfEmployees.
  ///
  /// In en, this message translates to:
  /// **'Number of Employees'**
  String get numberOfEmployees;

  /// No description provided for @oct.
  ///
  /// In en, this message translates to:
  /// **'Oct'**
  String get oct;

  /// No description provided for @october.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get october;

  /// No description provided for @officeAddress.
  ///
  /// In en, this message translates to:
  /// **'Office Address'**
  String get officeAddress;

  /// No description provided for @officeContactInformation.
  ///
  /// In en, this message translates to:
  /// **'Office Contact Information'**
  String get officeContactInformation;

  /// No description provided for @officeDetails.
  ///
  /// In en, this message translates to:
  /// **'Office Details'**
  String get officeDetails;

  /// No description provided for @officeEmployees.
  ///
  /// In en, this message translates to:
  /// **'Office Employees'**
  String get officeEmployees;

  /// No description provided for @officeInformation.
  ///
  /// In en, this message translates to:
  /// **'Office Information'**
  String get officeInformation;

  /// No description provided for @officeLocation.
  ///
  /// In en, this message translates to:
  /// **'Office Location'**
  String get officeLocation;

  /// No description provided for @officeLocationDescription.
  ///
  /// In en, this message translates to:
  /// **'Office Location Description'**
  String get officeLocationDescription;

  /// No description provided for @officeName.
  ///
  /// In en, this message translates to:
  /// **'Office Name'**
  String get officeName;

  /// No description provided for @officeOfficeName.
  ///
  /// In en, this message translates to:
  /// **'Office: {officename}'**
  String officeOfficeName(String officename);

  /// No description provided for @offices.
  ///
  /// In en, this message translates to:
  /// **'Offices'**
  String get offices;

  /// No description provided for @officeType.
  ///
  /// In en, this message translates to:
  /// **'Office Type'**
  String get officeType;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @online.
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get online;

  /// No description provided for @operatingHours.
  ///
  /// In en, this message translates to:
  /// **'Operating Hours'**
  String get operatingHours;

  /// No description provided for @optionalNote.
  ///
  /// In en, this message translates to:
  /// **'Optional Note'**
  String get optionalNote;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @otherDocuments.
  ///
  /// In en, this message translates to:
  /// **'Other Documents'**
  String get otherDocuments;

  /// No description provided for @otherInfo.
  ///
  /// In en, this message translates to:
  /// **'Other Info'**
  String get otherInfo;

  /// No description provided for @overtimeHours.
  ///
  /// In en, this message translates to:
  /// **'{hours} Hours This Month'**
  String overtimeHours(int hours);

  /// No description provided for @overtimeHoursLabel.
  ///
  /// In en, this message translates to:
  /// **'Overtime Hours'**
  String get overtimeHoursLabel;

  /// No description provided for @overtimePay.
  ///
  /// In en, this message translates to:
  /// **'Overtime Pay'**
  String get overtimePay;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @paid.
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get paid;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @payForThisPeriod.
  ///
  /// In en, this message translates to:
  /// **'Pay for this Period'**
  String get payForThisPeriod;

  /// No description provided for @paymentStatus.
  ///
  /// In en, this message translates to:
  /// **'Payment Status'**
  String get paymentStatus;

  /// No description provided for @payNow.
  ///
  /// In en, this message translates to:
  /// **'Pay Now'**
  String get payNow;

  /// No description provided for @payrollSummary.
  ///
  /// In en, this message translates to:
  /// **'Payroll Summary'**
  String get payrollSummary;

  /// No description provided for @payslipConfirmationWarning.
  ///
  /// In en, this message translates to:
  /// **'By Clicking Send, This Payslip Will be Sent To {name}'**
  String payslipConfirmationWarning(Object name);

  /// No description provided for @payslipSuccessNotification.
  ///
  /// In en, this message translates to:
  /// **'Payslip was Sent  to The Employee Successfully'**
  String get payslipSuccessNotification;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @pendingLeaveRequests.
  ///
  /// In en, this message translates to:
  /// **'Pending Leave Requests'**
  String get pendingLeaveRequests;

  /// No description provided for @pendingRequest.
  ///
  /// In en, this message translates to:
  /// **'Pending Request'**
  String get pendingRequest;

  /// No description provided for @pendingRequests.
  ///
  /// In en, this message translates to:
  /// **'Pending Requests'**
  String get pendingRequests;

  /// No description provided for @performance.
  ///
  /// In en, this message translates to:
  /// **'Performance'**
  String get performance;

  /// No description provided for @performanceAndAchievements.
  ///
  /// In en, this message translates to:
  /// **'Performance & Achievements'**
  String get performanceAndAchievements;

  /// No description provided for @performanceBonus.
  ///
  /// In en, this message translates to:
  /// **'Performance Bonus'**
  String get performanceBonus;

  /// No description provided for @permissionsDepartment.
  ///
  /// In en, this message translates to:
  /// **'Permissions Department'**
  String get permissionsDepartment;

  /// No description provided for @personalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// No description provided for @phoneNumberOrEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Phone Number or Email Address'**
  String get phoneNumberOrEmailAddress;

  /// No description provided for @pickerChooseFile.
  ///
  /// In en, this message translates to:
  /// **'Choose a File'**
  String get pickerChooseFile;

  /// No description provided for @pickerChooseFromGallery.
  ///
  /// In en, this message translates to:
  /// **'Choose From Gallery'**
  String get pickerChooseFromGallery;

  /// No description provided for @pickerRecordVideo.
  ///
  /// In en, this message translates to:
  /// **'Record Video'**
  String get pickerRecordVideo;

  /// No description provided for @pickerTakePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get pickerTakePhoto;

  /// No description provided for @pleaseCreateAProject.
  ///
  /// In en, this message translates to:
  /// **'Please Create A Project First To Add Tasks'**
  String get pleaseCreateAProject;

  /// No description provided for @pleaseCreateATask.
  ///
  /// In en, this message translates to:
  /// **'Please Create a Task'**
  String get pleaseCreateATask;

  /// No description provided for @pleaseDrawSignature.
  ///
  /// In en, this message translates to:
  /// **'Please Draw Your Signature'**
  String get pleaseDrawSignature;

  /// No description provided for @pleaseEnterAGroupName.
  ///
  /// In en, this message translates to:
  /// **'Please Enter a Group Name'**
  String get pleaseEnterAGroupName;

  /// No description provided for @pm.
  ///
  /// In en, this message translates to:
  /// **'PM'**
  String get pm;

  /// No description provided for @policies.
  ///
  /// In en, this message translates to:
  /// **'Policies'**
  String get policies;

  /// No description provided for @policy.
  ///
  /// In en, this message translates to:
  /// **'Policy'**
  String get policy;

  /// No description provided for @policyDescription.
  ///
  /// In en, this message translates to:
  /// **'Policy Description'**
  String get policyDescription;

  /// No description provided for @policyName.
  ///
  /// In en, this message translates to:
  /// **'Policy Name'**
  String get policyName;

  /// No description provided for @poor.
  ///
  /// In en, this message translates to:
  /// **'Poor'**
  String get poor;

  /// No description provided for @previousSalaries.
  ///
  /// In en, this message translates to:
  /// **'Previous Salaries'**
  String get previousSalaries;

  /// No description provided for @priority.
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// No description provided for @proceedConfirmation.
  ///
  /// In en, this message translates to:
  /// **'ARE YOU SURE YOU WANT TO PROCEED?'**
  String get proceedConfirmation;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @project.
  ///
  /// In en, this message translates to:
  /// **'Project'**
  String get project;

  /// No description provided for @projects.
  ///
  /// In en, this message translates to:
  /// **'Projects'**
  String get projects;

  /// No description provided for @promoteConfirmationTitle.
  ///
  /// In en, this message translates to:
  /// **'Are You Sure You Want To Promote This Employee To The New Position?'**
  String get promoteConfirmationTitle;

  /// No description provided for @promotion.
  ///
  /// In en, this message translates to:
  /// **'Promotion'**
  String get promotion;

  /// No description provided for @publish.
  ///
  /// In en, this message translates to:
  /// **'Publish'**
  String get publish;

  /// No description provided for @publishedByNAME.
  ///
  /// In en, this message translates to:
  /// **'Published By {name}'**
  String publishedByNAME(String name);

  /// No description provided for @read.
  ///
  /// In en, this message translates to:
  /// **'Read'**
  String get read;

  /// No description provided for @reasonForResignation.
  ///
  /// In en, this message translates to:
  /// **'Reason for Resignation'**
  String get reasonForResignation;

  /// No description provided for @recentlyViewed.
  ///
  /// In en, this message translates to:
  /// **'Recently Viewed'**
  String get recentlyViewed;

  /// No description provided for @record.
  ///
  /// In en, this message translates to:
  /// **'Record'**
  String get record;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @remainingLeaves.
  ///
  /// In en, this message translates to:
  /// **'Remaining Leaves'**
  String get remainingLeaves;

  /// No description provided for @rememberMe.
  ///
  /// In en, this message translates to:
  /// **'Remember Me'**
  String get rememberMe;

  /// No description provided for @reportAProblem.
  ///
  /// In en, this message translates to:
  /// **'Report a Problem'**
  String get reportAProblem;

  /// No description provided for @requestedEdit.
  ///
  /// In en, this message translates to:
  /// **'Requested Edit'**
  String get requestedEdit;

  /// No description provided for @requestForExpenses.
  ///
  /// In en, this message translates to:
  /// **'Request for Expenses'**
  String get requestForExpenses;

  /// No description provided for @requestForLeave.
  ///
  /// In en, this message translates to:
  /// **'Request for Leave'**
  String get requestForLeave;

  /// No description provided for @requestSentSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Request sent successfully'**
  String get requestSentSuccessfully;

  /// No description provided for @requestSubmittedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Your Request Has Been Submitted Successfully'**
  String get requestSubmittedSuccessfully;

  /// No description provided for @requestToChatWithHr.
  ///
  /// In en, this message translates to:
  /// **'Request to Chat with HR'**
  String get requestToChatWithHr;

  /// No description provided for @resendCode.
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get resendCode;

  /// No description provided for @resignation.
  ///
  /// In en, this message translates to:
  /// **'Resignation'**
  String get resignation;

  /// No description provided for @resignationRequest.
  ///
  /// In en, this message translates to:
  /// **'Resignation Request'**
  String get resignationRequest;

  /// No description provided for @resigned.
  ///
  /// In en, this message translates to:
  /// **'Resigned'**
  String get resigned;

  /// No description provided for @restaurant.
  ///
  /// In en, this message translates to:
  /// **'Restaurant'**
  String get restaurant;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @review.
  ///
  /// In en, this message translates to:
  /// **'Review'**
  String get review;

  /// No description provided for @reward.
  ///
  /// In en, this message translates to:
  /// **'Reward'**
  String get reward;

  /// No description provided for @role.
  ///
  /// In en, this message translates to:
  /// **'Role'**
  String get role;

  /// No description provided for @salaries.
  ///
  /// In en, this message translates to:
  /// **'Salaries'**
  String get salaries;

  /// No description provided for @salary.
  ///
  /// In en, this message translates to:
  /// **'Salary'**
  String get salary;

  /// No description provided for @salaryDetails.
  ///
  /// In en, this message translates to:
  /// **'Salary Details'**
  String get salaryDetails;

  /// No description provided for @salaryEdit.
  ///
  /// In en, this message translates to:
  /// **'Salary Edit'**
  String get salaryEdit;

  /// No description provided for @salaryHistory.
  ///
  /// In en, this message translates to:
  /// **'Salary History'**
  String get salaryHistory;

  /// No description provided for @salaryManagement.
  ///
  /// In en, this message translates to:
  /// **'Salary Management'**
  String get salaryManagement;

  /// No description provided for @salaryPaymentConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Salary Payment Confirmation'**
  String get salaryPaymentConfirmation;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @savedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Saved Successfully'**
  String get savedSuccessfully;

  /// No description provided for @saveSend.
  ///
  /// In en, this message translates to:
  /// **'Save & Send'**
  String get saveSend;

  /// No description provided for @schedule.
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// No description provided for @schedules.
  ///
  /// In en, this message translates to:
  /// **'Schedules'**
  String get schedules;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @searchForATask.
  ///
  /// In en, this message translates to:
  /// **'Search for a Task'**
  String get searchForATask;

  /// No description provided for @searching.
  ///
  /// In en, this message translates to:
  /// **'Searching'**
  String get searching;

  /// No description provided for @searchingForATask.
  ///
  /// In en, this message translates to:
  /// **'Searching for a Task'**
  String get searchingForATask;

  /// No description provided for @selectAudience.
  ///
  /// In en, this message translates to:
  /// **'Select Audience'**
  String get selectAudience;

  /// No description provided for @selectClosingTime.
  ///
  /// In en, this message translates to:
  /// **'Select Closing Time'**
  String get selectClosingTime;

  /// No description provided for @selectContacts.
  ///
  /// In en, this message translates to:
  /// **'Select Contacts'**
  String get selectContacts;

  /// No description provided for @selectDocument.
  ///
  /// In en, this message translates to:
  /// **'Select Document'**
  String get selectDocument;

  /// No description provided for @selectDocuments.
  ///
  /// In en, this message translates to:
  /// **'Select Documents'**
  String get selectDocuments;

  /// No description provided for @selectOpeningTime.
  ///
  /// In en, this message translates to:
  /// **'Select Opening Time'**
  String get selectOpeningTime;

  /// No description provided for @selectTimeFrom.
  ///
  /// In en, this message translates to:
  /// **'Select Time From'**
  String get selectTimeFrom;

  /// No description provided for @selectTimeTo.
  ///
  /// In en, this message translates to:
  /// **'Select Time To'**
  String get selectTimeTo;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @sendForApproval.
  ///
  /// In en, this message translates to:
  /// **'Send for Approval'**
  String get sendForApproval;

  /// No description provided for @sendShiftChangeRequest.
  ///
  /// In en, this message translates to:
  /// **'Send Shift Change Request'**
  String get sendShiftChangeRequest;

  /// No description provided for @sendYourCurrentLocation.
  ///
  /// In en, this message translates to:
  /// **'Send Your Current Location'**
  String get sendYourCurrentLocation;

  /// No description provided for @sendYourLiveLocation.
  ///
  /// In en, this message translates to:
  /// **'Send Your Live Location'**
  String get sendYourLiveLocation;

  /// No description provided for @sep.
  ///
  /// In en, this message translates to:
  /// **'Sep'**
  String get sep;

  /// No description provided for @september.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get september;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @shiftDetails.
  ///
  /// In en, this message translates to:
  /// **'Shift Details'**
  String get shiftDetails;

  /// No description provided for @shiftEdit.
  ///
  /// In en, this message translates to:
  /// **'Shift Edit'**
  String get shiftEdit;

  /// No description provided for @shiftEditRequests.
  ///
  /// In en, this message translates to:
  /// **'Shift Edit Requests'**
  String get shiftEditRequests;

  /// No description provided for @shiftHistory.
  ///
  /// In en, this message translates to:
  /// **'Shift History'**
  String get shiftHistory;

  /// No description provided for @shiftWorkingTime.
  ///
  /// In en, this message translates to:
  /// **'Shift Working Time'**
  String get shiftWorkingTime;

  /// No description provided for @showAll.
  ///
  /// In en, this message translates to:
  /// **'Show All'**
  String get showAll;

  /// No description provided for @sinceStartOfYear.
  ///
  /// In en, this message translates to:
  /// **'Since Start of Year'**
  String get sinceStartOfYear;

  /// No description provided for @singleFileLargeError.
  ///
  /// In en, this message translates to:
  /// **'The Selected File Is Too Large (Max {size}MB).'**
  String singleFileLargeError(Object size);

  /// No description provided for @sizeKB.
  ///
  /// In en, this message translates to:
  /// **'{size} KB'**
  String sizeKB(Object size);

  /// No description provided for @sizeMB.
  ///
  /// In en, this message translates to:
  /// **'{size} MB'**
  String sizeMB(Object size);

  /// No description provided for @someErrorOcurred.
  ///
  /// In en, this message translates to:
  /// **'Some Error Occurred'**
  String get someErrorOcurred;

  /// No description provided for @sorrySomeErrorOccured.
  ///
  /// In en, this message translates to:
  /// **'Sorry, Some Error Occured'**
  String get sorrySomeErrorOccured;

  /// No description provided for @startBreak.
  ///
  /// In en, this message translates to:
  /// **'Start Break'**
  String get startBreak;

  /// No description provided for @startDate.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// No description provided for @startWork.
  ///
  /// In en, this message translates to:
  /// **'Start Work'**
  String get startWork;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @statusUpdate.
  ///
  /// In en, this message translates to:
  /// **'Status Update'**
  String get statusUpdate;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @submitResignation.
  ///
  /// In en, this message translates to:
  /// **'Submit Resignation'**
  String get submitResignation;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @successfullyCompletedShift.
  ///
  /// In en, this message translates to:
  /// **'You Have Successfully Completed Your Shift.'**
  String get successfullyCompletedShift;

  /// No description provided for @successfullyPaid.
  ///
  /// In en, this message translates to:
  /// **'Paid Successfully'**
  String get successfullyPaid;

  /// No description provided for @sundayToFriday.
  ///
  /// In en, this message translates to:
  /// **'Sunday to Friday'**
  String get sundayToFriday;

  /// No description provided for @support.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// No description provided for @systemGeneratedAdditions.
  ///
  /// In en, this message translates to:
  /// **'System-Generated Additions'**
  String get systemGeneratedAdditions;

  /// No description provided for @systemGeneratedDeductions.
  ///
  /// In en, this message translates to:
  /// **'System-Generated Deductions'**
  String get systemGeneratedDeductions;

  /// No description provided for @taskAddedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Task Added Successfully'**
  String get taskAddedSuccessfully;

  /// No description provided for @taskCompleted.
  ///
  /// In en, this message translates to:
  /// **'Task Completed'**
  String get taskCompleted;

  /// No description provided for @taskDescription.
  ///
  /// In en, this message translates to:
  /// **'Task Description'**
  String get taskDescription;

  /// No description provided for @taskDetails.
  ///
  /// In en, this message translates to:
  /// **'Task Details'**
  String get taskDetails;

  /// No description provided for @tasks.
  ///
  /// In en, this message translates to:
  /// **'Tasks'**
  String get tasks;

  /// No description provided for @taxes.
  ///
  /// In en, this message translates to:
  /// **'Taxes'**
  String get taxes;

  /// No description provided for @team.
  ///
  /// In en, this message translates to:
  /// **'Team'**
  String get team;

  /// No description provided for @teams.
  ///
  /// In en, this message translates to:
  /// **'Teams'**
  String get teams;

  /// No description provided for @termsOfUse.
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get termsOfUse;

  /// No description provided for @theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// No description provided for @themeMode.
  ///
  /// In en, this message translates to:
  /// **'Theme Mode'**
  String get themeMode;

  /// No description provided for @theNewPosition.
  ///
  /// In en, this message translates to:
  /// **'The New Position'**
  String get theNewPosition;

  /// No description provided for @theNewSalary.
  ///
  /// In en, this message translates to:
  /// **'The New Salary'**
  String get theNewSalary;

  /// No description provided for @thereAreNoNotifications.
  ///
  /// In en, this message translates to:
  /// **'There Are No Notifications'**
  String get thereAreNoNotifications;

  /// No description provided for @theReason.
  ///
  /// In en, this message translates to:
  /// **'The Reason'**
  String get theReason;

  /// No description provided for @thisMonth.
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonth;

  /// No description provided for @thisMonthFilter.
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonthFilter;

  /// No description provided for @thisMonthsPayroll.
  ///
  /// In en, this message translates to:
  /// **'This Month\'s Payroll'**
  String get thisMonthsPayroll;

  /// No description provided for @thisMonthsSalary.
  ///
  /// In en, this message translates to:
  /// **'This Month\'s Salary'**
  String get thisMonthsSalary;

  /// No description provided for @thisMonthsSalaryDetails.
  ///
  /// In en, this message translates to:
  /// **'This Month\'s Salary Details'**
  String get thisMonthsSalaryDetails;

  /// No description provided for @thisWeek.
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// No description provided for @timeClock.
  ///
  /// In en, this message translates to:
  /// **'Time Clock'**
  String get timeClock;

  /// No description provided for @timeOffRequests.
  ///
  /// In en, this message translates to:
  /// **'Leave Requests'**
  String get timeOffRequests;

  /// No description provided for @timeOffTaken.
  ///
  /// In en, this message translates to:
  /// **'Leaves Taken'**
  String get timeOffTaken;

  /// No description provided for @timesheet.
  ///
  /// In en, this message translates to:
  /// **'Timesheet'**
  String get timesheet;

  /// No description provided for @timeZone.
  ///
  /// In en, this message translates to:
  /// **'Time Zone'**
  String get timeZone;

  /// No description provided for @timingsError.
  ///
  /// In en, this message translates to:
  /// **'The Timings Look Incorrect'**
  String get timingsError;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @totalAdditions.
  ///
  /// In en, this message translates to:
  /// **'Total Additions'**
  String get totalAdditions;

  /// No description provided for @totalDeductions.
  ///
  /// In en, this message translates to:
  /// **'Total Deductions'**
  String get totalDeductions;

  /// No description provided for @totalEmployees.
  ///
  /// In en, this message translates to:
  /// **'Total Employees'**
  String get totalEmployees;

  /// No description provided for @totalHours.
  ///
  /// In en, this message translates to:
  /// **'Total Hours'**
  String get totalHours;

  /// No description provided for @totalShiftTime.
  ///
  /// In en, this message translates to:
  /// **'Total Shift Time'**
  String get totalShiftTime;

  /// No description provided for @typing.
  ///
  /// In en, this message translates to:
  /// **'Typing...'**
  String get typing;

  /// No description provided for @unavailable.
  ///
  /// In en, this message translates to:
  /// **'Unavailable'**
  String get unavailable;

  /// No description provided for @unionFees.
  ///
  /// In en, this message translates to:
  /// **'Union Fees'**
  String get unionFees;

  /// No description provided for @unpaid.
  ///
  /// In en, this message translates to:
  /// **'Unpaid'**
  String get unpaid;

  /// No description provided for @unread.
  ///
  /// In en, this message translates to:
  /// **'Unread'**
  String get unread;

  /// No description provided for @updatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Updated Successfully'**
  String get updatedSuccessfully;

  /// No description provided for @updatePolicy.
  ///
  /// In en, this message translates to:
  /// **'Update Policy'**
  String get updatePolicy;

  /// No description provided for @uploadDocuments.
  ///
  /// In en, this message translates to:
  /// **'Upload Documents'**
  String get uploadDocuments;

  /// No description provided for @valueHours.
  ///
  /// In en, this message translates to:
  /// **'{hours} Hours'**
  String valueHours(num hours);

  /// No description provided for @verificationCode.
  ///
  /// In en, this message translates to:
  /// **'Please Enter The Verification Code Sent To'**
  String get verificationCode;

  /// No description provided for @viewActivity.
  ///
  /// In en, this message translates to:
  /// **'View Activity'**
  String get viewActivity;

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No description provided for @waitingForManagerApproval.
  ///
  /// In en, this message translates to:
  /// **'Waiting for Manager Approval'**
  String get waitingForManagerApproval;

  /// No description provided for @website.
  ///
  /// In en, this message translates to:
  /// **'Website'**
  String get website;

  /// No description provided for @weekly.
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get weekly;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// No description provided for @workDayYesNo.
  ///
  /// In en, this message translates to:
  /// **'Work Day (Yes/No)'**
  String get workDayYesNo;

  /// No description provided for @workingHours.
  ///
  /// In en, this message translates to:
  /// **'Working Hours'**
  String get workingHours;

  /// No description provided for @workingSchedule.
  ///
  /// In en, this message translates to:
  /// **'Working Schedule'**
  String get workingSchedule;

  /// No description provided for @workspace.
  ///
  /// In en, this message translates to:
  /// **'Workspace'**
  String get workspace;

  /// No description provided for @writeAMessage.
  ///
  /// In en, this message translates to:
  /// **'Write a Message...'**
  String get writeAMessage;

  /// No description provided for @writeANoteForTheEmployee.
  ///
  /// In en, this message translates to:
  /// **'Write a Note for The Employee'**
  String get writeANoteForTheEmployee;

  /// No description provided for @writeFeedback.
  ///
  /// In en, this message translates to:
  /// **'Write Feedback'**
  String get writeFeedback;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'Year'**
  String get year;

  /// No description provided for @yearly.
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @you.
  ///
  /// In en, this message translates to:
  /// **'You'**
  String get you;

  /// No description provided for @youCreatedGroup.
  ///
  /// In en, this message translates to:
  /// **'{name} Created This Group'**
  String youCreatedGroup(String name);

  /// No description provided for @zipCode.
  ///
  /// In en, this message translates to:
  /// **'Zip Code'**
  String get zipCode;

  /// No description provided for @halfDay.
  ///
  /// In en, this message translates to:
  /// **'Half Day'**
  String get halfDay;

  /// No description provided for @deadline.
  ///
  /// In en, this message translates to:
  /// **'Deadline'**
  String get deadline;

  /// No description provided for @reply.
  ///
  /// In en, this message translates to:
  /// **'Reply'**
  String get reply;

  /// No description provided for @male.
  ///
  /// In en, this message translates to:
  /// **'Male'**
  String get male;

  /// No description provided for @female.
  ///
  /// In en, this message translates to:
  /// **'Female'**
  String get female;

  /// No description provided for @gender.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get gender;

  /// No description provided for @selectAssignees.
  ///
  /// In en, this message translates to:
  /// **'Select assignees'**
  String get selectAssignees;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get day;

  /// No description provided for @week.
  ///
  /// In en, this message translates to:
  /// **'Week'**
  String get week;

  /// No description provided for @scheduleATask.
  ///
  /// In en, this message translates to:
  /// **'Schedule a Task'**
  String get scheduleATask;

  /// No description provided for @scheduleAShift.
  ///
  /// In en, this message translates to:
  /// **'Schedule a Shift'**
  String get scheduleAShift;

  /// No description provided for @addHoliday.
  ///
  /// In en, this message translates to:
  /// **'Add Holiday'**
  String get addHoliday;

  /// No description provided for @addEvent.
  ///
  /// In en, this message translates to:
  /// **'Add Event'**
  String get addEvent;

  /// No description provided for @editEvent.
  ///
  /// In en, this message translates to:
  /// **'Edit Event'**
  String get editEvent;

  /// No description provided for @enterEventTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter Event Title'**
  String get enterEventTitle;

  /// No description provided for @enterEventDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter Event Description'**
  String get enterEventDescription;

  /// No description provided for @pleaseEnterATitle.
  ///
  /// In en, this message translates to:
  /// **'Please Enter a Title'**
  String get pleaseEnterATitle;

  /// No description provided for @endTimeMustBeAfterStartTime.
  ///
  /// In en, this message translates to:
  /// **'End Time Must be After Start Time'**
  String get endTimeMustBeAfterStartTime;

  /// No description provided for @startTime.
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// No description provided for @endTime.
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// No description provided for @endDate.
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @event.
  ///
  /// In en, this message translates to:
  /// **'Event'**
  String get event;

  /// No description provided for @task.
  ///
  /// In en, this message translates to:
  /// **'Task'**
  String get task;

  /// No description provided for @shift.
  ///
  /// In en, this message translates to:
  /// **'Shift'**
  String get shift;

  /// No description provided for @holiday.
  ///
  /// In en, this message translates to:
  /// **'Holiday'**
  String get holiday;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en', 'ku'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
    case 'ku':
      return AppLocalizationsKu();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
