{"@enterVerificationCode": {"placeholders": {"phonenumber": {"type": "String"}}}, "@groupMembersCount": {"placeholders": {"membercount": {"type": "int"}}}, "@lastSeenOn": {"placeholders": {"dateTime": {"type": "String"}}}, "@maxFileImageVideoSize": {"placeholders": {"sizeinmb": {"type": "int"}}}, "@maxFileSizeInMB": {"placeholders": {"sizeInMb": {"type": "int"}}}, "@membersSelectedOfTotal": {"placeholders": {"selected": {"type": "int"}, "total": {"type": "int"}}}, "@nameClockedOutTime": {"placeholders": {"name": {"type": "String"}}}, "@officeOfficeName": {"placeholders": {"officename": {"type": "String"}}}, "@nameCreatedGroup": {"placeholders": {"name": {"type": "String"}}}, "@overtimeHours": {"placeholders": {"hours": {"type": "int"}}}, "@publishedByNAME": {"placeholders": {"name": {"type": "String"}}}, "@valueHours": {"placeholders": {"hours": {"type": "num"}}}, "@youCreatedGroup": {"placeholders": {"name": {"type": "String"}}}}