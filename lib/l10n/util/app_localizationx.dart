import 'package:ako_basma/l10n/app_localizations.dart';

extension AppLocalizationsX on AppLocalizations {
  String? translatedLabel(String id) {
    final translationMap = {
      'paid': paid,
      'approved': approved,
      'success': success,
      'completed': completed,
      'rejected': rejected,
      'failed': failed,
      'unpaid': unpaid,
      'pending': pending,
      'confirmed': confirmed,
      'reject': reject,
      'error': error,
      'cancel': cancel,
      'in progress': inProgress,
      'approve': approve,
    };
    return translationMap[(id).toLowerCase()];
  }
}
