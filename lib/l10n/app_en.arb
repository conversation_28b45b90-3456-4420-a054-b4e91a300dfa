{"about": "About", "aboutUs": "About Us", "absenceDeduction": "Absence Deduction", "accountInfo": "Account Information", "active": "Active", "activity": "Activity", "add": "Add", "addAComment": "Add a Comment", "addAnnouncement": "Add Announcement", "addATask": "Add a Task", "addBreak": "Add Break", "addComment": "Add Comment", "addDepartment": "Add Department", "addedSuccessfully": "Added Successfully", "addEmployee": "Add Employee", "additionalNotes": "Additional Notes", "addLeave": "Add Leave", "addNewOffice": "Add New Office", "addNote": "Add a Note", "addPolicy": "Add Policy", "address": "Address", "addressInformation": "Address Information", "addShift": "Add Shift", "addTask": "Add Task", "adminContactMessage": "If You Wish to Edit, Please Contact The Administration", "ai": "AI", "aiCapablityLine1": "Would You Like Me to Organise a Task?", "aiCapablityLine2": "Look Up An Employee’s Information?", "aiCapablityLine3": "Or Update Something For You?", "aiCapablityLine4": "Just Tell Me What You Need, I Am Here To Help!", "aiChatGreeting": "Hello and Welcome to <PERSON>ko <PERSON>!", "aiGreetingText": "How Can I Help You Today?", "akoBasma": "<PERSON><PERSON>", "all": "All", "allDay": "All Day", "allEmployees": "All Employees", "allOffices": "All Offices", "allowances": "Allowances", "allProjects": "All Projects", "allTime": "All Time", "am": "AM", "amount": "Amount", "amountIqd": "Amount IQD", "amountOfDecrease": "Amount of Decrease", "amountOfIncrease": "Amount of Increase", "amountOfIncreaseOrDecrease": "Amount of Increase or Decrease", "announcement": "Announcement", "approve": "Approve", "approved": "Approved", "approvedBy": "Approved By", "approveTask": "Approve Task", "approximateToDistanceMetres": "Approximately {distance} Metres Away", "apr": "Apr", "april": "April", "arabic": "Arabic", "archive": "Archive", "archiveEmployee": "Archive Employee", "archiveEmployeeWarning": "If The Employee Is Archived, Their Account Will Be Deactivated!", "archiveEmployeeWarningSubtitle": "ARE YOU SURE YOU WANT TO ARCHIVE THIS EMPLOYEE?", "archives": "Archives", "asiaBaghdad": "Asia/Baghdad", "assign": "Assign", "assignee": "Assignee", "assignToMe": "Assign to Me", "attachment": "Attachment", "attendance": "Attendance", "aug": "Aug", "august": "August", "availableOnWeekends": "Available on Weekends", "back": "Back", "backlog": "Backlog", "basicSalary": "Basic Salary", "branch": "Branch", "breakTime": "Break Time:", "breakTimeLabel": "Break Time", "call": "Call", "camera": "Camera", "cancel": "Cancel", "canEdit": "Can Edit", "canIGetMoreInfo": "Can I Get More Information?", "canView": "Can View", "changesSavedSuccessfully": "Changes Saved Successfully", "chat": "Messages", "chatRequestConfirmation": "ARE YOU SURE YOU WANT TO SEND THIS REQUEST?", "chatWithAi": "Chat with AI", "chatWithHr": "Chat with HR", "checkedOutHelpText": "Checked Out", "checkInTime": "Check-In Time", "checkOutTime": "Check-Out Time", "checkYourMessages": "Check your messages", "city": "City", "clear": "Clear", "clearSelection": "Clear Selection", "clickToUpload": "Click to Upload", "clockIn": "Clock In", "clockInTime": "Clock-In Time", "clockOut": "Clock Out", "clockOutTime": "Clock-Out Time", "close": "Close", "closingTimeMustBeAfterOpeningTime": "Closing Time Must be After Opening Time.", "comments": "Comments", "companyDirector": "Company Director", "companyInformation": "Company Information", "companyJobNumberStart": "Company Employee Number Sequence", "companyName": "Company Name", "companyNews": "Company News", "companyPolicy": "Company Policy", "completed": "Completed", "completedTasks": "Completed Tasks", "confirm": "Confirm", "confirmed": "Confirmed", "confirmHours": "Confirm Hours", "confirmPassword": "Confirm Password", "contact": "Contacts", "contactNumber": "Contact Number", "contractType": "Contract Type", "country": "Country", "create": "Create", "createAProject": "Create a Project", "createATask": "Create a Task", "createProject": "Create Project", "createTask": "Create Task", "critical": "Critical", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currentShift": "Current Shift", "customDateRange": "Custom Date Range", "daily": "Daily", "dailyShifts": "Daily Shifts", "dark": "Dark", "date": "Date", "dateOfBirth": "Date of Birth", "dateOfJoining": "Date of Joining", "dateOfResignation": "Date of Resignation", "days": "Days", "dec": "Dec", "december": "December", "decrease": "Decrease", "deductions": "Deductions", "delay": "Delay:", "delete": "Delete", "deleteEmployee": "Delete Employee", "deleteEmployeeWarning": "This Action Cannot be Undone. All Employee Data and Related Information Will be Removed From The System", "delivered": "Delivered", "department": "Department", "departmentInformation": "Department Information", "departmentName": "Department Name", "description": "Description", "design": "Design", "disciplinaryFine": "Disciplinary Fine", "document": "Document", "documents": "Documents", "downloadAll": "Download All", "draft": "Draft", "drawSignature": "Draw Signature", "dueDate": "Due Date", "duration": "Duration", "edit": "Edit", "editAccountInfo": "Edit Account Information", "editAddress": "Edit Address", "editAnnouncement": "Edit Announcement", "editGroup": "Edit Group", "editInfo": "Edit Info", "editMembers": "Edit Members", "editName": "Edit Name", "editPolicies": "Edit Policies", "editSalary": "<PERSON>", "email": "Email", "emailAddress": "Email Address", "emp": "Employee No", "employeeAddedSuccessfully": "Employee Added Successfully", "employeeIdCopySuccessMsg": "Employee No Copied to Clipboard", "employeeManagement": "Employee Management", "employeeOfTheMonth": "Employee of The Month", "employeeRatingQuestionTitle": "How Do You Rate This Employee?", "employees": "Employees", "employeesOfTheMonth": "Employees of The Month", "employeesWorkingToday": "Employees Working Today", "employeeTimeOff": "Employee Time-Off", "employeeTracking": "Employee Tracking", "employementType": "Employment Type", "endChat": "End <PERSON>", "endShift": "End Shift", "endWork": "End Work", "english": "English", "enterGroupDescription": "Enter Group Description", "enterGroupName": "Enter Group Name", "enterVerificationCode": "Please Enter The Verification Code Sent To {phonenumber}", "@enterVerificationCode": {"placeholders": {"phonenumber": {"type": "String"}}}, "enterYourNoteHere": "Enter Your Note Here", "error": "Error", "expenseRequests": "Expense Requests", "expenses": "Expenses", "failed": "Failed", "failedToSelectImage": "Failed to Select Image, Please Try Again.", "feb": "Feb", "february": "February", "feedback": "<PERSON><PERSON><PERSON>", "fileSelected": "File Selected", "filter": "Filter", "firstName": "First Name", "fixed": "Fixed", "forgotYourPassword": "Forgot Your Password?", "from": "From", "fullAccess": "Full Access", "gallery": "Gallery", "generalSettings": "General Settings", "generatePayslip": "Generate Payslip", "good": "Good", "gross": "Gross", "grossSalary": "Gross Salary", "groupDescription": "Group Description", "groupMembersCount": "{membercount} Members", "@groupMembersCount": {"placeholders": {"membercount": {"type": "int"}}}, "groupName": "Group Name", "headquarters": "Headquarters", "helloHowCanIHelpYouToday": "Hello! How Can I Help You Today?", "helloWorld": "Hello World!", "high": "High", "holidays": "Holidays", "home": "Home", "hourly": "Hourly", "hourlyRate": "Hourly Rate", "hours": "Hours", "hoursForToday": "{hours} Hours for Today", "hoursLetter": "{hours} H", "hrApprovalMessage": "Your Request Has Been Approved.", "hrManager": "HR Manager", "ifYouClockOutNow": "If You Clock Out Now, You'll be Clock Out at", "increase": "Increase", "inProgress": "In Progress", "iqd": "IQD", "iraq": "Iraq", "jan": "Jan", "january": "January", "jobInformation": "Job Information", "jobTitle": "Job Title", "joined": "Joined", "jul": "Jul", "july": "July", "jun": "Jun", "june": "June", "kurdish": "Kurdish", "language": "Language", "lastName": "Last Name", "lastSeenOn": "Last Seen on {dateTime}", "@lastSeenOn": {"placeholders": {"dateTime": {"type": "String"}}}, "lastSeenOnLastseen": "Last Seen on {lastSeen}", "lastWorkingDay": "Last Working Day", "leave": "Leave", "leaveFrom": "Leave From", "leaveManagement": "Leave Management", "leavesHistory": "Leaves History", "leavesTaken": "Leaves Taken", "leaveTo": "Leave To", "leaveType": "Leave Type", "light": "Light", "location": "Location", "logIn": "Log In", "logo": "Logo", "logout": "Log Out", "logOut": "Log Out", "logoutConfirmation": "Are You Sure You Want to Log Out?", "low": "Low", "manageDepartment": "Manage Department", "manageEmployees": "Manage Employees", "management": "Management", "manageOffices": "Manage Offices", "manageProject": "Manage Project", "manager": "Manager", "managerInformation": "Manager Information", "managersFeedback": "Manager's <PERSON><PERSON><PERSON>", "managersOnly": "Managers Only", "manageSalaries": "Manage Salaries", "manageYourEmployees": "Manage Your Employees", "manualAdditions": "Manual Additions", "manualDeductions": "Manual Deductions", "mar": "Mar", "march": "March", "maxFileImageVideoSize": "(Max. File /Image / Video size: {sizeinmb} MB)", "@maxFileImageVideoSize": {"placeholders": {"sizeinmb": {"type": "int"}}}, "maxFileSizeInMB": "(Max. File size: {sizeInMb} MB)", "@maxFileSizeInMB": {"placeholders": {"sizeInMb": {"type": "int"}}}, "may": "May", "medium": "Medium", "members": "Members", "membersSelectedOfTotal": "MEMBERS: {selected} of {total}", "@membersSelectedOfTotal": {"placeholders": {"selected": {"type": "int"}, "total": {"type": "int"}}}, "middleName": "Middle Name", "month": "Month", "monthly": "Monthly", "multiFileLargeError": "{count} File(s) Were Too Large and Not Included.", "myLocation": "My Location", "myTasks": "My Tasks", "name": "Name", "nameClockedOutTime": "{name} Clocked Out One Hour Ago", "@nameClockedOutTime": {"placeholders": {"name": {"type": "String"}}}, "nameCreatedGroup": "{name} Created Group", "@nameCreatedGroup": {"placeholders": {"name": {"type": "String"}}}, "nationalId": "National ID", "nearbyPlaces": "Nearby Places", "needHelp": "Need Help", "netSalary": "Net Salary", "newChannel": "New Channel", "newChannelFunctionality": "New Channel Functionality Will be Implemented", "newGroup": "New Group", "newPassword": "New Password", "news": "News", "next": "Next", "no": "No", "noContactsFound": "No Contacts Found", "noData": "No Data Available", "noIssues": "No Issues", "noProjectFound": "No Project Found", "noShiftOnDate": "No Shift Recorded on {date}", "noTasksFound": "No Tasks Found for This Project", "notAvailable": "Not Available", "notEditable": "Not Editable", "notes": "Notes", "notification": "Notification", "notifications": "Notifications", "nov": "Nov", "november": "November", "numberOfEmployees": "Number of Employees", "oct": "Oct", "october": "October", "officeAddress": "Office Address", "officeContactInformation": "Office Contact Information", "officeDetails": "Office Details", "officeEmployees": "Office Employees", "officeInformation": "Office Information", "officeLocation": "Office Location", "officeLocationDescription": "Office Location Description", "officeName": "Office Name", "officeOfficeName": "Office: {officename}", "@officeOfficeName": {"placeholders": {"officename": {"type": "String"}}}, "offices": "Offices", "officeType": "Office Type", "ok": "OK", "online": "Online", "operatingHours": "Operating Hours", "optionalNote": "Optional Note", "other": "Other", "otherDocuments": "Other Documents", "otherInfo": "Other Info", "overtimeHours": "{hours} Hours This Month", "@overtimeHours": {"placeholders": {"hours": {"type": "int"}}}, "overtimeHoursLabel": "Overtime Hours", "overtimePay": "Overtime Pay", "overview": "Overview", "paid": "Paid", "password": "Password", "payForThisPeriod": "Pay for this Period", "paymentStatus": "Payment Status", "payNow": "Pay Now", "payrollSummary": "Payroll Summary", "payslipConfirmationWarning": "By Clicking Send, This Payslip Will be Sent To {name}", "payslipSuccessNotification": "Payslip was Sent  to The Employee Successfully", "pending": "Pending", "pendingLeaveRequests": "Pending Leave Requests", "pendingRequest": "Pending Request", "pendingRequests": "Pending Requests", "performance": "Performance", "performanceAndAchievements": "Performance & Achievements", "performanceBonus": "Performance Bonus", "permissionsDepartment": "Permissions Department", "personalInformation": "Personal Information", "phone": "Phone", "phoneNumber": "Phone Number", "phoneNumberOrEmailAddress": "Phone Number or Email Address", "pickerChooseFile": "Choose a File", "pickerChooseFromGallery": "<PERSON><PERSON> From Gallery", "pickerRecordVideo": "Record Video", "pickerTakePhoto": "Take Photo", "pleaseCreateAProject": "Please Create A Project First To Add Tasks", "pleaseCreateATask": "Please Create a Task", "pleaseDrawSignature": "Please Draw Your Signature", "pleaseEnterAGroupName": "Please Enter a Group Name", "pm": "PM", "policies": "Policies", "policy": "Policy", "policyDescription": "Policy Description", "policyName": "Policy Name", "poor": "Poor", "previousSalaries": "Previous Salaries", "priority": "Priority", "proceedConfirmation": "ARE YOU SURE YOU WANT TO PROCEED?", "profile": "Profile", "project": "Project", "projects": "Projects", "promoteConfirmationTitle": "Are You Sure You Want To Promote This Employee To The New Position?", "promotion": "Promotion", "publish": "Publish", "publishedByNAME": "Published By {name}", "@publishedByNAME": {"placeholders": {"name": {"type": "String"}}}, "read": "Read", "reasonForResignation": "Reason for Resignation", "recentlyViewed": "Recently Viewed", "record": "Record", "reject": "Reject", "rejected": "Rejected", "remainingLeaves": "Remaining Leaves", "rememberMe": "Remember Me", "reportAProblem": "Report a Problem", "requestedEdit": "Requested Edit", "requestForExpenses": "Request for Expenses", "requestForLeave": "Request for Leave", "requestSentSuccessfully": "Request sent successfully", "requestSubmittedSuccessfully": "Your Request Has Been Submitted Successfully", "requestToChatWithHr": "Request to Chat with HR", "resendCode": "Resend Code", "resignation": "Resignation", "resignationRequest": "Resignation Request", "resigned": "Resigned", "restaurant": "Restaurant", "retry": "Retry", "review": "Review", "reward": "<PERSON><PERSON>", "role": "Role", "salaries": "Salaries", "salary": "Salary", "salaryDetails": "Salary Details", "salaryEdit": "Salary Edit", "salaryHistory": "Salary History", "salaryManagement": "Salary Management", "salaryPaymentConfirmation": "Salary Payment Confirmation", "save": "Save", "savedSuccessfully": "Saved Successfully", "saveSend": "Save & Send", "schedule": "Schedule", "schedules": "Schedules", "search": "Search", "searchForATask": "Search for a Task", "searching": "Searching", "searchingForATask": "Searching for a Task", "selectAudience": "Select Audience", "selectClosingTime": "Select Closing Time", "selectContacts": "Select Contacts", "selectDocument": "Select Document", "selectDocuments": "Select Documents", "selectOpeningTime": "Select Opening Time", "selectTimeFrom": "Select Time From", "selectTimeTo": "Select Time To", "send": "Send", "sendForApproval": "Send for A<PERSON>roval", "sendShiftChangeRequest": "Send Shift Change Request", "sendYourCurrentLocation": "Send Your Current Location", "sendYourLiveLocation": "Send Your Live Location", "sep": "Sep", "september": "September", "settings": "Settings", "shiftDetails": "Shift Details", "shiftEdit": "Shift Edit", "shiftEditRequests": "Shift Edit Requests", "shiftHistory": "Shift History", "shiftWorkingTime": "Shift Working Time", "showAll": "Show All", "sinceStartOfYear": "Since Start of Year", "singleFileLargeError": "The Selected File Is Too Large (Max {size}MB).", "sizeKB": "{size} KB", "sizeMB": "{size} MB", "someErrorOcurred": "Some Error Occurred", "sorrySomeErrorOccured": "Sorry, Some Error Occured", "startBreak": "Start Break", "startDate": "Start Date", "startWork": "Start Work", "status": "Status", "statusUpdate": "Status Update", "submit": "Submit", "submitResignation": "Submit Resignation", "success": "Success", "successfullyCompletedShift": "You Have Successfully Completed Your Shift.", "successfullyPaid": "Paid Successfully", "sundayToFriday": "Sunday to Friday", "support": "Support", "systemGeneratedAdditions": "System-Generated Additions", "systemGeneratedDeductions": "System-Generated Deductions", "taskAddedSuccessfully": "Task Added Successfully", "taskCompleted": "Task Completed", "taskDescription": "Task Description", "taskDetails": "Task Details", "tasks": "Tasks", "taxes": "Taxes", "team": "Team", "teams": "Teams", "termsOfUse": "Terms and Conditions", "theme": "Theme", "themeMode": "Theme Mode", "theNewPosition": "The New Position", "theNewSalary": "The New Salary", "thereAreNoNotifications": "There Are No Notifications", "theReason": "The Reason", "thisMonth": "This Month", "thisMonthFilter": "This Month", "thisMonthsPayroll": "This Month's Payroll", "thisMonthsSalary": "This Month's Salary", "thisMonthsSalaryDetails": "This Month's Salary Details", "thisWeek": "This Week", "timeClock": "Time Clock", "timeOffRequests": "Leave Requests", "timeOffTaken": "Leaves Taken", "timesheet": "Timesheet", "timeZone": "Time Zone", "timingsError": "The Timings Look Incorrect", "title": "Title", "to": "To", "today": "Today", "totalAdditions": "Total Additions", "totalDeductions": "Total Deductions", "totalEmployees": "Total Employees", "totalHours": "Total Hours", "totalShiftTime": "Total Shift Time", "typing": "Typing...", "unavailable": "Unavailable", "unionFees": "Union Fees", "unpaid": "Unpaid", "unread": "Unread", "updatedSuccessfully": "Updated Successfully", "updatePolicy": "Update Policy", "uploadDocuments": "Upload Documents", "valueHours": "{hours} Hours", "@valueHours": {"placeholders": {"hours": {"type": "num"}}}, "verificationCode": "Please Enter The Verification Code Sent To", "viewActivity": "View Activity", "viewAll": "View All", "waitingForManagerApproval": "Waiting for Manager <PERSON><PERSON><PERSON><PERSON>", "website": "Website", "weekly": "Weekly", "welcome": "Welcome", "workDayYesNo": "Work Day (Yes/No)", "workingHours": "Working Hours", "workingSchedule": "Working Schedule", "workspace": "Workspace", "writeAMessage": "Write a Message...", "writeANoteForTheEmployee": "Write a Note for The Employee", "writeFeedback": "Write Feedback", "year": "Year", "yearly": "Yearly", "yes": "Yes", "yesterday": "Yesterday", "you": "You", "youCreatedGroup": "{name} Created This Group", "@youCreatedGroup": {"placeholders": {"name": {"type": "String"}}}, "zipCode": "Zip Code", "halfDay": "Half Day", "deadline": "Deadline", "reply": "Reply", "male": "Male", "female": "Female", "gender": "Gender", "selectAssignees": "Select assignees", "day": "Day", "week": "Week", "scheduleATask": "Schedule a Task", "scheduleAShift": "Schedule a Shift", "addHoliday": "Add Holiday", "addEvent": "Add Event", "editEvent": "Edit Event", "enterEventTitle": "Enter Event Title", "enterEventDescription": "Enter Event Description", "pleaseEnterATitle": "Please Enter a Title", "endTimeMustBeAfterStartTime": "End Time Must be After Start Time", "startTime": "Start Time", "endTime": "End Time", "endDate": "End Date", "type": "Type", "event": "Event", "task": "Task", "shift": "Shift", "holiday": "Holiday"}