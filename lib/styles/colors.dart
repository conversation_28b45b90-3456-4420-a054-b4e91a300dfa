import 'package:flutter/material.dart';

const customColorMap = {
  'blue100': (Colors.blue, Colors.black),
};

ColorScheme colors(BuildContext context) => Theme.of(context).colorScheme;
TextTheme textStyles(BuildContext context) => Theme.of(context).textTheme;
Color customColors(String tag, BuildContext context) =>
    (Theme.of(context).brightness == Brightness.light
        ? customColorMap[tag]?.$1
        : customColorMap[tag]?.$2) ??
    Colors.transparent;

class DesignColors {
  // Shared Colors (used in both themes)
  static const Color success = Color(0xFF16B364);
  static const Color successContainerLight = Color(0xFFE6F2EC);
  static const Color successContainerDark = Color(0xFF03150C);
  static const Color warning = Color(0xFFFFAB2D);
  static const Color warningContainerLight = Color(0xFFFFF7EA);
  static const Color warningContainerDark = Color(0xFF1F1505);
  static const Color error = Color(0xFFD92932);
  static const Color errorContainerLight = Color(0xFFFEEEEF);
  static const Color errorContainerDark = Color(0xFF1A0506);
  static const Color info = Color(0xFF0094FF);
  static const Color infoContainerLight = Color(0xFFEBF6FF);
  static const Color infoContainerDark = Color(0xFF00121F);

  // Light Theme Colors
  static const Color lightPrimary = Color(0xFF0090BC);
  static const Color lightPrimaryVariant = Color(0xFFE6F4F8);
  static const Color lightPrimaryVariantDark = Color(0xFF006685);

  static const Color lightSecondary = Color(0xFFEE0026);
  static const Color lightSecondaryVariant = Color.fromRGBO(238, 0, 38, 0.12);

  static const Color lightBackground = Color(0xFFF9FAFA);
  static const Color lightDisabled = Color(0xFFDEDEDE);

  static const Color lightPrimaryText = Color(0xDE2F2F2F); // Primary Font 87%
  static const Color lightSecondaryText =
      Color(0x992F2F2F); // Secondary Font 60%

  /// 2F2F2F 37%
  static const Color lightTertiaryText = Color(0x5E2F2F2F); // Tertiary Font 37%
  static const Color lightStrokeColor = Color(0x142F2F2F); // Stroke 8%
  static const Color lightBackgroundContainer = Color(0xFFFFFFFF);

  // Dark Theme Colors
  static const Color darkPrimary = Color(0xFF54B5D2); // Lighter cyan-blue
  static const Color darkPrimaryVariant = Color(0xFF09131C); // Darker cyan-blue
  static const Color darkPrimaryVariantDark = Color(0xFF0090BC);

  static const Color darkSecondary = Color(0xFFEE0026); // Same red
  static const Color darkSecondaryVariant = Color(0xFF1D0005); // Darker red

  static const Color darkBackground = Color(0xFF0C0C0C); // Dark grayish-black
  static const Color darkSurface =
      Color(0xFF1E1E1E); // Update to match Figma // Slightly lighter dark gray
  static const Color darkDisabled = Color(0xFF2f2f2f); // Muted dark gray
  static const Color darkPrimaryText = Color(0xFFFFFFFF); // Primary Font
  static const Color darkSecondaryText =
      Color(0x99FFFFFF); // Secondary Font 60%
  static const Color darkTertiaryText = Color(0x5EFFFFFF); // Tertiary Font 37%
  static const Color darkStrokeColor = Color(0x14FFFFFF); // Stroke 8%

  static const Color darkBackgroundContainer = Color(0xFF1D1D1D);

  static const primaryGradientColors = [
    Color(0xFF015E8A),
    Color(0xFF018DB9),
  ];
  static const secondaryGradientColors = [
    Color(0xFFEB0125),
    Color(0xFF8C0117),
  ];
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: primaryGradientColors,
  );
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: secondaryGradientColors,
  );
}
