import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

// Text styles based on Figma design
class AppTextStyles {
  // Headline styles (Semi Bold)
  static TextStyle headline1 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 24, // Desktop: 32, Mobile: 24
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.5,
  );

  // IBM Plex Sans Arabic/20/SemiBold
  static TextStyle headline2 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 20, // Desktop: 24, Mobile: 20
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.5,
  );

  /// IBM Plex Sans Arabic/18/SemiBold
  static TextStyle headline3 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 18, // Desktop: 20, Mobile: 18
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.5,

    letterSpacing: 0,
  );

  // Title/large - IBM Plex Sans Arabic/16/SemiBold
  static TextStyle headline4 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 20, Mobile: 16
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.5,

    letterSpacing: 0,
  );

  // IBM Plex Sans Arabic/16/Regular
  static TextStyle body1 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/18, Mobile: 16/24
    fontWeight: FontWeight.w400, // Regular
    height: 1.5,

    letterSpacing: 0,
  );

  // IBM Plex Sans Arabic/14/Regular
  static TextStyle body2 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontWeight: FontWeight.w400,
    fontSize: 14, // Desktop: 14/24, Mobile: 14/20
    height: 1.5,

    letterSpacing: 0,
  );

  // IBM Plex Sans Arabic/12/Regular
  static TextStyle body3 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 12, // Desktop: 14/20, Mobile: 12/16
    fontWeight: FontWeight.w400, // Regular
    height: 1.5,

    letterSpacing: 0,
  );

  ///IBM Plex Sans Arabic/20/Medium(500)
  static TextStyle buttonMedium = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 20, // Desktop: 16/20, Mobile: 16/20
    fontWeight: FontWeight.w500, // Medium
    height: 1.5,

    letterSpacing: 0,
  );
  // IBM Plex Sans Arabic/18/Medium
  static TextStyle buttonNormal = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 18, // Desktop: 16/24, Mobile: 16/24
    fontWeight: FontWeight.w500, // Medium
    height: 1.5,
    letterSpacing: 0,
  );

  ///IBM Plex Sans Arabic/16/Medium(500)
  static TextStyle textButton = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/20, Mobile: 16/20
    fontWeight: FontWeight.w500, // Medium
    height: 1.5,

    letterSpacing: 0,
  );

  // IBM Plex Sans Arabic/12/Medium
  static TextStyle buttonSmall = const TextStyle(
      fontFamily: 'IBM Plex Sans Arabic',
      fontSize: 12, // Desktop: 14/16, Mobile: 14/16
      fontWeight: FontWeight.w500, // Medium
      height: 1.5,
      letterSpacing: 0);

  // Field styles
  static TextStyle fieldText1 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16, // Desktop: 16/24, Mobile: 16/24
    fontWeight: FontWeight.w400, // Regular
    height: 1.5,
  );

  static TextStyle fieldText2 = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 12, // Desktop: 12/16, Mobile: 12/16
    fontWeight: FontWeight.w400, // Regular
    height: 1.5,
  );

  static TextStyle fieldLabel = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 14,
    fontWeight: FontWeight.w600, // Semi Bold
    height: 1.5,
  );

  static TextStyle fieldInput = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 16,
    fontWeight: FontWeight.w400, // Regular
    height: 1.5,
  );

  static TextStyle fieldHint = const TextStyle(
    fontFamily: 'IBM Plex Sans Arabic',
    fontSize: 14,
    fontWeight: FontWeight.w400, // Regular
    color: Colors.grey,
    height: 1.5,
  );
}

extension AppColorsExtension on AppColors {
  /// Returns true if the theme is considered dark.
  bool get isDark => background.computeLuminance() < 0.5;

  static const successStates = [
    'paid',
    'approved',
    'paid',
    'success',
    'completed',
    'done',
    'delivery', // from image
    'approve',
  ];
  static const waitingStates = [
    'processing',
    'pending',
    'awaiting approval',
    'in review',
    'under process',
    'waiting',
    'queued',
    'in progress',
    'open', // from image
    'requested', // from image
    // from image
    'process', // from image
    'unpaid',
  ];
  static const errorStates = [
    'on hold',
    'rejected',
    'reject',
    'failed',
    'cancelled',
    'declined',
    'error',
    'expired',
    'void',
    'disputed',
    'cancel', // from image
  ];

  Color statusBackground(String status) {
    final lowerStatus = status.toLowerCase();
    if (successStates.contains(lowerStatus)) {
      return successContainer;
    } else if (waitingStates.contains(lowerStatus)) {
      return warningContainer;
    } else if (errorStates.contains(lowerStatus)) {
      return errorContainer;
    } else {
      return infoContainer;
    }
  }

  Color statusForeground(String status) {
    final lowerStatus = status.toLowerCase();
    if (successStates.contains(lowerStatus)) {
      return success;
    } else if (waitingStates.contains(lowerStatus)) {
      return warning;
    } else if (errorStates.contains(lowerStatus)) {
      return error;
    } else {
      return info;
    }
  }
}
