import 'package:ako_basma/styles/theme.dart';

import '../styles/colors.dart';
import 'package:flutter/material.dart';

class AppButtonStyle {
  static ButtonStyle primaryButtonStyle(
    BuildContext context, {
    bool compact = false,
    Color? color,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return ButtonStyle(
      visualDensity: compact ? VisualDensity.compact : null,
      textStyle: WidgetStateTextStyle.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return !compact
              ? textStyles.buttonMedium.copyWith(
                  color: colors.tertiaryText,
                  height: 1.5,
                )
              : textStyles.textButton.copyWith(
                  color: colors.tertiaryText,
                  height: 1.5,
                );
        }
        return !compact
            ? textStyles.buttonMedium.copyWith(
                color: const Color(0xffffffff),
                height: 1.5,
              )
            : textStyles.textButton.copyWith(
                color: const Color(0xffffffff),
                height: 1.5,
              );
      }),
      elevation: WidgetStateProperty.resolveWith((states) {
        return states.contains(WidgetState.pressed) ? 4.0 : 0.0;
      }),
      backgroundColor: WidgetStateColor.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return colors.disabled;
        }

        return color ?? colors.primary;
      }),
      foregroundColor: WidgetStateColor.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return colors.tertiaryText;
        }
        return Colors.white;
      }),
      padding: WidgetStateProperty.all(
        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      ),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(!compact ? 16 : 8),
        ),
      ),
      // minimumSize: WidgetStateProperty.all(
      //   const Size.fromHeight(56),
      // ),
      surfaceTintColor: WidgetStateProperty.all(colors.primary),
    );
    //  foregroundColor: Color(0xffffffff),
    //   surfaceTintColor: colors.primary,
    //   padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    //   // textStyle: TextStyle(color: colors(context).onPrimary),
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(16),
    //   ),
    //   minimumSize: const Size.fromHeight(56),
  }

  static ButtonStyle primaryOutlinedButtonStyle(
    BuildContext context, {
    bool compact = false,
    Color? color,
    Color? fontColor,
    double? radius,
    TextStyle? textStyle,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return ButtonStyle(
      visualDensity: compact ? VisualDensity.compact : null,
      textStyle: WidgetStateTextStyle.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return !compact
              ? textStyles.buttonMedium.copyWith(
                  color: fontColor ?? colors.tertiaryText,
                  height: 1.5,
                )
              : textStyles.textButton.copyWith(
                  color: fontColor ?? colors.tertiaryText,
                  height: 1.5,
                );
        }
        return !compact
            ? (textStyle ?? textStyles.buttonMedium).copyWith(
                color: fontColor ?? color ?? const Color(0xffffffff),
                height: 1.5,
              )
            : (textStyle ?? textStyles.textButton).copyWith(
                color: fontColor ?? color ?? const Color(0xffffffff),
                height: 1.5,
              );
      }),
      side: WidgetStateBorderSide.resolveWith((states) {
        return BorderSide(
          color: states.contains(WidgetState.disabled)
              ? colors.tertiaryText
              : color ?? colors.primary,
        );
      }),
      backgroundColor: WidgetStateColor.resolveWith((states) {
        return Colors.transparent;
      }),
      foregroundColor: WidgetStateColor.resolveWith((states) {
        return fontColor ?? color ?? Colors.white;
      }),
      padding: WidgetStateProperty.all(
        const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      ),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius ?? (!compact ? 16 : 8)),
        ),
      ),
      // minimumSize: WidgetStateProperty.all(
      //   const Size.fromHeight(56),
      // ),
      surfaceTintColor: WidgetStateProperty.all(Colors.transparent),
    );
    //  foregroundColor: Color(0xffffffff),
    //   surfaceTintColor: colors.primary,
    //   padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    //   // textStyle: TextStyle(color: colors(context).onPrimary),
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(16),
    //   ),
    //   minimumSize: const Size.fromHeight(56),
  }

  static ButtonStyle filledIconButton(BuildContext context) =>
      IconButton.styleFrom(
        backgroundColor: colors(context).primary,
        foregroundColor: colors(context).onPrimary,
        surfaceTintColor: colors(context).primary,
        // textStyle: AppTextStyle.primaryButton,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      );
}
