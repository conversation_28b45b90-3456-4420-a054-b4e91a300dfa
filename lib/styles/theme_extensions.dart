import 'package:flutter/material.dart';
import 'theme.dart';

class AppTheme {
  final ThemeData theme;
  final AppColors colors;
  final TextStyles textStyles;

  const AppTheme._({
    required this.theme,
    required this.colors,
    required this.textStyles,
  });

  static AppTheme of(BuildContext context) {
    final theme = Theme.of(context);
    return AppTheme._(
      theme: theme,
      colors: theme.extension<AppColors>()!,
      textStyles: theme.extension<TextStyles>()!,
    );
  }
}
