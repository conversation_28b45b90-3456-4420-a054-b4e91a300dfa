import 'dart:ui';

import 'package:ako_basma/models/employee_data.dart';

final List<EmployeeData> dummyEmployees = [
  EmployeeData(
    id: 1,
    imageUrl: 'https://i.pravatar.cc/150?img=1',
    name: '<PERSON>',
    contract: 'Full-time – On-site',
    jobTitle: 'Senior Software Engineer',
    department: 'Engineering',
    phone: '****** 123 4567',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2022, 1, 15),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Mansour District, Street 12',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 2,
    imageUrl: 'https://i.pravatar.cc/150?img=2',
    name: '<PERSON>',
    contract: 'Part-time – On-site',
    jobTitle: 'UI/UX Designer',
    department: 'Design',
    phone: '****** 234 5678',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2023, 3, 1),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Karrada District, Building 45',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 3,
    imageUrl: 'https://i.pravatar.cc/150?img=3',
    name: 'Michael Brown',
    contract: 'Full-time – Hybrid',
    jobTitle: 'Project Manager',
    department: 'Management',
    phone: '****** 345 6789',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2021, 6, 10),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Jadriya District, Block 7',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 4,
    imageUrl: 'https://i.pravatar.cc/150?img=4',
    name: 'Sarah Wilson',
    contract: 'Full-time – On-site',
    jobTitle: 'HR Specialist',
    department: 'Human Resources',
    phone: '****** 456 7890',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2022, 8, 20),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Zayouna District, Street 23',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 5,
    imageUrl: 'https://i.pravatar.cc/150?img=5',
    name: 'David Miller',
    contract: 'Freelance',
    jobTitle: 'DevOps Engineer',
    department: 'Engineering',
    phone: '****** 567 8901',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2023, 1, 5),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Yarmouk District, Building 15',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 6,
    imageUrl: 'https://i.pravatar.cc/150?img=6',
    name: 'Jennifer Davis',
    contract: 'Full-time – Remote',
    jobTitle: 'Marketing Manager',
    department: 'Marketing',
    phone: '****** 678 9012',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2022, 4, 15),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Salhiya District, Block 3',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 7,
    imageUrl: 'https://i.pravatar.cc/150?img=7',
    name: 'Robert Taylor',
    contract: 'Part-time – On-site',
    jobTitle: 'Content Writer',
    department: 'Marketing',
    phone: '****** 789 0123',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2023, 2, 1),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Adhamiya District, Street 8',
    zipCode: '62001',
  ),
  EmployeeData(
    id: 8,
    imageUrl: 'https://i.pravatar.cc/150?img=8',
    name: 'Lisa Anderson',
    contract: 'Full-time – On-site',
    jobTitle: 'Financial Analyst',
    department: 'Finance',
    phone: '****** 890 1234',
    email: '<EMAIL>',
    dateOfJoining: DateTime(2021, 9, 1),
    country: 'Iraq',
    city: 'Baghdad',
    address: 'Al-Karrada District, Building 32',
    zipCode: '62001',
  ),
];

const dummyColors = {
  'blue': Color(0xFF7BB7C9),
  'teal': Color(0xFF217882),
  'purple': Color(0xFF6B67B6),
  'brown': Color(0xFFB2987D),
  'pink': Color(0xFFDD83C8),
  'navy': Color(0xFF00121F),
  'darkBrown': Color(0xFF1F1505),
  'darkGreen': Color(0xFF03150C),
  'darkRed': Color(0xFF1A0506),
  'burgundy': Color(0xFF1D0005),
};
final List<Map<String, dynamic>> dummyChatData = [
  {
    'name': 'Ethaar Hussein',
    'message': 'Great! 😊',
    'time': DateTime.now().subtract(const Duration(hours: 2)),
    'profileImage': 'assets/images/person.png',
    'isRead': true,
  },
  {
    'name': 'Killan James',
    'message': 'Hello! Everyone',
    'time': DateTime.now().subtract(const Duration(hours: 6, minutes: 30)),
    'profileImage': 'assets/images/person.png',
    'isRead': false,
  },
  {
    'name': 'Sarah Ahmed',
    'message': 'How are you doing?',
    'time': DateTime.now().subtract(const Duration(hours: 8, minutes: 45)),
    'profileImage': 'assets/images/person.png',
    'isRead': true,
  },
  {
    'name': 'John Smith',
    'message': 'Let\'s meet tomorrow',
    'time': DateTime.now().subtract(const Duration(hours: 9, minutes: 15)),
    'profileImage': 'assets/images/person.png',
    'isRead': false,
  },
  {
    'name': 'Maria Garcia',
    'message': 'Thank you for your help!',
    'time': DateTime.now().subtract(const Duration(hours: 10, minutes: 30)),
    'profileImage': 'assets/images/person.png',
    'isRead': true,
  },
  {
    'name': 'Ahmed Ali',
    'message': 'See you soon',
    'time': DateTime.now().subtract(const Duration(hours: 11, minutes: 40)),
    'profileImage': 'assets/images/person.png',
    'isRead': false,
  },
];
