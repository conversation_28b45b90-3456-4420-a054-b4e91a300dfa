import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

class FilterButton extends StatelessWidget {
  const FilterButton({
    super.key,
    required this.onPress,
    this.filterCount = 0,
    this.onClear,
  });

  final int filterCount;
  final VoidCallback onPress;
  final VoidCallback? onClear;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;
    return InkWell(
      onTap: () {
        onPress();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        child: AnimatedSize(
          duration: 400.milliseconds,
          child: filterCount == 0
              ? Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Iconsax.filter_search_copy,
                        size: 24,
                        color: colors.secondaryText,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        strings.filter,
                        style: textStyles.body3.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                )
              : Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(8, 0, 0, 0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        strings.filter,
                        style: textStyles.body3.copyWith(
                          color: colors.primary,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        height: 16,
                        width: 16,
                        padding: const EdgeInsets.all(1),
                        decoration: ShapeDecoration(
                          shape: CircleBorder(),
                          color: colors.primary,
                        ),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            filterCount.toString(),
                            style: textStyles.body3.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      InkWell(
                        onTap: onClear,
                        child: Padding(
                          padding:
                              const EdgeInsetsDirectional.fromSTEB(4, 8, 8, 8),
                          child: Icon(
                            Icons.close_rounded,
                            size: 16,
                            color: colors.secondaryText,
                          ),
                        ),
                      ).animate().fadeIn(),
                    ],
                  ),
                ),
        ),
      ),
    );
  }
}
