import 'dart:math';

import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:ako_basma/styles/theme_extensions.dart';

import '../../styles/ui.dart';

class PrimaryButton extends StatefulWidget {
  const PrimaryButton({
    super.key,
    required this.label,
    required this.onTap,
    this.style,
    this.textStyle,
    this.wrapBottomPadding = false,
    this.expand = true,
    this.padding,
    this.isCompact = false,
    this.activeColor,
    this.onPressed,
    this.prefixIcon,
  });

  const PrimaryButton.async({
    super.key,
    required this.label,
    required this.onPressed,
    this.style,
    this.textStyle,
    this.wrapBottomPadding = false,
    this.expand = true,
    this.padding,
    this.isCompact = false,
    this.activeColor,
    this.prefixIcon,
    this.onTap,
  });

  final String label;
  final void Function()? onTap;
  final Future<void> Function()? onPressed;
  final ButtonStyle? style;
  final TextStyle? textStyle;
  final bool expand;
  final EdgeInsetsDirectional? padding;
  final bool wrapBottomPadding;
  final Color? activeColor;
  final bool isCompact;
  final IconData? prefixIcon;

  @override
  State<PrimaryButton> createState() => _PrimaryButtonState();
}

class _PrimaryButtonState extends State<PrimaryButton> {
  bool _loading = false;

  static const _defaultPadding =
      EdgeInsetsDirectional.symmetric(vertical: 8, horizontal: 16);
  static const _defaultCompactIconBtnPadding =
      EdgeInsetsDirectional.symmetric(vertical: 8, horizontal: 0);
  @override
  Widget build(BuildContext context) {
    if (widget.wrapBottomPadding) {
      return Padding(
        padding: EdgeInsets.fromLTRB(
            16, 0, 16, max(MediaQuery.paddingOf(context).bottom, 24)),
        child: _buildButton(),
      );
    }
    return _buildButton();
  }

  Widget _buildButton() {
    final colors = AppColors.of(context);

    final handleBehavior = _loading
        ? null
        : widget.onPressed != null
            ? _handleAsyncPress
            : widget.onTap;

    // Determine effective padding based on isCompact
    final effectivePadding = widget.padding ??
        (widget.isCompact
            ? widget.prefixIcon != null
                ? _defaultCompactIconBtnPadding
                : const EdgeInsetsDirectional.symmetric(
                    vertical: 7.5, horizontal: 16)
            : _defaultPadding);

    final buttonContent = Stack(
      alignment: Alignment.center,
      children: [
        // Text content (visible when not loading)
        Visibility(
          visible: !_loading,
          maintainSize: true, // Keep the size of the text even when invisible
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
              padding: effectivePadding,
              // widget.prefixIcon == null
              //     ? effectivePadding
              //     : effectivePadding.copyWith(start: 0, end: 8),
              child: widget.prefixIcon == null
                  ? Text(
                      widget.label,
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          widget.prefixIcon!,
                          size: 24,
                          color:
                              widget.onPressed == null && widget.onTap == null
                                  ? colors.tertiaryText
                                  : Colors.white,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          widget.label,
                        ),
                      ],
                    )),
        ),
        // Loading indicator (visible when loading)
        Visibility(
          visible: _loading,
          maintainSize: true,
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: (effectivePadding.vertical) + 2,
                horizontal: effectivePadding.horizontal),
            child: const DotsLoadingIndicator(),
          ),
        ),
      ],
    );

    final button = ElevatedButton(
        onPressed: handleBehavior,
        style:
            // can work for outlined buttons(if there are any) too.
            // take an extra argument of buttonType.

            widget.style ??
                AppButtonStyle.primaryButtonStyle(
                  context,
                  compact: widget.isCompact,
                  color: widget.activeColor,
                ).copyWith(
                    backgroundColor: _loading
                        ? WidgetStateColor.resolveWith((_) => colors
                            .primaryVariant) // Assuming primaryVariant is your desired loading background color
                        : null),

        // removed Center from here.. since it made the btn expand. didnt notice any change in the rest of usages.
        child: buttonContent
        // widget.prefixIcon == null
        //     ? buttonContent
        //     : Row(
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           Icon(
        //             widget.prefixIcon!,
        //             size: 24,
        //             color: widget.onPressed == null && widget.onTap == null
        //                 ? colors.tertiaryText
        //                 : Colors.white,
        //           ),
        //           const SizedBox(width: 8),
        //           buttonContent,
        //         ],
        // ),
        );

    return widget.expand ? Row(children: [Expanded(child: button)]) : button;
  }

  Future<void> _handleAsyncPress() async {
    setState(() => _loading = true);
    try {
      await widget.onPressed?.call();
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }
}
