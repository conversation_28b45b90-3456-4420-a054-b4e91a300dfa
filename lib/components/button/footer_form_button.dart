import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

class FooterFormButton extends StatelessWidget {
  const FooterFormButton({
    super.key,
    this.onCancel,
    this.onCancelAsync,
    this.onSubmit,
    this.onSubmitAsync,
    this.cancelLabel,
    this.submitLabel,
    this.cancelButtonStyle,
    this.submitButtonStyle,
    this.cancelTextStyle,
    this.submitTextStyle,
    this.cancelColor,
    this.submitColor,
    this.cancelFlex = 1,
    this.submitFlex = 2,
    this.spacing = 12,
  });

  final VoidCallback? onCancel;
  final Future<void> Function()? onCancelAsync;
  final VoidCallback? onSubmit;
  final Future<void> Function()? onSubmitAsync;
  final String? cancelLabel;
  final String? submitLabel;
  final ButtonStyle? cancelButtonStyle;
  final ButtonStyle? submitButtonStyle;
  final TextStyle? cancelTextStyle;
  final TextStyle? submitTextStyle;
  final Color? cancelColor;
  final Color? submitColor;
  final int cancelFlex;
  final int submitFlex;
  final double spacing;

  @override
  Widget build(BuildContext context) {
    // localize the default labels too.
    final strings = AppLocalizations.of(context)!;
    return IntrinsicHeight(
      child: Row(
        children: [
          Expanded(
            flex: cancelFlex,
            child: AppOutlinedButton(
              label: cancelLabel ?? strings.cancel,
              onTap: onCancel,
              onPressed: onCancelAsync,
              style: cancelButtonStyle,
              textStyle: cancelTextStyle,
              tintColor: cancelColor,
            ),
          ),
          SizedBox(width: spacing),
          Expanded(
            flex: submitFlex,
            child: PrimaryButton(
              label: submitLabel ?? strings.save,
              onTap: onSubmit,
              onPressed: onSubmitAsync,
              style: submitButtonStyle,
              textStyle: submitTextStyle,
              activeColor: submitColor,
              isCompact: true,
            ),
          ),
        ],
      ),
    );
  }
}
