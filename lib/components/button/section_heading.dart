import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';

class SectionHeading extends StatelessWidget {
  final String title;
  final Color? titleColor;
  final String? actionText;
  final Widget? action;
  final VoidCallback? onActionPressed;
  final EdgeInsetsGeometry? padding;
  final TextStyle? titleStyle;

  /// default style 
  const SectionHeading({
    super.key,
    required this.title,
    this.titleColor,
    this.actionText,
    this.action,
    this.onActionPressed,
    this.padding,
    this.titleStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    return Padding(
      padding: padding ??
          EdgeInsetsDirectional.fromSTEB(16, 0, actionText == null ? 16 : 5, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: titleStyle ?? theme.textStyles.textButton
                .copyWith(color: titleColor ?? theme.colors.primaryText),
          ),
          if (action != null)
            action!
          else if (actionText != null)
            TextButton(
              onPressed: onActionPressed,
              style: TextButton.styleFrom(
                // padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                alignment: Alignment.centerRight,
              ),
              child: Text(
                actionText!,
                style: theme.textStyles.buttonSmall
                    .copyWith(color: theme.colors.primary),
              ),
            ),
        ],
      ),
    );
  }
}
