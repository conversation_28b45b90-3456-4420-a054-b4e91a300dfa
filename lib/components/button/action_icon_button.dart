import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';

class ActionIconButton extends StatelessWidget {
  final dynamic icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? iconColor;
  final double? iconSize;
  final Color? borderColor;

  const ActionIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.iconColor,
    this.iconSize,
    this.borderColor,
  }) : assert(icon is IconData || icon is Widget,
            'Icon must be either IconData or Widget');

  @override
  Widget build(BuildContext context) {
    final colors = Theme.of(context).extension<AppColors>()!;

    return SizedBox(
      child: Material(
        color: backgroundColor ?? colors.backgroundContainer,
        borderRadius: BorderRadius.circular(6.67),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6.67),
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.67),
              border: Border.all(
                width: 0.83, 
                color: borderColor ?? colors.primaryVariant
              ),
            ),
            child: icon is IconData
                ? Icon(
                    icon,
                    size: iconSize ?? 20,
                    color: iconColor ?? colors.secondaryText,
                  )
                : icon as Widget,
          ),
        ),
      ),
    );
  }
}
