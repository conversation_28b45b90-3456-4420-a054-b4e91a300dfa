import 'dart:math';

import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:jumping_dot/jumping_dot.dart';

class AppOutlinedButton extends StatefulWidget {
  const AppOutlinedButton({
    super.key,
    required this.label,
    required this.onTap,
    this.style,
    this.textStyle,
    this.expand = true,
    this.padding = const EdgeInsets.symmetric(vertical: 7, horizontal: 16),
    this.tintColor,
    this.outlineColor,
    this.loadingDotColor,
    this.prefixIcon,
    this.onPressed,
  });

  const AppOutlinedButton.async({
    super.key,
    required this.label,
    required this.onPressed,
    this.style,
    this.textStyle,
    this.expand = true,
    this.padding = const EdgeInsets.symmetric(vertical:7, horizontal: 16),
    this.tintColor,
    this.outlineColor,
    this.loadingDotColor,
    this.prefixIcon,
    this.onTap,
  });

  final String label;
  final void Function()? onTap;
  final Future<void> Function()? onPressed;
  final ButtonStyle? style;
  final TextStyle? textStyle;
  final bool expand;
  final EdgeInsets? padding;
  // final bool isAsync;
  final Color? tintColor;
  final Color? outlineColor;

  final Color? loadingDotColor;
  final IconData? prefixIcon;

  @override
  State<AppOutlinedButton> createState() => _AppOutlinedButtonState();
}

class _AppOutlinedButtonState extends State<AppOutlinedButton> {
  bool _loading = false;

  @override
  Widget build(BuildContext context) {
    return _buildButton();
  }

  Widget _buildButton() {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    final handleBehavior = _loading
        ? null
        : widget.onPressed != null
            ? _handleAsyncPress
            : widget.onTap;
    final textColor = widget.tintColor ?? colors.secondaryText;
    final Color resolvedTintColor =
        widget.tintColor ?? colors.tertiaryText; // Default tint color

    final buttonContent = Stack(
      alignment: Alignment.center,
      children: [
        // Text content (visible when not loading)
        Visibility(
          visible: !_loading,
          maintainSize: true,
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
            padding: widget.padding!,
            child: Text(
              widget.label,
              style: widget.textStyle ??
                  textStyles.body.copyWith(
                    color: textColor, // Use resolved tint color for text
                  ),
            ),
          ),
        ),
        // Loading indicator (visible when loading)
        Visibility(
          visible: _loading,
          maintainSize: true,
          maintainAnimation: true,
          maintainState: true,
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: (widget.padding?.vertical ?? 0),
                horizontal: widget.padding?.horizontal ?? 0),
            child: DotsLoadingIndicator(
              activeColor: widget.loadingDotColor ??
                  textColor, // Use loadingDotColor or resolved tint color
            ),
          ),
        ),
      ],
    );

    final button = OutlinedButton(
      onPressed: handleBehavior,
      style: widget.style ??
          OutlinedButton.styleFrom(
              side: BorderSide(
                color: (widget.outlineColor ?? resolvedTintColor) ??
                    colors.disabled, // Use resolved tint color for border
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: EdgeInsets.zero,
              visualDensity: VisualDensity.compact,
              // Override default text style to avoid conflict
              textStyle: Theme.of(context)
                  .extension<TextStyles>()!
                  .body2
                  .copyWith(color: Colors.transparent), // Hide default text
              overlayColor: colors.strokeColor),
      child: Center(
        child: widget.prefixIcon == null
            ? buttonContent
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    widget.prefixIcon!,
                    size: 24,
                    color: textColor,
                  ),
                  const SizedBox(width: 8),
                  buttonContent,
                ],
              ),
      ),
    );

    return widget.expand ? Row(children: [Expanded(child: button)]) : button;
  }

  Future<void> _handleAsyncPress() async {
    setState(() => _loading = true);
    try {
      await widget.onPressed?.call();
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }
}
