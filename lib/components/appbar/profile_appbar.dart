import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/i18n/util/remote_translation_service.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class ProfileAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const ProfileAppBar({
    super.key,
    this.actions,
  });
  final List<Widget>? actions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return SafeArea(
      bottom: false,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                context.go('/home/<USER>');
              },
              child: DecoratedBox(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(
                      width: 1,
                      color: colors.tertiaryText.withOpacity(0.5 * 0.37),
                    )),
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      border: Border.all(
                        width: 1,
                        color: colors.tertiaryText,
                      )),
                  child: Container(
                      height: 40,
                      width: 40,
                      margin: const EdgeInsets.all(3),
                      foregroundDecoration: BoxDecoration(
                        border: Border.all(
                          color: colors.tertiaryText,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadiusGeometry.circular(20),
                        child: const ImageContainer(
                          url: null,
                          placeholderAsset: 'assets/images/person.png',
                          placeholderFit: BoxFit.cover,
                          fit: BoxFit.cover,
                        ),
                      )),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      // remoteStrings.t('welcome'),
                      strings.welcome,
                      style:
                          textStyles.body3.copyWith(color: colors.primaryText),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Nada Jaafar Uday',
                      style: textStyles.headline4
                          .copyWith(fontSize: 12, color: colors.secondaryText),
                    ),
                  ],
                ),
              ),
            ),
            if (actions != null) ...actions!,
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
