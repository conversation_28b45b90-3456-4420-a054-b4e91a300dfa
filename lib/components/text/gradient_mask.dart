import 'package:flutter/material.dart';

class GradientMask extends StatelessWidget {
  final Widget child;
  final Gradient gradient;
  final BlendMode blendMode;

  const GradientMask({
    super.key,
    required this.child,
    required this.gradient,
    this.blendMode = BlendMode.srcIn,
  });

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      blendMode: blendMode,
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: child,
    );
  }
}

// Example usage:
// GradientMask(
//   gradient: LinearGradient(
//     colors: [Colors.blue, Colors.purple],
//     begin: Alignment.topLeft,
//     end: Alignment.bottomRight,
//   ),
//   child: Text(
//     'Gradient Text',
//     style: TextStyle(
//       fontSize: 24,
//       fontWeight: FontWeight.bold,
//     ),
//   ),
// ),
