import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

class AdaptiveHeightCarousel extends StatefulWidget {
  /// The number of items in the carousel
  final int itemCount;

  /// Builder function to create items
  final Widget Function(BuildContext context, int index) itemBuilder;

  /// Optional padding for each item
  final EdgeInsetsGeometry? itemPadding;

  /// Optional viewport fraction (0.0 to 1.0)
  final double? viewportFraction;

  /// Whether to enable infinite scroll
  final bool enableInfiniteScroll;

  /// Whether to pad the ends
  final bool padEnds;

  /// Optional controller for the carousel
  final CarouselSliderController? controller;

  /// Optional callback when page changes
  final Function(int index, CarouselPageChangedReason reason)? onPageChanged;

  /// Optional auto play interval
  final Duration? autoPlayInterval;

  const AdaptiveHeightCarousel({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.itemPadding,
    this.viewportFraction,
    this.enableInfiniteScroll = false,
    this.padEnds = true,
    this.controller,
    this.onPageChanged,
    this.autoPlayInterval,
  })  : assert(itemCount > 0, 'itemCount must be greater than 0'),
        assert(
            viewportFraction == null ||
                (viewportFraction > 0 && viewportFraction <= 1),
            'viewportFraction must be between 0 and 1'),
        assert(autoPlayInterval == null || autoPlayInterval > Duration.zero,
            'autoPlayInterval must be greater than Duration.zero');

  @override
  State<AdaptiveHeightCarousel> createState() => _AdaptiveHeightCarouselState();
}

class _AdaptiveHeightCarouselState extends State<AdaptiveHeightCarousel> {
  double _carouselHeight = 0;
  final _measurementKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_measurementKey.currentContext != null) {
        final RenderBox renderBox =
            _measurementKey.currentContext!.findRenderObject() as RenderBox;
        final itemHeight = renderBox.size.height;
        setState(() {
          _carouselHeight = itemHeight;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            // Hidden item for measurement
            Offstage(
              child: Padding(
                padding: widget.itemPadding ?? EdgeInsets.zero,
                child: KeyedSubtree(
                  key: _measurementKey,
                  child: widget.itemBuilder(context, 0),
                ),
              ),
            ),
            // Actual carousel
            if (_carouselHeight > 0)
              CarouselSlider(
                options: CarouselOptions(
                  viewportFraction: widget.viewportFraction ??
                      (1 - 24 / constraints.maxWidth),
                  enableInfiniteScroll: widget.enableInfiniteScroll,
                  padEnds: widget.padEnds,
                  height: _carouselHeight,
                  onPageChanged: widget.onPageChanged,
                  autoPlayInterval:
                      widget.autoPlayInterval ?? const Duration(seconds: 3),
                ),
                carouselController: widget.controller,
                items: List.generate(widget.itemCount, (index) {
                  return Builder(
                    builder: (BuildContext context) {
                      return Padding(
                        padding: widget.itemPadding ?? EdgeInsets.zero,
                        child: widget.itemBuilder(context, index),
                      );
                    },
                  );
                }),
              ),
          ],
        );
      },
    );
  }
}
