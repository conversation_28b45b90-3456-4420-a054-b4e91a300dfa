import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class AnimatedStepper extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color? activeColor;
  final Color? completedColor;
  final Color? inactiveColor;
  final Color? lineColor;
  final double stepSize;
  final double lineHeight;
  final double spacing;

  const AnimatedStepper({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor,
    this.completedColor,
    this.inactiveColor,
    this.lineColor,
    this.stepSize = 40,
    this.lineHeight = 1,
    this.spacing = 4,
  }) : assert(currentStep >= 0 && currentStep <= totalSteps,
            'Current step must be between 0 and total steps');

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(totalSteps * 2 - 1, (index) {
        if (index.isEven) {
          // This is a step circle
          final stepNumber = (index ~/ 2) + 1;
          final isCompleted = stepNumber < currentStep;
          final isActive = stepNumber == currentStep;

          return _StepCircle(
            number: stepNumber,
            isCompleted: isCompleted,
            isActive: isActive,
            activeColor: activeColor ?? theme.colors.primary,
            completedColor: completedColor ?? theme.colors.success,
            inactiveColor: inactiveColor ?? theme.colors.disabled,
            inactiveTextColor: inactiveColor ?? theme.colors.tertiaryText,
            size: stepSize,
          );
        } else {
          // This is a connecting line
          final stepNumber = (index ~/ 2) + 1;
          final isCompleted = stepNumber < currentStep;
          final isActive = stepNumber == currentStep;

          return Expanded(
            child: _StepLine(
              isCompleted: isCompleted,
              isActive: isActive,
              activeColor: activeColor ?? theme.colors.primary,
              completedColor: completedColor ?? theme.colors.success,
              inactiveColor: lineColor ?? theme.colors.tertiaryText,
              height: lineHeight,
              minWidth: stepSize,
              spacing: spacing,
            ),
          );
        }
      }),
    );
  }
}

class _StepCircle extends StatelessWidget {
  final int number;
  final bool isCompleted;
  final bool isActive;
  final Color activeColor;
  final Color completedColor;
  final Color inactiveColor;
  final Color inactiveTextColor;

  final double size;

  const _StepCircle({
    required this.number,
    required this.isCompleted,
    required this.isActive,
    required this.activeColor,
    required this.completedColor,
    required this.inactiveColor,
    required this.inactiveTextColor,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: isCompleted
              ? completedColor
              : (isActive ? activeColor : inactiveColor),
          width: 1,
        ),
        color: isCompleted ? completedColor : Colors.transparent,
      ),
      child: Center(
        child: isCompleted
            ? Icon(
                Iconsax.card_tick,
                color: completedColor,
                size: size * 0.6,
              )
            : Text(number.toString(),
                style: theme.textStyles.headline4.copyWith(
                  fontSize: 12,
                  color: isActive ? activeColor : inactiveTextColor,
                )),
      ),
    )
        .animate(
          target: isCompleted ? 1 : (isActive ? 0.5 : 0),
        )
        .custom(
          duration: 500.ms,
          builder: (context, value, child) {
            final isTransitioningToCompleted = value > 0.5;
            final isTransitioningToActive = value > 0 && value <= 0.5;

            Color borderColor;
            Color textColor;
            Color backgroundColor;
            Widget? icon;

            if (isTransitioningToCompleted) {
              borderColor =
                  Color.lerp(activeColor, completedColor, (value - 0.5) * 2) ??
                      completedColor;
              textColor =
                  Color.lerp(activeColor, completedColor, (value - 0.5) * 2) ??
                      completedColor;
              backgroundColor = Color.lerp(
                    Colors.transparent,
                    completedColor,
                    (value - 0.5) * 2,
                  ) ??
                  Colors.transparent;
              icon = Stack(
                alignment: Alignment.center,
                children: [
                  Opacity(
                    opacity: 1 - ((value - 0.5) * 2),
                    child: Text(
                      number.toString(),
                      style: theme.textStyles.headline4.copyWith(
                        fontSize: 12,
                        color: textColor,
                      ),
                    ),
                  ),
                  Opacity(
                    opacity: (value - 0.5) * 2,
                    child: Icon(
                      Icons.check_rounded,
                      color: Colors.white,
                      size: size * 0.6,
                    ),
                  ),
                ],
              );
            } else if (isTransitioningToActive) {
              borderColor = Color.lerp(inactiveColor, activeColor, value * 2) ??
                  activeColor;
              textColor =
                  Color.lerp(inactiveTextColor, activeColor, value * 2) ??
                      activeColor;
              backgroundColor = Colors.transparent;
              icon = Text(
                number.toString(),
                style: theme.textStyles.headline4.copyWith(
                  fontSize: 12,
                  color: textColor,
                ),
              );
            } else {
              borderColor = inactiveColor;
              textColor = inactiveTextColor;
              backgroundColor = Colors.transparent;
              icon = Text(
                number.toString(),
                style: theme.textStyles.headline4.copyWith(
                  fontSize: 12,
                  color: textColor,
                ),
              );
            }

            return Container(
              width: size,
              height: size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: borderColor,
                  width: 1,
                ),
                color: backgroundColor,
              ),
              child: Center(child: icon),
            );
          },
        );
  }
}

class _StepLine extends StatelessWidget {
  final bool isCompleted;
  final bool isActive;
  final Color activeColor;
  final Color completedColor;
  final Color inactiveColor;
  final double height;
  final double minWidth;
  final double spacing;

  const _StepLine({
    required this.isCompleted,
    required this.isActive,
    required this.activeColor,
    required this.completedColor,
    required this.inactiveColor,
    required this.height,
    required this.minWidth,
    required this.spacing,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final lineWidth = availableWidth.isFinite ? availableWidth : minWidth;

        return Container(
          height: height,
          margin: EdgeInsets.symmetric(horizontal: spacing),
          child: Stack(
            children: [
              // Background line (always visible)
              Container(
                width: lineWidth,
                height: height,
                color: inactiveColor,
              ),
              // Animated fill line
              Container(
                width: lineWidth,
                height: height,
                color: isCompleted ? completedColor : activeColor,
              )
                  .animate(
                    target: isCompleted ? 1 : (isActive ? 0.5 : 0),
                  )
                  .custom(
                    duration: 500.ms,
                    builder: (context, value, child) {
                      final isTransitioningToCompleted = value > 0.5;
                      final isTransitioningToActive = value > 0 && value <= 0.5;

                      Color lineColor;
                      double progress;

                      if (isTransitioningToCompleted) {
                        lineColor = completedColor;
                        progress = (value - 0.5) * 2;
                      } else if (isTransitioningToActive) {
                        lineColor = activeColor;
                        progress = value * 2;
                      } else {
                        lineColor = inactiveColor;
                        progress = 0;
                      }

                      return Container(
                        width: lineWidth * progress,
                        height: height,
                        color: lineColor,
                      );
                    },
                  ),
            ],
          ),
        );
      },
    );
  }
}
