import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';

class AvatarRow extends StatelessWidget {
  final List<String> imageUrls;
  final int restriction;
  final double iconSize;
  final double overlapFactor;

  const AvatarRow({
    super.key,
    required this.imageUrls,
    this.restriction = 3,
    this.iconSize = 32,
    this.overlapFactor = 0.35,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final displayCount =
        imageUrls.length > restriction ? restriction : imageUrls.length;
    final extraCount = imageUrls.length - restriction;
    final overlap = iconSize * overlapFactor;
    final avatars = <Widget>[];

    for (int i = 0; i < displayCount; i++) {
      avatars.add(
        PositionedDirectional(
          start: i * (iconSize - overlap),
          child: Container(
            width: iconSize,
            height: iconSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.colors.background,
                width: 2,
              ),
            ),
            child: ClipOval(
              child: Image.network(
                imageUrls[i],
                width: iconSize,
                height: iconSize,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: theme.colors.background,
                  child: Icon(Icons.person,
                      size: iconSize * 0.6, color: theme.colors.tertiaryText),
                ),
              ),
            ),
          ),
        ),
      );
    }

    if (extraCount > 0) {
      avatars.add(
        PositionedDirectional(
          start: displayCount * (iconSize - overlap),
          child: Container(
            width: iconSize,
            height: iconSize,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: theme.colors.backgroundContainer,
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.colors.primaryVariant,
                width: 1,
              ),
            ),
            child: Text(
              "+$extraCount",
              style: theme.textStyles.headline4.copyWith(
                fontSize: 10,
                color: theme.colors.secondaryText,
              ),
              textScaler: TextScaler.noScaling,
            ),
          ),
        ),
      );
    }

    final totalWidth = (displayCount + (extraCount > 0 ? 1 : 0)) * iconSize -
        (displayCount + (extraCount > 0 ? 1 : 0) - 1) * overlap;

    return SizedBox(
      height: iconSize,
      width: totalWidth > 0 ? totalWidth : iconSize,
      child: Stack(
        clipBehavior: Clip.none,
        children: avatars,
      ),
    );
  }
}
