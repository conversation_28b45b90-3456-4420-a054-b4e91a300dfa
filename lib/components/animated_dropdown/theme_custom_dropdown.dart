import 'package:flutter/material.dart';
import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/styles/theme_extensions.dart';

class ThemeCustomDropdown extends StatelessWidget {
  final List<Map<String, dynamic>> items;
  final dynamic initialItem;
  final void Function(dynamic)? onChanged;
  final EdgeInsetsGeometry? closedHeaderPadding;
  final EdgeInsetsGeometry? listItemPadding;
  final EdgeInsetsGeometry? itemsListPadding;
  final bool excludeSelected;
  final bool onContainer;
  final bool hideSelectedFieldWhenExpanded;
  final TextStyle? headerTextStyle;
  final TextStyle? listItemTextStyle;
  final TextAlign headerTextAlign;
  final TextAlign listItemTextAlign;
  final String? hintText;
  final bool enabled;
  const ThemeCustomDropdown({
    super.key,
    required this.items,
    this.initialItem,
    this.onChanged,
    this.closedHeaderPadding,
    this.listItemPadding,
    this.itemsListPadding,
    this.excludeSelected = false,
    this.onContainer = false,
    this.hideSelectedFieldWhenExpanded = true,
    this.headerTextStyle,
    this.listItemTextStyle,
    this.headerTextAlign = TextAlign.start,
    this.listItemTextAlign = TextAlign.start,
    this.hintText,
    this.enabled = true,
  });

  Color? _getColorFor(dynamic item) {
    final found = items.firstWhere(
      (element) => element['item'] == item,
      orElse: () => {},
    );
    return found['color'];
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return CustomDropdown(
      onContainer: onContainer,
      initialItem: initialItem,
      listItemPadding: listItemPadding as EdgeInsets?,
      itemsListPadding: itemsListPadding as EdgeInsets?,
      excludeSelected: excludeSelected,
      hideSelectedFieldWhenExpanded: hideSelectedFieldWhenExpanded,
      closedHeaderPadding: closedHeaderPadding as EdgeInsets?,
      items: items.map((e) => e['item']).toList(),
      onChanged: onChanged,
      listItemBuilder: (context, item, isSelected, onItemSelect) {
        final color = _getColorFor(item) ?? theme.colors.secondaryText;
        final textWidget = Text(
          item.toString(),
          style: (listItemTextStyle ?? theme.textStyles.body).copyWith(
            color: color,
          ),
          textAlign: listItemTextAlign,
        );
        return Container(
          padding: listItemPadding ??
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: listItemTextAlign == TextAlign.center
              ? Center(child: textWidget)
              : textWidget,
        );
      },
      enabled: enabled,
      headerBuilder: (context, selectedItem, enabled) {
        if (selectedItem == null) {
          final textWidget = Text(
            hintText ?? 'Select value',
            style: (headerTextStyle ?? theme.textStyles.body).copyWith(
              color: theme.colors.tertiaryText,
            ),
            textAlign: headerTextAlign,
          );
          return headerTextAlign == TextAlign.center
              ? Center(child: textWidget)
              : textWidget;
        }

        final color = _getColorFor(selectedItem) ?? theme.colors.secondaryText;
        final textWidget = Text(
          selectedItem.toString(),
          style: (headerTextStyle ?? theme.textStyles.body).copyWith(
            color: color,
          ),
          textAlign: headerTextAlign,
        );
        return headerTextAlign == TextAlign.center
            ? Center(child: textWidget)
            : textWidget;
      },
    );
  }
}
