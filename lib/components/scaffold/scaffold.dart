import 'package:ako_basma/screens/tasks/tasks.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../nav/bottom_navbar.dart';

class ScaffoldWithNestedNavigation extends ConsumerWidget {
  const ScaffoldWithNestedNavigation({
    Key? key,
    required this.child,
    required this.selectedIndex,
    required this.navigationItems,
  }) : super(key: key ?? const ValueKey('ScaffoldWithNestedNavigation'));
  // final StatefulNavigationShell navigationShell;
  final Widget child;
  final int selectedIndex;
  final List<NavigationItem> navigationItems;

  void _goBranch(int index, BuildContext context) {
    context.go(navigationItems[index].path);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: child,
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: selectedIndex,
        onTap: (index) {
          _goBranch(index, context);
        },
      ),
      extendBody: false,
      primary: true,
    );
  }
}

/// An item that represents a navigation destination in a navigation bar/rail.
class NavigationItem {
  /// Path in the router.
  final String path;

  /// Widget to show when navigating to this [path].
  final Widget Function(BuildContext context, GoRouterState state) body;

  /// Icon in the navigation bar.
  // final IconData icon;

  /// Icon in the navigation bar when selected.
  // final IconData? selectedIcon;

  /// Label in the navigation bar.
  // final String label;

  /// The subroutes of the route from this [path].
  final List<RouteBase> routes;

  NavigationItem({
    required this.path,
    required this.body,
    // required this.icon,
    // this.selectedIcon,
    // required this.label,
    this.routes = const [],
  });
}
