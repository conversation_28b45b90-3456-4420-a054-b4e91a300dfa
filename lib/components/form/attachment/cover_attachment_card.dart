import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:pdfx/pdfx.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class CoverAttachmentCard extends StatefulWidget {
  final String? filePath;
  final String? url;
  final double width;
  final double height;
  final BoxFit? fit;
  final bool centerEditIcon;
  const CoverAttachmentCard({
    super.key,
    this.filePath,
    this.url,
    required this.width,
    this.height = 148,
    this.fit,
    this.centerEditIcon = true,
  });

  @override
  State<CoverAttachmentCard> createState() => _CoverAttachmentCardState();
}

class _CoverAttachmentCardState extends State<CoverAttachmentCard> {
  Widget? _thumbnail;
  bool _isLoading = true;
  static const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        _loadThumbnail();
      },
    );
  }

  @override
  void didUpdateWidget(CoverAttachmentCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filePath != widget.filePath || oldWidget.url != widget.url) {
      _loadThumbnail();
    }
  }

  Future<void> _loadThumbnail() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _thumbnail = null;
    });

    try {
      Widget thumbnail;
      if (widget.filePath != null) {
        thumbnail = await _buildLocalThumbnail(widget.filePath!);
      } else if (widget.url != null) {
        thumbnail = await _buildRemoteThumbnail(widget.url!);
      } else {
        thumbnail = _buildPlaceholder();
      }

      if (!mounted) return;
      setState(() {
        _thumbnail = thumbnail;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error building thumbnail: $e');
      if (!mounted) return;
      setState(() {
        _thumbnail = _buildPlaceholder();
        _isLoading = false;
      });
    }
  }

  Future<Widget> _buildLocalThumbnail(String filePath) async {
    final extension = path.extension(filePath).toLowerCase();

    if (imageExtensions.contains(extension)) {
      return Image.file(
        File(filePath),
        fit: widget.fit ?? BoxFit.cover,
        width: widget.width,
        height: widget.height,
        cacheWidth: (widget.width * 3).toInt(),
        cacheHeight: (widget.height * 3).toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } else if (extension == '.pdf') {
      return _buildPdfThumbnail(filePath);
    } else {
      return _buildDocumentThumbnail(extension);
    }
  }

  Future<Widget> _buildRemoteThumbnail(String url) async {
    final extension = path.extension(url).toLowerCase();

    if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
      return Image.network(
        url,
        fit: widget.fit ?? BoxFit.cover,
        width: widget.width,
        height: widget.height,
        cacheWidth: (widget.width * 3).toInt(),
        cacheHeight: (widget.height * 3).toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } else if (extension == '.pdf') {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/temp_${DateTime.now().millisecondsSinceEpoch}.pdf');

      try {
        final response = await http.get(Uri.parse(url));
        await tempFile.writeAsBytes(response.bodyBytes);
        return _buildPdfThumbnail(tempFile.path);
      } catch (e) {
        debugPrint('Error downloading PDF: $e');
        return _buildPlaceholder();
      }
    } else {
      return _buildDocumentThumbnail(extension);
    }
  }

  Future<Widget> _buildPdfThumbnail(String filePath) async {
    try {
      PdfDocument doc = await PdfDocument.openFile(filePath);
      PdfPage page = await doc.getPage(1);

      // Calculate the target size based on the widget dimensions
      final targetWidth = widget.width * 3; // Account for device pixel ratio
      final targetHeight = widget.height * 3;

      // Calculate the aspect ratio to maintain proportions
      final pageAspectRatio = page.width / page.height;
      final targetAspectRatio = widget.width / widget.height;

      double renderWidth, renderHeight;
      if (pageAspectRatio > targetAspectRatio) {
        // Page is wider than target
        renderWidth = targetWidth;
        renderHeight = targetWidth / pageAspectRatio;
      } else {
        // Page is taller than target
        renderHeight = targetHeight;
        renderWidth = targetHeight * pageAspectRatio;
      }

      final pageImage = await page.render(
        width: renderWidth,
        height: renderHeight,
        format: PdfPageImageFormat.jpeg,
        backgroundColor: '#FFFFFF',
      );

      await page.close();
      await doc.close();

      if (pageImage == null) {
        return _buildPlaceholder();
      }

      return Image.memory(
        pageImage.bytes,
        width: widget.width,
        height: widget.height,
        fit: widget.fit ?? BoxFit.contain,
        cacheWidth: renderWidth.toInt(),
        cacheHeight: renderHeight.toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } catch (e) {
      debugPrint('Error rendering PDF thumbnail: $e');
      return _buildPlaceholder();
    }
  }

  Widget _buildDocumentThumbnail(String extension) {
    IconData iconData;
    switch (extension) {
      case '.doc':
      case '.docx':
        iconData = Icons.description_rounded;
        break;
      case '.xls':
      case '.xlsx':
        iconData = Icons.table_chart_rounded;
        break;
      case '.ppt':
      case '.pptx':
        iconData = Icons.slideshow_rounded;
        break;
      case '.txt':
        iconData = Icons.text_snippet_rounded;
        break;
      default:
        iconData = Icons.insert_drive_file_rounded;
    }

    final theme = AppTheme.of(context);
    return Container(
      color: theme.colors.background,
      child: Icon(
        iconData,
        size: 48,
        color: theme.colors.secondaryText,
      ),
    );
  }

  Widget _buildPlaceholder() {
    final theme = AppTheme.of(context);
    return Container(
      color: theme.colors.background,
      child: Icon(
        Icons.broken_image,
        size: 48,
        color: theme.colors.disabled,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final hasData = !(widget.filePath == null && widget.url == null);
    final content = _isLoading
        ? const Center(child: DotsLoadingIndicator())
        : (_thumbnail ?? _buildPlaceholder())
            .animate()
            .fadeIn(duration: 300.milliseconds);
    final editIcon = IgnorePointer(
      child: Container(
        height: 44,
        width: 44,
        decoration: ShapeDecoration(
            shape: const CircleBorder(),
            color: theme.colors.backgroundContainer),
        child: Center(
          child: Icon(
            SolarIconsOutline.galleryEdit,
            color: theme.colors.primary,
            size: 24,
            applyTextScaling: false,
          ),
        ),
      ),
    );
    return InkWell(
      // borderRadius: BorderRadius.circular(8),
      // onTap: () async {
      //   final uri = widget.url != null
      //       ? Uri.parse(widget.url!)
      //       : Uri.file(widget.filePath!);
      //   if (await canLaunchUrl(uri)) {
      //     await launchUrl(uri);
      //   }
      // },
      child: SizedBox(
        height: widget.height,
        width: widget.width,
        child: Stack(
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              // padding: const EdgeInsets.all(8),
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: theme.colors.primaryVariant,
              ),
              child: hasData
                  ? content
                  : Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            SolarIconsOutline.galleryAdd,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Image / Video',
                            style: theme.textStyles.button,
                            textAlign: TextAlign.center,
                            textScaler: TextScaler.noScaling,
                          )
                        ],
                      ),
                    ),
            ),
            if (hasData)
              widget.centerEditIcon
                  ? Align(
                      // alignment: Alignment.center,
                      alignment: AlignmentDirectional.bottomEnd,
                      child: editIcon,
                    )
                  : PositionedDirectional(
                      bottom: 8,
                      end: 8,
                      child: editIcon,
                    ),
          ],
        ),
      ),
    );
  }
}
