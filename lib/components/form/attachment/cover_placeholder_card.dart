// import 'dart:math';

// import 'package:ako_basma/components/form/attachment/attachment_grid_card.dart';
// import 'package:ako_basma/styles/theme_extensions.dart';
// import 'package:ako_basma/util/ui/popups.dart';
// import 'package:dotted_border/dotted_border.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_animate/flutter_animate.dart';
// import 'package:iconsax_flutter/iconsax_flutter.dart';
// import 'package:solar_icons/solar_icons.dart';
// import 'package:url_launcher/url_launcher.dart';

// class CoverPlaceholderCard extends StatelessWidget {
//   const CoverPlaceholderCard({
//     super.key,
//     this.icon,
//     this.filePath,
//     this.url,
//     this.previewWidth,
//     this.previewHeight,
//     this.onTap,
//   });

//   final dynamic icon;
//   final String? filePath;
//   final String? url;
//   final double? previewWidth;
//   final double? previewHeight;
//   final VoidCallback? onTap;
//   // final Attac

//   @override
//   Widget build(BuildContext context) {
//     final theme = AppTheme.of(context);
//     final resIcon = icon ?? SolarIconsOutline.galleryAdd;
//     final resLabel = 'Image / Video';
//     final data = filePath ?? url;
//     return InkWell(
//       borderRadius: BorderRadius.circular(6),
//       onTap: () async {
//         unfocus();
//         if (data != null) {
//           final uri = filePath != null ? Uri.file(filePath!) : Uri.parse(url!);
//           if (await canLaunchUrl(uri)) {
//             await launchUrl(uri);
//           }
//         } else {
//           if (onTap != null) {
//             onTap!();
//           }
//         }
//       },
//       child: Stack(
//         children: [
//           AnimatedContainer(
//             duration: 300.milliseconds,
//             height: previewHeight,
//             clipBehavior: Clip.hardEdge,
//             // padding: ,
//             // constraints: const BoxConstraints(minHeight: pr),
//             // margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
//             decoration: BoxDecoration(
//               color: theme.colors.primaryVariant,
//               borderRadius: BorderRadius.circular(12),
//             ),

//             child: data != null
//                 ? LayoutBuilder(
//                     builder: (context, constraints) {
//                       return AttachmentGridCard(
//                         filePath: filePath,
//                         url: url,
//                         width: constraints.maxWidth,
//                         height: previewHeight ?? 148,
//                         minimal: true,
//                         fit: BoxFit.cover,
//                       );
//                     },
//                   )
//                 : Center(
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         const Icon(
//                           SolarIconsOutline.galleryAdd,
//                           size: 32,
//                         ),
//                         const SizedBox(height: 8),
//                         Text(
//                           'Image / Video',
//                           style: theme.textStyles.button,
//                           textAlign: TextAlign.center,
//                           textScaler: TextScaler.noScaling,
//                         )
//                       ],
//                     ),
//                   ),
//           ),
//           if (data != null)
//             Align(
//               alignment: Alignment.center,
//               child: InkWell(
//                 onTap: () {
//                   onTap?.call();
//                 },
//                 child: Container(
//                   height: 44,
//                   width: 44,
//                   decoration: ShapeDecoration(
//                       shape: const CircleBorder(),
//                       color: theme.colors.backgroundContainer),
//                   child: Center(
//                     child: Icon(
//                       SolarIconsOutline.galleryEdit,
//                       color: theme.colors.primaryText,
//                       size: 24,
//                       applyTextScaling: false,
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }
