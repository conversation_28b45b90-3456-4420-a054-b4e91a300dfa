import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:path/path.dart' as path;
import 'package:pdfx/pdfx.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class AttachmentGridCard extends StatefulWidget {
  final String? filePath;
  final String? url;
  final double width;
  final double height;
  final bool minimal;
  final BoxFit? fit;
  const AttachmentGridCard({
    super.key,
    this.filePath,
    this.url,
    this.width = 114,
    this.height = 100,
    this.fit,
    this.minimal = false,
  }) : assert(filePath != null || url != null,
            'Either filePath or url must be provided');

  @override
  State<AttachmentGridCard> createState() => _AttachmentGridCardState();
}

class _AttachmentGridCardState extends State<AttachmentGridCard> {
  Widget? _thumbnail;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadThumbnail();
  }

  @override
  void didUpdateWidget(AttachmentGridCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.filePath != widget.filePath || oldWidget.url != widget.url) {
      _loadThumbnail();
    }
  }

  Future<void> _loadThumbnail() async {
    print('loading thumbnail');
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _thumbnail = null;
    });

    try {
      Widget thumbnail;
      if (widget.filePath != null) {
        thumbnail = await _buildLocalThumbnail(widget.filePath!);
      } else if (widget.url != null) {
        thumbnail = await _buildRemoteThumbnail(widget.url!);
      } else {
        thumbnail = _buildPlaceholder();
      }

      if (!mounted) return;
      setState(() {
        _thumbnail = thumbnail;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error building thumbnail: $e');
      if (!mounted) return;
      setState(() {
        _thumbnail = _buildPlaceholder();
        _isLoading = false;
      });
    }
  }

  Future<Widget> _buildLocalThumbnail(String filePath) async {
    final extension = path.extension(filePath).toLowerCase();
    debugPrint('making image of $filePath');

    if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
      return Image.file(
        File(filePath),
        fit: widget.fit ?? BoxFit.cover,
        width: widget.width,
        height: widget.height,
        cacheWidth: (widget.width).toInt(),
        cacheHeight: (widget.height).toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } else if (extension == '.pdf') {
      return _buildPdfThumbnail(filePath);
    } else {
      return _buildDocumentThumbnail(extension);
    }
  }

  Future<Widget> _buildRemoteThumbnail(String url) async {
    final extension = path.extension(url).toLowerCase();
    print(widget.filePath);

    if (['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
      return Image.network(
        url,
        fit: widget.fit ?? BoxFit.cover,
        width: widget.width,
        height: widget.height,
        cacheWidth: (widget.width * 3).toInt(),
        cacheHeight: (widget.height * 3).toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } else if (extension == '.pdf') {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/temp_${DateTime.now().millisecondsSinceEpoch}.pdf');

      try {
        final response = await http.get(Uri.parse(url));
        await tempFile.writeAsBytes(response.bodyBytes);
        return _buildPdfThumbnail(tempFile.path);
      } catch (e) {
        debugPrint('Error downloading PDF: $e');
        return _buildPlaceholder();
      }
    } else {
      return _buildDocumentThumbnail(extension);
    }
  }

  Future<Widget> _buildPdfThumbnail(String filePath) async {
    print(widget.filePath);

    try {
      PdfDocument doc = await PdfDocument.openFile(filePath);
      PdfPage page = await doc.getPage(1);

      // Calculate the target size based on the widget dimensions
      final targetWidth = widget.width * 3; // Account for device pixel ratio
      final targetHeight = widget.height * 3;

      // Calculate the aspect ratio to maintain proportions
      final pageAspectRatio = page.width / page.height;
      final targetAspectRatio = widget.width / widget.height;

      double renderWidth, renderHeight;
      if (pageAspectRatio > targetAspectRatio) {
        // Page is wider than target
        renderWidth = targetWidth;
        renderHeight = targetWidth / pageAspectRatio;
      } else {
        // Page is taller than target
        renderHeight = targetHeight;
        renderWidth = targetHeight * pageAspectRatio;
      }

      final pageImage = await page.render(
        width: renderWidth,
        height: renderHeight,
        format: PdfPageImageFormat.jpeg,
        backgroundColor: '#FFFFFF',
      );

      await page.close();
      await doc.close();

      if (pageImage == null) {
        return _buildPlaceholder();
      }

      return Image.memory(
        pageImage.bytes,
        width: widget.width,
        height: widget.height,
        fit: widget.fit ?? BoxFit.contain,
        cacheWidth: renderWidth.toInt(),
        cacheHeight: renderHeight.toInt(),
        errorBuilder: (context, error, stackTrace) => _buildPlaceholder(),
      );
    } catch (e) {
      debugPrint('Error rendering PDF thumbnail: $e');
      return _buildPlaceholder();
    }
  }

  Widget _buildDocumentThumbnail(String extension) {
    IconData iconData;
    switch (extension) {
      case '.doc':
      case '.docx':
        iconData = Icons.description_rounded;
        break;
      case '.xls':
      case '.xlsx':
        iconData = Icons.table_chart_rounded;
        break;
      case '.ppt':
      case '.pptx':
        iconData = Icons.slideshow_rounded;
        break;
      case '.txt':
        iconData = Icons.text_snippet_rounded;
        break;
      default:
        iconData = Icons.insert_drive_file_rounded;
    }

    final theme = AppTheme.of(context);
    return Container(
      color: theme.colors.background,
      child: Icon(
        iconData,
        size: 48,
        color: theme.colors.secondaryText,
      ),
    );
  }

  Widget _buildPlaceholder() {
    final theme = AppTheme.of(context);
    return Container(
      color: theme.colors.background,
      child: Icon(
        Icons.broken_image,
        size: 48,
        color: theme.colors.disabled,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final content = _isLoading
        ? const Center(child: DotsLoadingIndicator())
        : (_thumbnail ?? _buildPlaceholder())
            .animate()
            .fadeIn(duration: 300.milliseconds);
    if (widget.minimal) return content;
    return InkWell(
      // borderRadius: BorderRadius.circular(8),
      // onTap: () async {
      //   final uri = widget.url != null
      //       ? Uri.parse(widget.url!)
      //       : Uri.file(widget.filePath!);
      //   if (await canLaunchUrl(uri)) {
      //     await launchUrl(uri);
      //   }
      // },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.center,
                  child: AnimatedContainer(
                    clipBehavior: Clip.hardEdge,
                    decoration: BoxDecoration(
                      color: _isLoading ? Colors.transparent : Colors.white,
                      border: Border.all(
                          width: 2, color: theme.colors.primaryVariant),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    height: widget.height,
                    width: widget.width,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    foregroundDecoration: BoxDecoration(
                      border: Border.all(
                          width: 2, color: theme.colors.primaryVariant),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    duration: 300.milliseconds,
                    child: content,
                  ),
                ),

                // Positioned(
                //   top: -20,
                //   right: 0,
                //   child: Container(
                //     onPressed: () {},
                //     icon: Icon(Icons.close),
                //   ),
                // )
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Iconsax.document_text_1_copy,
                color: theme.colors.tertiaryText,
                size: 16,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  path.basename(widget.url ?? widget.filePath ?? ''),
                  style: theme.textStyles.body2.copyWith(
                    color: theme.colors.tertiaryText,
                  ),
                  textScaler: TextScaler.noScaling,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
