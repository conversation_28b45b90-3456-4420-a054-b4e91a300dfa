import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme_extensions.dart';

class ColorSelector extends StatelessWidget {
  const ColorSelector({
    super.key,
    required this.colors,
    required this.selectedKey,
    required this.onColorSelected,
    this.validator,
    this.padding,
  });

  final Map<String, Color> colors;
  final String? selectedKey;
  final void Function(String key) onColorSelected;
  final String? Function()? validator;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);

    final content = SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: padding,
      child: Row(
        children: colors.entries.map((entry) {
          final isSelected = selectedKey == entry.key;
          return Padding(
            padding: const EdgeInsetsDirectional.only(end: 8),
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () => onColorSelected(entry.key),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: entry.value,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color:
                        isSelected ? theme.colors.primary : Colors.transparent,
                    width: 3,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );

    if (validator != null) {
      return FormField(
        validator: (_) => validator!(),
        builder: (formState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              content,
              if (formState.hasError)
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 10, 0, 0),
                  child: Text(
                    formState.errorText!,
                    style: theme.textStyles.body3.copyWith(
                      color: theme.colors.error,
                    ),
                  ),
                ),
            ],
          );
        },
      );
    }

    return content;
  }
}
