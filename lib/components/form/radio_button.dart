import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AppRadioButton extends StatelessWidget {
  const AppRadioButton({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.label,
    this.gap = 8,
    this.padding,
    this.toggleable = false,
    this.expand = false,
    this.tile = false,
  });

  const AppRadioButton.tab({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.label,
    this.gap = 8,
    this.padding,
    this.toggleable = false,
  })  : expand = false,
        tile = false;

  const AppRadioButton.tile({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.label,
    this.gap = 8,
    this.padding,
    this.toggleable = false,
  })  : expand = true,
        tile = true;

  final String value;
  final String groupValue;
  final void Function(String? value) onChanged;
  final bool toggleable;

  final String? label;
  final double gap;
  final EdgeInsets? padding;
  final bool expand;
  final bool tile;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final selected = value == groupValue;
    final button = AnimatedContainer(
      duration: 300.milliseconds,
      decoration: ShapeDecoration(
        shape: CircleBorder(
          side: BorderSide(
            color: selected ? theme.colors.primary : theme.colors.tertiaryText,
          ),
        ),
      ),
      padding: const EdgeInsets.all(4),
      child: AnimatedContainer(
        duration: 300.milliseconds,
        height: 10,
        width: 10,
        decoration: ShapeDecoration(
          shape: const CircleBorder(),
          color: selected ? theme.colors.primary : Colors.transparent,
        ),
      ),
    );
    if (label == null) {
      return InkWell(
          splashFactory: NoSplash.splashFactory,
          onTap: () {
            if (selected && toggleable || !selected) {
              onChanged(value);
            }
          },
          child: button);
    }

    final text = Text(
      label!,
      style: theme.textStyles.body.copyWith(
        color: theme.colors.secondaryText,
      ),
    );

    final listItemContents = Padding(
      padding: padding ??
          (tile
              ? const EdgeInsetsGeometry.symmetric(vertical: 8, horizontal: 12)
              : EdgeInsets.zero),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          button,
          SizedBox(width: gap),
          if (expand) Expanded(child: text) else text,
        ],
      ),
    );

    return InkWell(
        splashFactory: NoSplash.splashFactory,
        onTap: () {
          if (selected && toggleable || !selected) {
            onChanged(value);
          }
        },
        child: tile
            ? Container(
                decoration: BoxDecoration(
                    border: Border.all(color: theme.colors.strokeColor),
                    borderRadius: BorderRadius.circular(8)),
                child: listItemContents,
              )
            : listItemContents);
  }
}
