import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../styles/colors.dart';
import '../../styles/text.dart';
import '../../util/ui/popups.dart';

class ChipItem {
  final String label;
  final IconData? icon;
  final Color? color;
  final String? tag;

  const ChipItem({
    required this.label,
    this.icon,
    this.color,
    this.tag,
  });
  @override
  String toString() {
    return label;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChipItem && tag == other.tag;
  }

  @override
  int get hashCode => tag.hashCode;
}

class ChipSelector extends StatelessWidget {
  const ChipSelector({
    super.key,
    required this.items,
    required this.selectedItems,
    required this.onItemTap,
    this.borderColor,
    this.selectedChipColor,
    this.selectedTextColor,
    this.validator,
    this.padding,
    this.unselectedChipColor,
    this.unselectedBorderColor,
    this.selectedBorderColor,
    this.width,
    this.selectedTextStyle,
    this.unselectedTextStyle,
    this.borderRadius,
    this.chipPadding,
    this.chipPaddingBuilder,
  }) : wrapMode = false;

  const ChipSelector.wrap({
    super.key,
    required this.items,
    required this.selectedItems,
    required this.onItemTap,
    this.borderColor,
    this.selectedChipColor,
    this.selectedTextColor,
    this.validator,
    this.unselectedChipColor,
    this.unselectedBorderColor,
    this.selectedBorderColor,
    this.width,
    this.selectedTextStyle,
    this.unselectedTextStyle,
    this.borderRadius,
    this.chipPadding,
    this.chipPaddingBuilder,
  })  : wrapMode = true,
        padding = null;

  final List<ChipItem> items;
  final List<String> selectedItems;
  final void Function(String value) onItemTap;

  final bool wrapMode;
  final Color? borderColor;
  final Color? selectedChipColor;
  final EdgeInsetsGeometry? padding;
  final Color? selectedTextColor;
  final String? Function()? validator;

  // New customization properties
  final Color? unselectedChipColor;
  final Color? unselectedBorderColor;
  final Color? selectedBorderColor;
  final double? width;
  final TextStyle? selectedTextStyle;
  final TextStyle? unselectedTextStyle;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? chipPadding;
  final EdgeInsetsGeometry Function(String)? chipPaddingBuilder;

  @override
  Widget build(BuildContext context) {
    final children = items
        .map((item) => _buildChip(
              context,
              item,
              selectedItems.contains(item.tag ?? item.label),
              () {
                onItemTap(item.tag ?? item.label);
              },
            ))
        .toList();

    final content = wrapMode
        ? Wrap(
            spacing: 8,
            runSpacing: 8,
            children: children,
          )
        : Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: padding,
                  child: Row(
                    children: children.indexed
                        .map((e) => Padding(
                              padding: EdgeInsetsDirectional.only(
                                  end: e.$1 == children.length - 1 ? 0 : 8),
                              child: e.$2,
                            ))
                        .toList(),
                  ),
                ),
              ),
            ],
          );
    if (validator != null) {
      return FormField(validator: (_) {
        return validator!();
      }, builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            content,
            if (_.hasError)
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                child: customErrorText(context, _.errorText),
              )
          ],
        );
      });
    }
    return content;
  }

  Widget _buildChip(BuildContext context, ChipItem item, bool selected,
      void Function() onTap) {
    final theme = AppTheme.of(context);
    final padding = (chipPaddingBuilder == null
        ? (chipPadding ?? const EdgeInsets.all(8))
        : chipPaddingBuilder!(item.tag ?? item.label));
    return InkWell(
      borderRadius: borderRadius ?? BorderRadius.circular(4),
      splashFactory: NoSplash.splashFactory,
      onTap: () => onTap(),
      child: AnimatedContainer(
        duration: 250.milliseconds,
        width: width,
        decoration: BoxDecoration(
            borderRadius: borderRadius ?? BorderRadius.circular(4),
            color: selected
                ? selectedChipColor ?? theme.colors.backgroundContainer
                : unselectedChipColor ?? theme.colors.backgroundContainer,
            border: Border.all(
                color: selected
                    ? selectedBorderColor ?? theme.colors.primary
                    : unselectedBorderColor ?? theme.colors.primaryVariant)),
        padding: padding,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.icon != null) ...[
              Icon(
                item.icon,
                size: 16,
                color: theme.colors.primaryVariantDark,
              ),
              const SizedBox(width: 5),
            ],
            Text(
              item.label,
              style: selected
                  ? selectedTextStyle ??
                      theme.textStyles.body3.copyWith(
                          color: selectedTextColor ?? theme.colors.primaryText)
                  : unselectedTextStyle ??
                      theme.textStyles.body3
                          .copyWith(color: theme.colors.secondaryText),
            ),
          ],
        ),
      ),
    );
  }

  static Widget get skeleton {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(20, 0, 24, 0),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [100.0, 150.0, 70.0]
            .map((e) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Container(
                    width: e,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.blueGrey.withOpacity(0.2),
                    ),
                  ),
                ))
            .toList(),
      ),
    ).animate(onPlay: (c) {
      c.repeat();
    }).shimmer(duration: 2.seconds);
  }
}
