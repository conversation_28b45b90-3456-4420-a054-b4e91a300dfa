import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:remixicon/remixicon.dart';
import 'package:solar_icons/solar_icons.dart';
import '/components/form/util.dart';

class PasswordTextField extends StatefulWidget {
  const PasswordTextField({
    super.key,
    this.heading,
    this.controller,
    this.headingPadding = 8,
    this.initialValue,
    this.decoration,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.style,
    this.strutStyle,
    this.textDirection,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.autofocus = false,
    this.readOnly = false,
    this.showCursor,
    this.obscuringCharacter = '•',
    this.autocorrect = false,
    this.enableSuggestions = true,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.onSaved,
    this.validator,
    this.inputFormatters,
    this.enabled,
    this.cursorColor,
    this.keyboardAppearance,
    this.buildCounter,
    this.scrollPhysics,
    this.autofillHints,
    this.clipBehavior = Clip.hardEdge,
    this.autovalidateMode,
    this.focusNode,
    this.fieldKey,
    this.onTap,
    this.label,
  });

  final String? initialValue;
  final InputDecoration? decoration;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final TextStyle? style;
  final FocusNode? focusNode;
  final Key? fieldKey;
  final String? label;
  final void Function()? onTap;

  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool readOnly;
  final bool? showCursor;
  final String obscuringCharacter;
  final bool autocorrect;
  final bool enableSuggestions;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;
  final void Function(String)? onChanged;
  final void Function()? onEditingComplete;
  final void Function(String)? onFieldSubmitted;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enabled;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final Widget? Function(
    BuildContext, {
    required int currentLength,
    required bool isFocused,
    required int? maxLength,
  })? buildCounter;
  final ScrollPhysics? scrollPhysics;
  final Iterable<String>? autofillHints;
  final Clip clipBehavior;

  final Widget? heading;
  final TextEditingController? controller;
  final double headingPadding;
  final AutovalidateMode? autovalidateMode;

  @override
  State<PasswordTextField> createState() => _PasswordTextFieldState();
}

class _PasswordTextFieldState extends State<PasswordTextField> {
  bool _visible = false;
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.heading != null)
          Padding(
            padding: EdgeInsets.only(
              bottom: widget.headingPadding,
            ),
            child: widget.heading,
          ),
        SimpleTextField(
          key: widget.fieldKey,
          autocorrect: false,
          decoration: InputDecoration(
              labelText: widget.label,
              prefixIcon: Icon(
                SolarIconsOutline.lock,
                size: 24,
              ),
              suffixIcon: (widget.focusNode?.hasFocus ?? true)
                  ? IconButton(
                      onPressed: _toggle,
                      icon: Icon(
                        _visible
                            ? SolarIconsOutline.eye
                            : SolarIconsOutline.eyeClosed,
                        size: 24,
                      ),
                    )
                  : null,
              suffixIconColor: colors.secondaryText),
          obscureText: !_visible,
          keyboardType:
              _visible ? TextInputType.visiblePassword : TextInputType.text,
          obscuringCharacter: '*',
          thick: true,
          autovalidateMode: widget.autovalidateMode,
          focusNode: widget.focusNode,

          controller: widget.controller,
          onChanged: widget.onChanged,
          initialValue: widget.initialValue,
          textCapitalization: widget.textCapitalization,
          textInputAction: widget.textInputAction,
          style: widget.style,
          // AppTextStyle.title,
          // widget.style?.copyWith(
          //   // color: Colors.white,
          //   fontSize: 14,
          //   fontWeight: FontWeight.w500,
          // ),
          strutStyle: widget.strutStyle,
          textDirection: widget.textDirection,
          textAlign: widget.textAlign,
          textAlignVertical: widget.textAlignVertical,
          autofocus: widget.autofocus,
          readOnly: widget.readOnly,
          showCursor: widget.showCursor,
          enableSuggestions: widget.enableSuggestions,
          maxLengthEnforcement: widget.maxLengthEnforcement,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          expands: widget.expands,
          maxLength: widget.maxLength,
          onTap: widget.onTap,
          onEditingComplete: widget.onEditingComplete,
          onFieldSubmitted: widget.onFieldSubmitted,
          onSaved: widget.onSaved,
          validator: widget.validator,
          inputFormatters: widget.inputFormatters,
          enabled: widget.enabled,
          cursorColor: widget.cursorColor,
          keyboardAppearance: widget.keyboardAppearance,
          buildCounter: widget.buildCounter,
          scrollPhysics: widget.scrollPhysics,
          autofillHints: widget.autofillHints,
          clipBehavior: widget.clipBehavior,
        ),
      ],
    );
  }

  void _toggle() {
    setState(() {
      _visible = !_visible;
    });
  }
}
