import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../util/ui/popups.dart';

class TabSelector extends StatelessWidget {
  const TabSelector({
    super.key,
    required this.items,
    required this.selectedItems,
    required this.onItemTap,
    this.borderColor,
    this.selectedChipColor,
    this.selectedTextColor,
    this.validator,
    this.padding,
    this.unselectedChipColor,
    this.unselectedBorderColor,
    this.selectedBorderColor,
    this.width,
    this.selectedTextStyle,
    this.unselectedTextStyle,
    this.borderRadius,
    this.chipPadding,
    this.iconSize,
    this.iconTextGap,
    this.iconColor,
    this.selectedIconColor,
    this.unselectedIconColor,
  });

  final List<ChipItem> items;
  final List<String> selectedItems;
  final void Function(String value) onItemTap;

  final Color? borderColor;
  final Color? selectedChipColor;
  final EdgeInsetsGeometry? padding;
  final Color? selectedTextColor;
  final String? Function()? validator;

  // New customization properties
  final Color? unselectedChipColor;
  final Color? unselectedBorderColor;
  final Color? selectedBorderColor;
  final double? width;
  final TextStyle? selectedTextStyle;
  final TextStyle? unselectedTextStyle;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? chipPadding;
  final double? iconSize;
  final double? iconTextGap;
  final Color? iconColor;
  final Color? selectedIconColor;
  final Color? unselectedIconColor;

  @override
  Widget build(BuildContext context) {
    final children = items
        .map((item) => _buildChip(
              context,
              item,
              selectedItems.contains(item.tag ?? item.label),
              () {
                onItemTap(item.tag ?? item.label);
              },
              iconSize: iconSize,
              iconTextGap: iconTextGap,
              iconColor: iconColor,
              selectedIconColor: selectedIconColor,
              unselectedIconColor: unselectedIconColor,
            ))
        .toList();

    final content = Row(
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        final isLast = index == children.length - 1;

        return Expanded(
          child: Padding(
            padding: EdgeInsetsDirectional.only(
              end: isLast ? 0 : 8,
            ),
            child: child,
          ),
        );
      }).toList(),
    );
    if (validator != null) {
      return FormField(validator: (_) {
        return validator!();
      }, builder: (_) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            content,
            if (_.hasError)
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                child: customErrorText(context, _.errorText),
              )
          ],
        );
      });
    }
    return content;
  }

  Widget _buildChip(
    BuildContext context,
    ChipItem item,
    bool selected,
    void Function() onTap, {
    double? iconSize,
    double? iconTextGap,
    Color? iconColor,
    Color? selectedIconColor,
    Color? unselectedIconColor,
  }) {
    final theme = AppTheme.of(context);

    return InkWell(
      borderRadius: borderRadius ?? BorderRadius.circular(4),
      splashFactory: NoSplash.splashFactory,
      onTap: () => onTap(),
      child: AnimatedContainer(
        duration: 250.milliseconds,
        width: width,
        decoration: BoxDecoration(
            borderRadius: borderRadius ?? BorderRadius.circular(8),
            color: selected
                ? selectedChipColor ?? theme.colors.primaryVariant
                : unselectedChipColor ?? theme.colors.background,
            border: Border.all(
                color: selected
                    ? selectedBorderColor ?? theme.colors.primaryVariant
                    : unselectedBorderColor ?? theme.colors.strokeColor)),
        padding: chipPadding ??
            const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (item.icon != null) ...[
              Icon(
                item.icon,
                size: iconSize ?? 16,
                color: selected
                    ? (selectedIconColor ??
                        iconColor ??
                        theme.colors.primaryVariantDark)
                    : (unselectedIconColor ??
                        iconColor ??
                        theme.colors.primaryVariantDark),
              ),
              SizedBox(width: iconTextGap ?? 5),
            ],
            Text(
              item.label,
              style: selected
                  ? selectedTextStyle ??
                      theme.textStyles.body.copyWith(
                          color: selectedTextColor ?? theme.colors.primary)
                  : unselectedTextStyle ??
                      theme.textStyles.body
                          .copyWith(color: theme.colors.secondaryText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  static Widget get skeleton {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(20, 0, 24, 0),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [100.0, 150.0, 70.0]
            .map((e) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Container(
                    width: e,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.blueGrey.withOpacity(0.2),
                    ),
                  ),
                ))
            .toList(),
      ),
    ).animate(onPlay: (c) {
      c.repeat();
    }).shimmer(duration: 2.seconds);
  }
}
