import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';

class CheckListTile extends StatelessWidget {
  const CheckListTile({
    super.key,
    this.label,
    this.title,
    required this.value,
    required this.onChanged,
    this.padding = const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
    this.checkboxSize = 20.0,
    this.spacing = 8.0,
    this.backgroundColor,
    this.checkSize = 22.0,
    this.checkPosition = 'start',
    this.unselectedCheckColor,
    this.selectedCheckColor,
  });

  final Widget? title;
  final String? label;
  final bool value;
  final ValueChanged<bool?> onChanged;
  final EdgeInsetsGeometry padding;
  final double checkboxSize;
  final double spacing;
  final Color? backgroundColor;
  final Color? unselectedCheckColor;
  final Color? selectedCheckColor;

  final double checkSize;
  final String checkPosition;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final content = [
      SizedBox.square(
        dimension: checkSize,
        child: IgnorePointer(
          child: Checkbox(
            value: value,

            onChanged: onChanged,
            side: BorderSide(
                color: value
                    ? selectedCheckColor ?? theme.colors.primary
                    : unselectedCheckColor ?? theme.colors.tertiaryText),
            checkColor: theme.colors.backgroundContainer,

            shape: RoundedRectangleBorder(
                borderRadius: BorderRadiusGeometry.circular(4)),

            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            // style: ButtonStyle(
            //     padding: WidgetStatePropertyAll(EdgeInsets.all(1))),
          ),
        ),
      ),

      SizedBox(width: spacing), // Use spacing parameter

      if (title != null)
        Expanded(child: title!)
      else
        Expanded(
            child: Text(
          label ?? "",
          style: theme.textStyles.textButton.copyWith(
            color: value ? theme.colors.primary : theme.colors.primaryText,
          ),
        )), // Wrap title in Expanded to handle overflow
    ];
    return InkWell(
      onTap: () {
        onChanged(!value); // Toggle the value on tap
      },
      child: backgroundColor != null
          ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: backgroundColor,
              ),
              padding: padding,
              child: Row(
                children: checkPosition == 'start'
                    ? content
                    : content.reversed.toList(),
              ),
            )
          : Padding(
              padding: padding,
              child: Row(
                children: checkPosition == 'start'
                    ? content
                    : content.reversed.toList(),
              ),
            ),
    );
  }
}
