import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

import 'util.dart';

class SearchTextField extends StatelessWidget {
  const SearchTextField({
    super.key,
    this.controller,
    this.initialValue,
    this.keyboardType,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.style,
    this.strutStyle,
    this.textDirection,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.autofocus = false,
    this.readOnly = false,
    this.showCursor,
    this.obscuringCharacter = '•',
    this.obscureText = false,
    this.autocorrect = true,
    this.enableSuggestions = true,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.expands = false,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.onSaved,
    this.onTap,
    this.validator,
    this.inputFormatters,
    this.enabled,
    this.cursorColor,
    this.keyboardAppearance,
    this.buildCounter,
    this.scrollPhysics,
    this.autofillHints,
    this.clipBehavior = Clip.hardEdge,
    this.autovalidateMode,
    this.focusNode,
    this.fieldKey,
    this.onContainer = false,
    this.hintText,
  });

  final Key? fieldKey;
  final String? initialValue;
  final TextInputType? keyboardType;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final TextStyle? style;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool readOnly;
  final bool? showCursor;
  final String obscuringCharacter;
  final bool obscureText;
  final bool autocorrect;
  final bool enableSuggestions;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final bool expands;
  final int? maxLength;
  final bool onContainer;

  final void Function(String)? onChanged;
  final void Function()? onTap;
  final void Function()? onEditingComplete;
  final void Function(String)? onFieldSubmitted;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enabled;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final Widget? Function(
    BuildContext, {
    required int currentLength,
    required bool isFocused,
    required int? maxLength,
  })? buildCounter;
  final ScrollPhysics? scrollPhysics;
  final Iterable<String>? autofillHints;
  final Clip clipBehavior;

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final AutovalidateMode? autovalidateMode;
  final String? hintText;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final baseInputBorder = OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
    );
    final strings = AppLocalizations.of(context)!;

    return TextFormField(
      key: fieldKey,
      autovalidateMode: autovalidateMode,
      focusNode: focusNode,
      controller: controller,
      onChanged: onChanged,
      initialValue: initialValue,
      keyboardType: keyboardType,
      textCapitalization: textCapitalization,
      textInputAction: textInputAction,
      style: style ?? textStyles.body2.copyWith(color: colors.primaryText),
      strutStyle: strutStyle,
      textDirection: textDirection,
      textAlign: textAlign,
      textAlignVertical: textAlignVertical,
      autofocus: autofocus,
      readOnly: readOnly,
      showCursor: showCursor,
      obscuringCharacter: obscuringCharacter,
      obscureText: obscureText,
      autocorrect: autocorrect,
      enableSuggestions: enableSuggestions,
      maxLengthEnforcement: maxLengthEnforcement,
      maxLines: maxLines,
      minLines: minLines,
      expands: expands,
      maxLength: maxLength,
      onTap: onTap,
      onEditingComplete: onEditingComplete,
      onFieldSubmitted: onFieldSubmitted,
      onSaved: onSaved,
      validator: validator,
      inputFormatters: inputFormatters,
      enabled: enabled,
      cursorColor: cursorColor,
      keyboardAppearance: keyboardAppearance,
      buildCounter: buildCounter,
      scrollPhysics: scrollPhysics,
      autofillHints: autofillHints,
      clipBehavior: clipBehavior,
      decoration: InputDecoration(
        hintText:
            // localize
            hintText ?? strings.searching,
        hintStyle: textStyles.body2.copyWith(color: colors.tertiaryText),
        prefixIcon: const Padding(
          padding: EdgeInsets.only(left: 12, right: 8),
          child: Icon(
            Iconsax.search_normal_copy,
            size: 24,
          ),
        ),
        prefixIconConstraints: const BoxConstraints(
          minWidth: 48,
          minHeight: 32,
        ),
        prefixIconColor: TextFieldStateColor(
          defaultColor: (controller?.text.trim().isNotEmpty ?? false)
              ? colors.primaryText
              : colors.tertiaryText,
          disabledColor: colors.disabled,
          errorColor: colors.error,
          focusedColor: colors.primary,
        ),
        suffixIconConstraints: const BoxConstraints(
          minWidth: 48,
          minHeight: 32,
        ),
        suffixIcon: (controller?.text.trim().isNotEmpty ?? false)
            ? GestureDetector(
                onTap: () {
                  controller?.clear();
                  onChanged?.call('');
                },
                child: Icon(
                  Icons.close_rounded,
                  size: 18,
                  color: colors.primaryText,
                ),
              ).animate().fadeIn()
            : null,
        fillColor: onContainer ? colors.background : colors.backgroundContainer,
        filled: true,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        isDense: true,
        border: baseInputBorder.copyWith(
          borderSide: BorderSide(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        enabledBorder: baseInputBorder.copyWith(
          borderSide: BorderSide(
            color: colors.primaryVariant,
            width: 1,
          ),
        ),
        focusedBorder: baseInputBorder.copyWith(
          borderSide: BorderSide(
            color: colors.primary,
            width: 1,
          ),
        ),
        errorBorder: baseInputBorder.copyWith(
          borderSide: BorderSide(
            color: colors.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: baseInputBorder.copyWith(
          borderSide: BorderSide(
            color: colors.error,
            width: 1,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: colors.disabled,
            width: 1,
          ),
        ),
      ),
    );
  }
}

class TextFieldStateColor extends WidgetStateColor {
  const TextFieldStateColor({
    required this.defaultColor,
    required this.disabledColor,
    required this.errorColor,
    required this.focusedColor,
  }) : super(0);

  final Color defaultColor;
  final Color disabledColor;
  final Color errorColor;
  final Color focusedColor;

  @override
  Color resolve(Set<WidgetState> states) {
    if (states.contains(WidgetState.error)) return errorColor;
    if (states.contains(WidgetState.disabled)) return disabledColor;
    if (states.contains(WidgetState.focused)) return focusedColor;
    return defaultColor;
  }
}
