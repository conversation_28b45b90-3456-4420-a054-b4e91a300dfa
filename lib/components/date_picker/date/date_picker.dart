import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import '../shared/picker_type.dart';
import '../shared/types.dart';
import '../shared/utils.dart';
import 'days_view.dart';
import '../shared/device_orientation_builder.dart';
import '../shared/month_picker.dart';
import '../shared/year_picker.dart';
import 'show_date_picker_dialog.dart';

/// Displays a grid of days for a given month and allows the user to select a
/// date.
///
/// Days are arranged in a rectangular grid with one column for each day of the
/// week. Controls are provided to change the year and month that the grid is
/// showing.
///
/// The date picker widget is rarely used directly. Instead, consider using
/// [showDatePickerDialog], which will create a dialog that uses this.
///
/// See also:
///
///  * [showDatePickerDialog], which creates a Dialog that contains a
///    [DatePicker].
///
class DatePicker extends StatefulWidget {
  /// Creates a calendar date picker.
  ///
  /// It will display a grid of days for the [initialDate]'s month. If [initialDate]
  /// is null, `DateTime.now()` will be used. If `DateTime.now()` does not fall within
  /// the valid range of [minDate] and [maxDate], it will fall back to the nearest
  /// valid date from `DateTime.now()`, selecting the [maxDate] if `DateTime.now()` is
  /// after the valid range, or [minDate] if before.
  ///
  /// The day indicated by [selectedDate] will be selected if provided.
  ///
  /// The optional [onDateSelected] callback will be called if provided when a date
  /// is selected.
  ///
  /// The user interface provides a way to change the year and the month being
  /// displayed. By default it will show the day grid, but this can be changed
  /// with [initialPickerType].
  ///
  /// The [minDate] is the earliest allowable date. The [maxDate] is the latest
  /// allowable date. [initialDate] and [selectedDate] must either fall between
  /// these dates, or be equal to one of them.
  ///
  /// The [currentDate] represents the current day (i.e. today). This
  /// date will be highlighted in the day grid. If null, the date of
  /// `DateTime.now()` will be used.
  ///
  /// For each of these [DateTime] parameters, only
  /// their dates are considered. Their time fields are ignored.
  DatePicker({
    super.key,
    required this.maxDate,
    required this.minDate,
    this.onDateSelected,
    this.onClearTap,
    this.initialDate,
    this.selectedDate,
    this.currentDate,
    this.padding = const EdgeInsets.all(16),
    this.initialPickerType = PickerType.days,
    this.daysOfTheWeekTextStyle,
    this.enabledCellsTextStyle,
    this.enabledCellsDecoration = const BoxDecoration(),
    this.disabledCellsTextStyle,
    this.disabledCellsDecoration = const BoxDecoration(),
    this.currentDateTextStyle,
    this.currentDateDecoration,
    this.selectedCellTextStyle,
    this.selectedCellDecoration,
    this.leadingDateTextStyle,
    this.slidersColor,
    this.slidersSize,
    this.highlightColor,
    this.splashColor,
    this.splashRadius,
    this.centerLeadingDate = false,
    this.previousPageSemanticLabel,
    this.nextPageSemanticLabel,
    this.disabledDayPredicate,
  }) {
    assert(!minDate.isAfter(maxDate), "minDate can't be after maxDate");
  }

  /// The date which will be displayed on first opening. If not specified, the picker
  /// will default to `DateTime.now()`. If `DateTime.now()` does not fall within the
  /// valid range of [minDate] and [maxDate], it will automatically adjust to the nearest
  /// valid date, selecting [maxDate] if `DateTime.now()` is after the valid range, or
  /// [minDate] if it is before.
  ///
  /// Note that only dates are considered. time fields are ignored.
  final DateTime? initialDate;

  /// The date to which the picker will consider as current date. e.g (today).
  /// If not specified, the picker will default to `DateTime.now()` date.
  ///
  /// Note that only dates are considered. time fields are ignored.
  final DateTime? currentDate;

  /// The initially selected date when the picker is first opened.
  ///
  /// Note that only dates are considered. time fields are ignored.
  final DateTime? selectedDate;

  /// Called when the user picks a date.
  final ValueChanged<DateTime>? onDateSelected;
  final VoidCallback? onClearTap;

  /// The earliest date the user is permitted to pick.
  ///
  /// This date must be on or before the [maxDate].
  ///
  /// Note that only dates are considered. time fields are ignored.
  final DateTime minDate;

  /// The latest date the user is permitted to pick.
  ///
  /// This date must be on or after the [minDate].
  ///
  /// Note that only dates are considered. time fields are ignored.
  final DateTime maxDate;

  /// The initial display of the calendar picker.
  final PickerType initialPickerType;

  /// The amount of padding to be added around the [DatePicker].
  final EdgeInsets padding;

  /// The text style of the days of the week in the header.
  ///
  /// defaults to [TextTheme.titleSmall] with a [FontWeight.bold],
  /// a `14` font size, and a [ColorScheme.onSurface] with 30% opacity.
  final TextStyle? daysOfTheWeekTextStyle;

  /// The text style of cells which are selectable.
  ///
  /// defaults to [TextTheme.titleLarge] with a [FontWeight.normal]
  /// and [ColorScheme.onSurface] color.
  final TextStyle? enabledCellsTextStyle;

  /// The cell decoration of cells which are selectable.
  ///
  /// defaults to empty [BoxDecoration].
  final BoxDecoration enabledCellsDecoration;

  /// The text style of cells which are not selectable.
  ///
  /// defaults to [TextTheme.titleLarge] with a [FontWeight.normal]
  /// and [ColorScheme.onSurface] color with 30% opacity.
  final TextStyle? disabledCellsTextStyle;

  /// The cell decoration of cells which are not selectable.
  ///
  /// defaults to empty [BoxDecoration].
  final BoxDecoration disabledCellsDecoration;

  /// The text style of the current date.
  ///
  /// defaults to [TextTheme.titleLarge] with a [FontWeight.normal]
  /// and [ColorScheme.primary] color.
  final TextStyle? currentDateTextStyle;

  /// The cell decoration of the current date.
  ///
  /// defaults to circle stroke border with [ColorScheme.primary] color.
  final BoxDecoration? currentDateDecoration;

  /// The text style of selected cell.
  ///
  /// defaults to [TextTheme.titleLarge] with a [FontWeight.normal]
  /// and [ColorScheme.onPrimary] color.
  final TextStyle? selectedCellTextStyle;

  /// The cell decoration of selected cell.
  ///
  /// defaults to circle with [ColorScheme.primary] color.
  final BoxDecoration? selectedCellDecoration;

  /// The text style of leading date showing in the header.
  ///
  /// defaults to `18px` with a [FontWeight.bold]
  /// and [ColorScheme.primary] color.
  final TextStyle? leadingDateTextStyle;

  /// The color of the page sliders.
  ///
  /// defaults to [ColorScheme.primary] color.
  final Color? slidersColor;

  /// The size of the page sliders.
  ///
  /// defaults to `20px`.
  final double? slidersSize;

  /// The splash color of the ink response.
  ///
  /// defaults to the color of [selectedCellDecoration] with 30% opacity,
  /// if [selectedCellDecoration] is null will fall back to
  /// [ColorScheme.onPrimary] with 30% opacity.
  final Color? splashColor;

  /// The highlight color of the ink response when pressed.
  ///
  /// defaults to the color of [selectedCellDecoration] with 30% opacity,
  /// if [selectedCellDecoration] is null will fall back to
  /// [ColorScheme.onPrimary] with 30% opacity.
  final Color? highlightColor;

  /// The radius of the ink splash.
  final double? splashRadius;

  /// Centring the leading date. e.g:
  ///
  /// <       December 2023      >
  ///
  final bool centerLeadingDate;

  /// Semantic label for button to go to the previous page.
  ///
  /// defaults to `Previous Day/Month/Year` according to picker type.
  final String? previousPageSemanticLabel;

  /// Semantic label for button to go to the next page.
  ///
  /// defaults to `Next Day/Month/Year` according to picker type.
  final String? nextPageSemanticLabel;

  /// A predicate function used to determine if a given day should be disabled.
  final DatePredicate? disabledDayPredicate;

  @override
  State<DatePicker> createState() => _DatePickerState();
}

class _DatePickerState extends State<DatePicker> {
  PickerType? _pickerType;
  DateTime? _displayedDate;
  DateTime? _selectedDate;

  // Days view state
  final GlobalKey _daysPageViewKey = GlobalKey();
  late final PageController _daysPageController;
  int _currentPageIndex = 0;

  bool get _isNextMonthNavigable {
    return _currentPageIndex <
        DateUtils.monthDelta(widget.minDate, widget.maxDate);
  }

  bool get _isPreviousMonthNavigable {
    return _currentPageIndex > 0;
  }

  bool get _isNextYearNavigable {
    final displayedMonth =
        DateUtils.addMonthsToMonthDate(widget.minDate, _currentPageIndex);
    final nextYear = DateTime(displayedMonth.year + 1, displayedMonth.month);
    return !nextYear.isAfter(widget.maxDate);
  }

  bool get _isPreviousYearNavigable {
    final displayedMonth =
        DateUtils.addMonthsToMonthDate(widget.minDate, _currentPageIndex);
    final prevYear = DateTime(displayedMonth.year - 1, displayedMonth.month);
    return !prevYear.isBefore(widget.minDate);
  }

  @override
  void initState() {
    final clampedInitailDate = DateUtilsX.clampDateToRange(
        max: widget.maxDate, min: widget.minDate, date: DateTime.now());
    _displayedDate =
        DateUtils.dateOnly(widget.initialDate ?? clampedInitailDate);
    _pickerType = widget.initialPickerType;
    _selectedDate = widget.selectedDate != null
        ? DateUtils.dateOnly(widget.selectedDate!)
        : null;
    // Days view state
    _currentPageIndex = DateUtils.monthDelta(widget.minDate, _displayedDate!);
    _daysPageController = PageController(
      initialPage: _currentPageIndex,
    );
    super.initState();
  }

  @override
  void didUpdateWidget(covariant DatePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialDate != widget.initialDate) {
      final clampedInitailDate = DateUtilsX.clampDateToRange(
          max: widget.maxDate, min: widget.minDate, date: DateTime.now());
      _displayedDate =
          DateUtils.dateOnly(widget.initialDate ?? clampedInitailDate);
      _currentPageIndex = DateUtils.monthDelta(widget.minDate, _displayedDate!);
      // Only jump, do not recreate controller!
      _daysPageController.jumpToPage(_currentPageIndex);
    }
    if (oldWidget.initialPickerType != widget.initialPickerType) {
      _pickerType = widget.initialPickerType;
    }
    if (oldWidget.selectedDate != widget.selectedDate) {
      _selectedDate = widget.selectedDate != null
          ? DateUtils.dateOnly(widget.selectedDate!)
          : null;
    }
  }

  @override
  void dispose() {
    _daysPageController.dispose();
    super.dispose();
  }

  void _handleMonthPageChange(bool increase) {
    final DateTime displayedMonth =
        DateUtils.addMonthsToMonthDate(widget.minDate, _currentPageIndex);
    if (increase) {
      final nextMonth = DateTime(displayedMonth.year, displayedMonth.month + 1);
      final min = DateUtils.dateOnly(widget.minDate);
      final max = DateUtils.dateOnly(widget.maxDate);
      final clamped =
          DateUtilsX.clampDateToRange(date: nextMonth, min: min, max: max);
      final targetPage = DateUtils.monthDelta(widget.minDate, clamped);
      if (targetPage != _currentPageIndex) {
        _daysPageController.animateToPage(
          targetPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );
      }
    } else {
      final prevMonth = DateTime(displayedMonth.year, displayedMonth.month - 1);
      final min = DateUtils.dateOnly(widget.minDate);
      final max = DateUtils.dateOnly(widget.maxDate);
      final clamped =
          DateUtilsX.clampDateToRange(date: prevMonth, min: min, max: max);
      final targetPage = DateUtils.monthDelta(widget.minDate, clamped);
      if (targetPage != _currentPageIndex) {
        _daysPageController.animateToPage(
          targetPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );
      }
    }
  }

  void _handleYearPageChange(bool increase) {
    final DateTime displayedMonth =
        DateUtils.addMonthsToMonthDate(widget.minDate, _currentPageIndex);
    // Jump to previous year (no animation)
    if (increase) {
      final nextYear = DateTime(displayedMonth.year + 1, displayedMonth.month);
      final min = DateUtils.dateOnly(widget.minDate);
      final max = DateUtils.dateOnly(widget.maxDate);
      final clamped =
          DateUtilsX.clampDateToRange(date: nextYear, min: min, max: max);
      final targetPage = DateUtils.monthDelta(widget.minDate, clamped);
      if (targetPage != _currentPageIndex) {
        _daysPageController.jumpToPage(targetPage);
      }
    } else {
      final prevYear = DateTime(displayedMonth.year - 1, displayedMonth.month);
      final min = DateUtils.dateOnly(widget.minDate);
      final max = DateUtils.dateOnly(widget.maxDate);
      final clamped =
          DateUtilsX.clampDateToRange(date: prevYear, min: min, max: max);
      final targetPage = DateUtils.monthDelta(widget.minDate, clamped);
      if (targetPage != _currentPageIndex) {
        _daysPageController.jumpToPage(targetPage);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final pages = switch (_pickerType!) {
      PickerType.days => Padding(
          padding: widget.padding,
          child: _buildDaysView(context),
        ),
      PickerType.months => Padding(
          padding: widget.padding,
          child: MonthPicker(
            centerLeadingDate: widget.centerLeadingDate,
            initialDate: _displayedDate,
            selectedDate: _selectedDate,
            currentDate:
                DateUtils.dateOnly(widget.currentDate ?? DateTime.now()),
            maxDate: DateUtils.dateOnly(widget.maxDate),
            minDate: DateUtils.dateOnly(widget.minDate),
            currentDateDecoration: widget.currentDateDecoration,
            currentDateTextStyle: widget.currentDateTextStyle,
            disabledCellsDecoration: widget.disabledCellsDecoration,
            disabledCellsTextStyle: widget.disabledCellsTextStyle,
            enabledCellsDecoration: widget.enabledCellsDecoration,
            enabledCellsTextStyle: widget.enabledCellsTextStyle,
            selectedCellDecoration: widget.selectedCellDecoration,
            selectedCellTextStyle: widget.selectedCellTextStyle,
            slidersColor: widget.slidersColor,
            slidersSize: widget.slidersSize,
            leadingDateTextStyle: widget.leadingDateTextStyle,
            splashColor: widget.splashColor,
            highlightColor: widget.highlightColor,
            splashRadius: widget.splashRadius,
            previousPageSemanticLabel: widget.previousPageSemanticLabel,
            nextPageSemanticLabel: widget.nextPageSemanticLabel,
            onLeadingDateTap: () {
              setState(() {
                _pickerType = PickerType.years;
              });
            },
            onDateSelected: (selectedMonth) {
              // clamped the initial date to fall between min and max date.
              final clampedSelectedMonth = DateUtilsX.clampDateToRange(
                min: widget.minDate,
                max: widget.maxDate,
                date: selectedMonth,
              );
              final newPage =
                  DateUtils.monthDelta(widget.minDate, clampedSelectedMonth);
              setState(() {
                _displayedDate = clampedSelectedMonth;
                _pickerType = PickerType.days;
                _currentPageIndex = newPage;
              });
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (_daysPageController.hasClients) {
                  _daysPageController.jumpToPage(newPage);
                }
              });
            },
          ),
        ),
      PickerType.years => Padding(
          padding: widget.padding,
          child: YearsPicker(
            centerLeadingDate: widget.centerLeadingDate,
            initialDate: _displayedDate,
            selectedDate: _selectedDate,
            currentDate:
                DateUtils.dateOnly(widget.currentDate ?? DateTime.now()),
            maxDate: DateUtils.dateOnly(widget.maxDate),
            minDate: DateUtils.dateOnly(widget.minDate),
            currentDateDecoration: widget.currentDateDecoration,
            currentDateTextStyle: widget.currentDateTextStyle,
            disabledCellsDecoration: widget.disabledCellsDecoration,
            disabledCellsTextStyle: widget.disabledCellsTextStyle,
            enabledCellsDecoration: widget.enabledCellsDecoration,
            enabledCellsTextStyle: widget.enabledCellsTextStyle,
            selectedCellDecoration: widget.selectedCellDecoration,
            selectedCellTextStyle: widget.selectedCellTextStyle,
            slidersColor: widget.slidersColor,
            slidersSize: widget.slidersSize,
            leadingDateTextStyle: widget.leadingDateTextStyle,
            splashColor: widget.splashColor,
            highlightColor: widget.highlightColor,
            splashRadius: widget.splashRadius,
            previousPageSemanticLabel: widget.previousPageSemanticLabel,
            nextPageSemanticLabel: widget.nextPageSemanticLabel,
            onDateSelected: (selectedYear) {
              // clamped the initial date to fall between min and max date.
              final clampedSelectedYear = DateUtilsX.clampDateToRange(
                min: widget.minDate,
                max: widget.maxDate,
                date: selectedYear,
              );
              final newPage =
                  DateUtils.monthDelta(widget.minDate, clampedSelectedYear);
              setState(() {
                _displayedDate = clampedSelectedYear;
                _pickerType = PickerType.months;
                _currentPageIndex = newPage;
              });
            },
          ),
        ),
    };

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // my custom header..
        SizedBox(
          height: 64,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: (_pickerType == PickerType.days &&
                                  _isPreviousMonthNavigable)
                              ? () => _handleMonthPageChange(false)
                              : null,
                          icon: Icon(
                            Icons.navigate_before,
                            size: 24,
                            color: (_pickerType == PickerType.days &&
                                    _isPreviousMonthNavigable)
                                ? theme.colors.primaryText
                                : theme.colors.tertiaryText,
                          ),
                          visualDensity: VisualDensity.compact,
                        ),
                        Expanded(
                          child: InkWell(
                            splashFactory: NoSplash.splashFactory,
                            onTap: () {
                              setState(() {
                                if (_pickerType == PickerType.months) {
                                  _pickerType = PickerType.days;
                                } else {
                                  _pickerType = PickerType.months;
                                }
                              });
                            },
                            child: Center(
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    10, 8, 4, 8),
                                child: Row(
                                  children: [
                                    Builder(
                                      builder: (context) {
                                        final DateTime displayedMonth =
                                            DateUtils.addMonthsToMonthDate(
                                                widget.minDate,
                                                _currentPageIndex);
                                        return Text(
                                          getMonths(context, short: true)[
                                              displayedMonth.month - 1],
                                          style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500),
                                          textScaler: TextScaler.noScaling,
                                        );
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: AnimatedRotation(
                                        turns: _pickerType == PickerType.months
                                            ? 0.5
                                            : 0.0,
                                        duration:
                                            const Duration(milliseconds: 200),
                                        child: const Icon(
                                          Icons.arrow_drop_down,
                                          size: 18,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: (_pickerType == PickerType.days &&
                                  _isNextMonthNavigable)
                              ? () => _handleMonthPageChange(true)
                              : null,
                          icon: Icon(
                            Icons.navigate_next,
                            size: 24,
                            color: (_pickerType == PickerType.days &&
                                    _isNextMonthNavigable)
                                ? theme.colors.primaryText
                                : theme.colors.tertiaryText,
                          ),
                          visualDensity: VisualDensity.compact,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: (_pickerType == PickerType.days &&
                                  _isPreviousYearNavigable)
                              ? () => _handleYearPageChange(false)
                              : null,
                          icon: Icon(
                            Icons.navigate_before,
                            size: 24,
                            color: (_pickerType == PickerType.days &&
                                    _isPreviousYearNavigable)
                                ? theme.colors.primaryText
                                : theme.colors.tertiaryText,
                          ),
                          visualDensity: VisualDensity.compact,
                        ),
                        Expanded(
                          child: InkWell(
                            splashFactory: NoSplash.splashFactory,
                            onTap: () {
                              setState(() {
                                if (_pickerType == PickerType.years) {
                                  _pickerType = PickerType.days;
                                } else {
                                  _pickerType = PickerType.years;
                                }
                              });
                            },
                            child: Center(
                              child: Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    10, 8, 4, 8),
                                child: Row(
                                  children: [
                                    Builder(
                                      builder: (context) {
                                        final DateTime displayedMonth =
                                            DateUtils.addMonthsToMonthDate(
                                                widget.minDate,
                                                _currentPageIndex);
                                        return Text(
                                          displayedMonth.year.toString(),
                                          style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500),
                                          textScaler: TextScaler.noScaling,
                                        );
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    AnimatedRotation(
                                      turns: _pickerType == PickerType.years
                                          ? 0.5
                                          : 0.0,
                                      duration: Duration(milliseconds: 200),
                                      child: Icon(
                                        Icons.arrow_drop_down,
                                        size: 18,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: (_pickerType == PickerType.days &&
                                  _isNextYearNavigable)
                              ? () => _handleYearPageChange(true)
                              : null,
                          icon: Icon(
                            Icons.navigate_next,
                            size: 24,
                            color: (_pickerType == PickerType.days &&
                                    _isNextYearNavigable)
                                ? theme.colors.primaryText
                                : theme.colors.tertiaryText,
                          ),
                          visualDensity: VisualDensity.compact,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Expanded(child: pages),
        SizedBox(
          height: 56,
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  TextButton(
                      onPressed: () {
                        widget.onClearTap?.call();
                        Navigator.pop(context, null);
                      },
                      child: Text(
                        strings.clear,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      )),
                  Spacer(),
                  TextButton(
                      onPressed: () {
                        Navigator.pop(context, null);
                      },
                      child: Text(
                        strings.cancel,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      )),
                  TextButton(
                      onPressed: _selectedDate != null
                          ? () {
                              widget.onDateSelected?.call(_selectedDate!);
                            }
                          : null,
                      child: Text(
                        strings.ok,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ))
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDaysView(BuildContext context) {
    final theme = AppTheme.of(context);
    final TextStyle daysOfTheWeekTextStyle = widget.daysOfTheWeekTextStyle ??
        theme.textStyles.body.copyWith(
          color: theme.colors.primaryText,
        );
    final TextStyle enabledCellsTextStyle = widget.enabledCellsTextStyle ??
        theme.textStyles.body.copyWith(
          color: theme.colors.primaryText,
        );
    final BoxDecoration enabledCellsDecoration = widget.enabledCellsDecoration;
    final TextStyle disabledCellsTextStyle = widget.disabledCellsTextStyle ??
        theme.textStyles.body.copyWith(
          color: theme.colors.primaryText.withOpacity(0.30),
        );
    final BoxDecoration disabledCellsDecoration =
        widget.disabledCellsDecoration;
    final TextStyle currentDateTextStyle = widget.currentDateTextStyle ??
        theme.textStyles.body.copyWith(
          color: theme.colors.primary,
        );
    final BoxDecoration currentDateDecoration = widget.currentDateDecoration ??
        BoxDecoration(
          border: Border.all(color: theme.colors.primary),
          shape: BoxShape.circle,
        );
    final TextStyle selectedCellTextStyle = widget.selectedCellTextStyle ??
        theme.textStyles.body.copyWith(
          color: Colors.white,
        );
    final BoxDecoration selectedCellDecoration =
        widget.selectedCellDecoration ??
            BoxDecoration(
              color: theme.colors.primary,
              shape: BoxShape.circle,
            );
    final TextStyle leadingDateTextStyle = widget.leadingDateTextStyle ??
        TextStyle(
          color: theme.colors.primary,
        );
    final slidersColor = widget.slidersColor ?? theme.colors.primary;
    final slidersSize = widget.slidersSize ?? 20;
    final splashColor = widget.splashColor ??
        selectedCellDecoration.color?.withOpacity(0.3) ??
        theme.colors.primary.withOpacity(0.3);
    final highlightColor = widget.highlightColor ??
        selectedCellDecoration.color?.withOpacity(0.3) ??
        theme.colors.primary.withOpacity(0.3);
    final int totalMonths =
        DateUtils.monthDelta(widget.minDate, widget.maxDate) + 1;
    final DateTime displayedMonth =
        DateUtils.addMonthsToMonthDate(widget.minDate, _currentPageIndex);

    return DeviceOrientationBuilder(builder: (context, o) {
      late final Size size;
      switch (o) {
        case Orientation.portrait:
          size = const Size(328.0, 402.0);
          break;
        case Orientation.landscape:
          size = const Size(328.0, 300.0);
          break;
      }
      return LimitedBox(
        maxHeight: size.height,
        maxWidth: size.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header(
            //   centerLeadingDate: widget.centerLeadingDate,
            //   leadingDateTextStyle: leadingDateTextStyle,
            //   slidersColor: slidersColor,
            //   slidersSize: slidersSize,
            //   onDateTap: () => setState(() => _pickerType = PickerType.months),
            //   displayedDate: MaterialLocalizations.of(context)
            //       .formatMonthYear(displayedMonth)
            //       .replaceAll('٩', '9')
            //       .replaceAll('٨', '8')
            //       .replaceAll('٧', '7')
            //       .replaceAll('٦', '6')
            //       .replaceAll('٥', '5')
            //       .replaceAll('٤', '4')
            //       .replaceAll('٣', '3')
            //       .replaceAll('٢', '2')
            //       .replaceAll('١', '1')
            //       .replaceAll('٠', '0'),
            //   onNextPage: () {
            //     // Animate for month change
            //     if (_currentPageIndex < totalMonths - 1) {
            //       _daysPageController.nextPage(
            //         duration: const Duration(milliseconds: 300),
            //         curve: Curves.ease,
            //       );
            //     }
            //   },
            //   onPreviousPage: () {
            //     // Animate for month change
            //     if (_currentPageIndex > 0) {
            //       _daysPageController.previousPage(
            //         duration: const Duration(milliseconds: 300),
            //         curve: Curves.ease,
            //       );
            //     }
            //   },
            //   previousPageSemanticLabel: widget.previousPageSemanticLabel,
            //   nextPageSemanticLabel: widget.nextPageSemanticLabel,
            // ),
            // const SizedBox(height: 10),
            Expanded(
              child: PageView.builder(
                scrollDirection: Axis.horizontal,
                key: _daysPageViewKey,
                controller: _daysPageController,
                itemCount: totalMonths,
                onPageChanged: (monthPage) {
                  setState(() {
                    _currentPageIndex = monthPage;
                  });
                },
                itemBuilder: (context, index) {
                  final DateTime month =
                      DateUtils.addMonthsToMonthDate(widget.minDate, index);
                  return DaysView(
                    key: ValueKey<DateTime>(month),
                    currentDate: DateUtils.dateOnly(
                        widget.currentDate ?? DateTime.now()),
                    maxDate: DateUtils.dateOnly(widget.maxDate),
                    minDate: DateUtils.dateOnly(widget.minDate),
                    displayedMonth: month,
                    selectedDate: _selectedDate,
                    daysOfTheWeekTextStyle: daysOfTheWeekTextStyle,
                    enabledCellsTextStyle: enabledCellsTextStyle,
                    enabledCellsDecoration: enabledCellsDecoration,
                    disabledCellsTextStyle: disabledCellsTextStyle,
                    disabledCellsDecoration: disabledCellsDecoration,
                    currentDateDecoration: currentDateDecoration,
                    currentDateTextStyle: currentDateTextStyle,
                    selectedDayDecoration: selectedCellDecoration,
                    selectedDayTextStyle: selectedCellTextStyle,
                    highlightColor: highlightColor,
                    splashColor: splashColor,
                    splashRadius: widget.splashRadius,
                    disabledDayPredicate: widget.disabledDayPredicate,
                    onChanged: (value) {
                      setState(() {
                        _selectedDate = value;
                        _displayedDate = value;
                      });
                    },
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}
