class EmployeeData {
  final int id;
  final String imageUrl;
  final String name;
  final String contract;
  final String jobTitle;
  final String department;
  final dynamic departmentColor; // You may want to use Color if importing 'dart:ui' or 'package:flutter/material.dart'
  final String? phone;
  final String? email;
  final DateTime? dateOfJoining;
  final DateTime? dateOfResignation;
  final String? country;
  final String? city;
  final String? address;
  final String? zipCode;

  const EmployeeData({
    required this.id,
    required this.imageUrl,
    required this.name,
    required this.contract,
    required this.jobTitle,
    required this.department,
    this.departmentColor,
    this.phone,
    this.email,
    this.dateOfJoining,
    this.dateOfResignation,
    this.country,
    this.city,
    this.address,
    this.zipCode,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EmployeeData &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          imageUrl == other.imageUrl &&
          name == other.name &&
          contract == other.contract &&
          jobTitle == other.jobTitle &&
          department == other.department &&
          departmentColor == other.departmentColor &&
          phone == other.phone &&
          email == other.email &&
          dateOfJoining == other.dateOfJoining &&
          dateOfResignation == other.dateOfResignation &&
          country == other.country &&
          city == other.city &&
          address == other.address &&
          zipCode == other.zipCode;

  @override
  int get hashCode =>
      id.hashCode ^
      imageUrl.hashCode ^
      name.hashCode ^
      contract.hashCode ^
      jobTitle.hashCode ^
      department.hashCode ^
      departmentColor.hashCode ^
      phone.hashCode ^
      email.hashCode ^
      dateOfJoining.hashCode ^
      dateOfResignation.hashCode ^
      country.hashCode ^
      city.hashCode ^
      address.hashCode ^
      zipCode.hashCode;

  int compareTo(EmployeeData other) {
    // Compare by name, then by id if names are equal
    final nameComparison = name.compareTo(other.name);
    if (nameComparison != 0) {
      return nameComparison;
    }
    return id.compareTo(other.id);
  }
}
