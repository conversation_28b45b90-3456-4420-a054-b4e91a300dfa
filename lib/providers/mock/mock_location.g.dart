// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mock_location.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mockDeviceLocationHash() =>
    r'742832a9477f394e71a57ee6ed6867e598e9ce8e';

/// See also [MockDeviceLocation].
@ProviderFor(MockDeviceLocation)
final mockDeviceLocationProvider =
    NotifierProvider<MockDeviceLocation, LatLng?>.internal(
  MockDeviceLocation.new,
  name: r'mockDeviceLocationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mockDeviceLocationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MockDeviceLocation = Notifier<LatLng?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
