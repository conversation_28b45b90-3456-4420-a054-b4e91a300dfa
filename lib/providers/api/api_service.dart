import 'package:hive_flutter/hive_flutter.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../util/hive/hive_util.dart';
import 'api_client.dart';

part 'api_service.g.dart';

@riverpod
class ApiService extends _$ApiService {
  @override
  ApiClient build() {
    _initListener();
    final token = HiveUtils.token();
    return token != null ? ApiClient.withToken(token) : ApiClient();
  }

  void _initListener() {
    Hive.box(HiveUtils.accBoxKey)
        .listenable(keys: [HiveUtils.tokenKey]).addListener(() {
      final token = HiveUtils.token();
      state = token != null ? ApiClient.withToken(token) : ApiClient();
    });
  }

  void setTempToken(String token) {
    state = ApiClient.withToken(token);
  }
}
