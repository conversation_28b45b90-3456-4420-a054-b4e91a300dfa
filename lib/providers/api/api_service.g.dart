// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$apiServiceHash() => r'1a3e7a1ba858ec05a43b8fc09f8ab377b60e18f4';

/// See also [ApiService].
@ProviderFor(ApiService)
final apiServiceProvider =
    AutoDisposeNotifierProvider<ApiService, ApiClient>.internal(
  ApiService.new,
  name: r'apiServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$apiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ApiService = AutoDisposeNotifier<ApiClient>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
