import 'package:ako_basma/providers/auth/auth.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../util/hive/hive_util.dart';
import '../../i18n/util/remote_translation_service.dart';
import '../locale/locale_provider.dart';

part 'login_state.g.dart';

enum LoginStates {
  loggedOut,
  notAllowed,
  allowed,
  waiting,
  expired,
  error,
  offline,
}

@riverpod
class LoginState extends _$LoginState {
  @override
  LoginStates build() {
    final token = HiveUtils.token();
    if (kDebugMode) {
      print('found token = $token');
    }
    if (token == null) {
      return LoginStates.loggedOut;
    } else {
      refreshLoginState();
      return LoginStates.waiting;
    }
  }

  /// call this when you are just
  /// logging in,, signing in.,,, or the app is started...
  /// AFTER setting token..
  /// if token isnt there - it goes to logged out
  /// if tokens there.. it gets server profile.. and checks for isVerified..
  /// and selects the apppropriate state. and sets it.
  Future<void> refreshLoginState({
    LoginStates? overrideState,
    String? accessToken,
  }) async {
    if (overrideState != null) {
      state = overrideState;
      return;
    }
    final token = accessToken ?? HiveUtils.token();
    if (token == null) {
      state = LoginStates.loggedOut;
      return;
    }

    // commenting this for now.. because i dont wanna show a diff loading screen now..
    // state = LoginStates.waiting;
    bool allowed = false;
    try {
      final ic = InternetConnection.createInstance(
          useDefaultOptions: false,
          customCheckOptions: [
            //for CN
            'https://www.baidu.com',
            'https://wechat.com',

            // website
            //TODO: Add a server url here

            //default list
            'https://google.com',
            'https://one.one.one.one',
            'https://icanhazip.com/',
            'https://jsonplaceholder.typicode.com/todos/1',
            'https://reqres.in/api/users/1',
          ].map((url) => InternetCheckOption(uri: Uri.parse(url))).toList());

      // TODO change it to enable internet access check.
      // bool hasInternetAccess = await ic.hasInternetAccess;
      // if (!hasInternetAccess) {
      //   state = LoginStates.offline;
      //   return;
      // }

      // Initialize translations with current locale
      final locale = ref.read(localeProvider);
      await remoteStrings.initialize(locale);

      final profile =
          await ref.read(authStateProvider.notifier).refreshDeviceProfile();

      /// We may add any login restrictions here
      allowed = true;

      // profile.isVerified ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('login state init logic/');
        print(e);
      }
      // TODO
      // later on add a translation  error page too.
      if (e is DioException) {
        final code = (e).response?.statusCode;
        if (code == 404 || code == 401) {
          if (kDebugMode) {
            print('setting expired screen....');
          }
          state = LoginStates.expired;
          return;
        }
      }
      if (kDebugMode) {
        print('setting error screen....');
      }

      state = LoginStates.error;
      return;
    }

    state = allowed ? LoginStates.allowed : LoginStates.notAllowed;
  }
}
