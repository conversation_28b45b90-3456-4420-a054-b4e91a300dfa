import 'package:flutter/foundation.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../api/api_client.dart';
import '../api/api_service.dart';
import '../../model/profile.dart';
import '../../util/hive/hive_util.dart';
import '../../util/notification/push_notification.dart';
import 'login_state.dart';

part 'auth.g.dart';

@riverpod
class AuthState extends _$AuthState {
  @override
  Profile? build() {
    final token = HiveUtils.token();
    final profile = HiveUtils.profile();
    if (kDebugMode) {
      print(token);
    }
    return profile;
  }

  /// for logging in, sends verification otp
  Future<void> sendLoginOtp(String email, String password) async {
    final result =
        await ref.read(apiServiceProvider).loginWithEmail(email, password);

    // store token
    final profile = result.$1;
    final token = result.$2;

    await HiveUtils.setToken(token);
    ref.read(loginStateProvider.notifier).refreshLoginState();
    await refreshDeviceProfile();
  }

  Future<void> loginWithCredentials({
    String? email,
    String? phone,
    required String password,

    /// this would be called after the login is successful with credentials and before refreshing the device profile.
    /// In this gap, verify the login mode with verification code (2FA). this function should give true if the code is validated
    /// and user is allowed to use the app.
    required Future<bool> Function()? onLoginVerification,
    bool storeToken = true,
  }) async {
    // final result = email != null
    // ? await ref.read(apiServiceProvider).loginWithEmail(email, password)
    // : await ref.read(apiServiceProvider).loginWithPhone(phone!, password);

    // store token
    // final profile = result.$1;
    // final token = result.$2;
    final token = 'testtoken';

    final allowed =
        onLoginVerification != null ? (await onLoginVerification()) : true;
    // final allowed = kDebugMode ? true : await onLoginVerification();

    if (allowed) {
      if (storeToken) {
        await HiveUtils.setToken(token);
      } else {
        // for temp token loading for api service
        // should work for a temp session.
        ref.read(apiServiceProvider.notifier).setTempToken(token);
      }
      await ref
          .read(loginStateProvider.notifier)
          .refreshLoginState(accessToken: token);
      await refreshDeviceProfile();
    } else {
      await logOut(cleanUp: false, token: token);
    }

    // state = token;
  }

  /// the function for login directly with email/phone (in case of user migration). verification is already done before reaching its call.
  Future<void> existingUserResetPassword({
    String? email,
    String? phone,
    required String verificationToken,
    required String password,
  }) async {
    final result = await ref.read(apiServiceProvider).loginWithResetToken(
          email: email,
          phone: phone,
          verificationToken: verificationToken,
          password: password,
        );

    // store token
    final profile = result.$1;
    final token = result.$2;

    // final allowed = kDebugMode ? true : await onLoginVerification();

    await HiveUtils.setToken(token);
    ref.read(loginStateProvider.notifier).refreshLoginState();
    await refreshDeviceProfile();
  }

  Future<void> signUp(
      {required String name,
      required String email,
      required String password,
      String? phone,
      String? referCode,
      String? bday}) async {
    final result = await ref.read(apiServiceProvider).signUp(
          name: name,
          email: email,
          password: password,
          phone: phone,
          referCode: referCode,
          bday: bday,
        );

    // store token
    // final profile = result.$1;
    final token = result.$2;

    await HiveUtils.setToken(token);
    ref.read(loginStateProvider.notifier).refreshLoginState();
  }

  Future<Profile> refreshDeviceProfile() async {
    /// TODO : testing only
    await Future.delayed(const Duration(milliseconds: 1500));
    final profile = Profile(
      id: 1,
      name: 'Nada Jaafar Uday',
      email: '<EMAIL>',
      phone: '0987666777877',
      address: 'ABC - 123',
      postalCode: '885544',
      profilePicture:
          'https://pbs.twimg.com/profile_images/756637418317574145/UbL7MVM6_400x400.jpg',
    );

    // await ref.read(apiServiceProvider).profile();
    state = profile;
    await HiveUtils.setProfile(profile);
    return profile;
  }

  Future<void> logOut({
    bool cleanUp = true,
    String? token,
  }) async {
    //clear any data..
    //reset providers??
    try {
      // await ref.read(apiServiceProvider).logOut(token: token);
    } catch (e) {
      if (kDebugMode) {
        print('Error in logout call.');
        print(e.errorMsg);
      }
    } finally {
      state = null;
      if (cleanUp) {
        await HiveUtils.cleanUp();
        ref.read(loginStateProvider.notifier).refreshLoginState();
        // ref
        //     .read(pushNotificationServiceProvider.notifier)
        //     .disableNotifications(setPref: false);
      }
    }
  }
}
