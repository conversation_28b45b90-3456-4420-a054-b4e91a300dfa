import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/material.dart' as ui;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'theme_provider.g.dart';

@Riverpod(keepAlive: true)
class Theme extends _$Theme {
  @override
  ui.ThemeMode build() {
    final savedTheme = HiveUtils.getPreferredTheme();
    return savedTheme ?? ui.ThemeMode.light;
  }

  // void initializeSystem(ui.BuildContext context) async {
  //   final brightness = ui.Theme.of(context).brightness;
  //   final setTheme = brightness == ui.Brightness.dark
  //       ? ui.ThemeMode.dark
  //       : ui.ThemeMode.light;
  //   state = setTheme;
  //   await HiveUtils.setPreferredTheme(setTheme);
  // }

  void toggleTheme() async {
    final newTheme =
        state == ui.ThemeMode.dark ? ui.ThemeMode.light : ui.ThemeMode.dark;
    state = newTheme;
    await HiveUtils.setPreferredTheme(newTheme);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('preferred_theme', newTheme.name);
  }
}
