import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/widgets.dart' as def;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'locale_provider.g.dart';

@Riverpod(keepAlive: true)
class Locale extends _$Locale {
  static const defaultLocale = def.Locale('en', 'gb');
  static const locales = {
    'en': def.Locale('en', 'gb'),
    'ar': def.Locale('ar'),
    'ku': def.Locale('ku'),
  };
  @override
  def.Locale build() {
    final code = HiveUtils.getPreferredLocale();
    return locales[code] ?? defaultLocale;
  }

  void setLocale(String code) async {
    state = locales[code] ?? defaultLocale;
    await HiveUtils.setPreferredLocale(code);
  }

  void initializeSystem(def.BuildContext context) async {
    final systemLocale = def.Localizations.localeOf(context);
    state = systemLocale;
    await HiveUtils.setPreferredLocale(systemLocale.languageCode);
  }
}
