import 'package:flutter/widgets.dart';
import '../data/ar.dart';
import '../data/ku.dart';
import '../data/en.dart';

class RemoteTranslationService with ChangeNotifier {
  static final RemoteTranslationService _instance =
      RemoteTranslationService._internal();
  factory RemoteTranslationService() => _instance;
  RemoteTranslationService._internal();

  Map<String, String> _translations = {};
  Locale? _currentLocale;

  Future<void> initialize(Locale locale) async {
    _currentLocale = locale;
    try {
      final fetched = await fetchTranslationsFromServer(locale.languageCode);
      _translations = fetched;
      notifyListeners();
    } catch (e) {
      _translations = {};
    }
  }

  String t(String key, {Map<String, dynamic>? params}) {
    String? value = _translations[key];
    print('found value $value for $key');
    if (value == null && _currentLocale != null) {
      final lang = _currentLocale!.languageCode;
      Map<String, String>? localMap;
      final maps = {'ar': arStrings, 'ku': kuStrings, 'en': enStrings};
      localMap = maps[lang];
      value = localMap?[key];
    }
    value ??= enStrings[key];
    value ??= key;
    if (params != null && params.isNotEmpty) {
      params.forEach((k, v) {
        value = value!.replaceAll('{$k}', v.toString());
      });
    }
    return value!;
  }

  Future<Map<String, String>> fetchTranslationsFromServer(String lang) async {
    await Future.delayed(const Duration(milliseconds: 1000));
// for now
    return switch (lang) {
      'en' => enStrings,
      'ar' => arStrings,
      'ku' => kuStrings,
      String() => enStrings,
    };
    // return {};
  }
}

RemoteTranslationService get remoteStrings => RemoteTranslationService();
