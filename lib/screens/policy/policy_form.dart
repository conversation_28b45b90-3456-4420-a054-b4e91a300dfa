import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/styles/ui.dart';
import 'package:ako_basma/util/form/validators.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class PolicyForm extends StatefulWidget {
  const PolicyForm({super.key, this.title, this.desc, this.onSuccess});

  final String? title;
  final String? desc;
  final void Function(bool)? onSuccess;
  @override
  State<PolicyForm> createState() => _PolicyFormState();
}

class _PolicyFormState extends State<PolicyForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final isNew = widget.title == null && widget.desc == null;
    return Scaffold(
      appBar: MyAppbar(
        title: isNew ? strings.addPolicy : strings.updatePolicy,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomDropdown(
                      hintText: strings.policies,
                      items: [
                        // TODO replace with dynamic strings
                        ChipItem(label: 'Attendance Policy', tag: 'attendance'),
                        ChipItem(label: 'Leave Policy', tag: 'leave'),
                        ChipItem(label: 'Code of Conduct', tag: 'conduct'),
                        ChipItem(label: 'Remote Work Policy', tag: 'remote'),
                        ChipItem(label: 'Security Policy', tag: 'security'),
                      ],
                      onChanged: (value) {},
                    ),
                    const SizedBox(height: 16),
                    SimpleTextField(
                      controller: _titleController,
                      decoration:
                          InputDecoration(labelText: strings.policyName),
                      // validator: Validators.required,
                    ),
                    const SizedBox(height: 16),
                    SimpleTextField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        hintText: strings.policyDescription,
                      ),
                      minLines: 8,
                      maxLines: 10,
                    ),
                  ],
                ),
              ),
            ),
          ),
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                spacing: 12,
                children: [
                  PrimaryButton(
                    label: strings.save,
                    onTap: () {
                      context.pop();
                      widget.onSuccess?.call(true);
                    },
                    padding: EdgeInsetsDirectional.symmetric(vertical: 5),
                    textStyle: theme.textStyles.body.copyWith(fontSize: 20),
                  ),
                  AppOutlinedButton(
                    label: strings.draft,
                    onTap: () {},
                    tintColor: theme.colors.primary,
                    textStyle: theme.textStyles.body
                        .copyWith(color: theme.colors.primary),
                    outlineColor: theme.colors.primary,
                    style: AppButtonStyle.primaryOutlinedButtonStyle(
                      context,
                      color: theme.colors.primary,
                    ),
                    padding: EdgeInsets.symmetric(vertical: 8),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
