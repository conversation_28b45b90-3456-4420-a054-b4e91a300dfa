import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class PoliciesScreen extends StatefulWidget {
  const PoliciesScreen({super.key});

  @override
  State<PoliciesScreen> createState() => _PoliciesScreenState();
}

class _PoliciesScreenState extends State<PoliciesScreen> {
  bool _editing = false;
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final policies = [
      {
        'title': 'Workplace Conduct',
        'text':
            'All employees are expected to maintain a professional and respectful environment at all times.'
      },
      {
        'title': 'Leave Policy',
        'text':
            'Employees are entitled to 20 days of paid leave per year. Leave requests must be submitted at least 2 weeks in advance.'
      },
      {
        'title': 'Remote Work',
        'text':
            'Remote work is permitted with prior approval from your manager. Ensure availability during core business hours.'
      },
      {
        'title': 'Data Security',
        'text':
            'Protect company data by following security protocols. Do not share passwords or sensitive information.'
      },
      {
        'title': 'Dress Code',
        'text':
            'Business casual attire is required in the office. Dress appropriately for meetings with clients.'
      },
    ];
    return Scaffold(
      appBar: const MyAppbar(title: 'Company Policy'),
      body: Column(
        children: [
          AnimatedSize(
            duration: 300.milliseconds,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: _buildButtonsRow(onAdd: () {
                context.go('/home/<USER>/add', extra: {
                  'onSuccess': (value) {
                    if (value == true) {
                      showAppSnackbar(context,
                          title: strings.addedSuccessfully, type: 'success');
                    }
                  }
                });
              }, onEdit: () {
                setState(() {
                  _editing = true;
                });
              }, onCancel: () {
                setState(() {
                  _editing = false;
                });
              }, onSave: () {
                setState(() {
                  _editing = false;
                });
              }),
            ),
          ),
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
              itemCount: policies.length,
              separatorBuilder: (_, __) => const SizedBox(height: 16),
              itemBuilder: (context, index) {
                final policy = policies[index];
                return InkWell(
                    onTap: () {
                      context.go('/home/<USER>/${_editing ? 'edit' : 'view'}',
                          extra: {
                            'title': policy['title'],
                            'desc': policy['text'],
                            if (_editing)
                              'onSuccess': (v) {
                                ///data.
                              }
                            //  'onSuccess':
                          });
                    },
                    splashFactory: NoSplash.splashFactory,
                    child: _buildPolicyCard(
                        policy['title'], policy['text'] ?? ""));
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonsRow({
    required void Function()? onEdit,
    required void Function()? onAdd,
    required void Function()? onCancel,
    required void Function()? onSave,
  }) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return LayoutBuilder(builder: (context, constraints) {
      final totalWidth = constraints.maxWidth;
      const spacing = 8.0;
      final availableWidth = totalWidth - spacing;

      final editCancelWidth =
          !_editing ? availableWidth / 2 : availableWidth / 3;
      final addSaveWidth =
          !_editing ? availableWidth / 2 : availableWidth * 2 / 3;

      return Row(
        children: [
          InkWell(
            splashFactory: NoSplash.splashFactory,
            onTap: !_editing ? onEdit : onCancel,
            borderRadius: BorderRadius.circular(8),
            child: AnimatedContainer(
              width: editCancelWidth,
              duration: 300.milliseconds,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(
                    color: !_editing
                        ? theme.colors.primary
                        : theme.colors.secondaryText),
                borderRadius: BorderRadius.circular(8),
              ),
              child: !_editing
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/icons/edit.svg',
                          width: 24,
                          height: 24,
                          color: theme.colors.primary,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            strings.editPolicies,
                            style: theme.textStyles.body
                                .copyWith(color: theme.colors.primary),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    )
                  : Center(
                      child: Text(
                        strings.cancel,
                        maxLines: 1,
                        style: theme.textStyles.body
                            .copyWith(color: theme.colors.secondaryText),
                        textAlign: TextAlign.center,
                      ),
                    ),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: !_editing ? onAdd : onSave,
            splashFactory: NoSplash.splashFactory,
            borderRadius: BorderRadius.circular(8),
            child: AnimatedContainer(
              width: addSaveWidth,
              duration: 300.milliseconds,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: theme.colors.primary),
                color: theme.colors.primary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: !_editing
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Iconsax.add_copy,
                            size: 24, color: Colors.white),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            strings.addPolicy,
                            style: theme.textStyles.textButton
                                .copyWith(color: Colors.white),
                            maxLines: 1,
                          ),
                        ),
                      ],
                    )
                  : Center(
                      child: Text(
                        strings.save,
                        style: theme.textStyles.textButton
                            .copyWith(color: Colors.white),
                        maxLines: 1,
                        textAlign: TextAlign.center,
                      ),
                    ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildPolicyCard(String? title, String desc) {
    final theme = AppTheme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (title != null && title.trim().isNotEmpty)
            Text(
              title,
              style: theme.textStyles.headline4,
            ),
          Text(
            desc,
            style: theme.textStyles.body
                .copyWith(color: theme.colors.secondaryText),
          ),
        ],
      ),
    );
  }
}
