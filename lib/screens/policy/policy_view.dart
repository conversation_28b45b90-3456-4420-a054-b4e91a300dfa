import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';

class PolicyViewScreen extends StatelessWidget {
  const PolicyViewScreen({super.key, this.title, this.desc});

  final String? title;
  final String? desc;
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    // final theme = AppTheme.of(context);
    return Scaffold(
      appBar:  MyAppbar(title: strings.companyPolicy),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (title != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Text(
                  title!,
                  style: theme.textStyles.headline2,
                ),
              ),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (desc != null && desc!.trim().isNotEmpty)
                    Text(
                      desc!,
                      style: theme.textStyles.body.copyWith(
                          color: theme.colors.secondaryText, fontSize: 18),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
