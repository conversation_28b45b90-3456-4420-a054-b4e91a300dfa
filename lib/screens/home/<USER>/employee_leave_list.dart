import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/employee_leave_carousel_card.dart';
import 'package:flutter/material.dart';

class HomeEmployeeLeaveListScreen extends StatefulWidget {
  const HomeEmployeeLeaveListScreen({super.key});

  @override
  State<HomeEmployeeLeaveListScreen> createState() =>
      _HomeEmployeeLeaveListScreenState();
}

class _HomeEmployeeLeaveListScreenState
    extends State<HomeEmployeeLeaveListScreen> {
  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(title: strings.employeeTimeOff),
      body: Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(16),
              itemBuilder: (ctx, index) => EmployeeLeaveCarouselCard(),
              itemCount: 15,
              separatorBuilder: (context, index) => const SizedBox(
                height: 8,
              ),
            ),
          )
        ],
      ),
    );
  }
}
