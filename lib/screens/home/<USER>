import 'dart:math';

import 'package:ako_basma/components/appbar/profile_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/list/adaptive_height_carousel.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/screens/employees/components/popups/employee_leave_details.dart';
import 'package:ako_basma/screens/home/<USER>/dashboard_location_map.dart';
import 'package:ako_basma/screens/home/<USER>/dashboard_employee_carousel.dart';
import 'package:ako_basma/screens/home/<USER>/employee_leave_carousel_card.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final _menuButtonKey = GlobalKey();
  final _chartTimelineDropdownButtonKey = GlobalKey();
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: ProfileAppBar(
        actions: [
          ActionIconButton(
            icon: SvgPicture.asset(
              'assets/icons/notification-dot-${theme.brightness == Brightness.dark ? 'dark' : 'light'}.svg',
              width: 20,
              height: 20,
            ),
            onPressed: () {
              context.go('/home/<USER>');
            },
          ),
          const SizedBox(width: 8),
          ActionIconButton(
            key: _menuButtonKey,
            icon: SolarIconsOutline.listArrowDownMinimalistic,
            onPressed: () {
              showLocalContextMenu(
                context: context,
                buttonKey: _menuButtonKey,
                items: [
                  {
                    'icon': Iconsax.buildings_2_copy,
                    'label': strings.offices,
                    'onPressed': () {
                      context.go('/home/<USER>');
                    },
                  },
                  {
                    'icon': SolarIconsOutline.calendarMinimalistic,
                    'label': strings.schedules,
                    'onPressed': () {
                      context.go('/home/<USER>');
                    },
                  },
                  {
                    'icon': Iconsax.setting_copy,
                    'label': strings.settings,
                    'onPressed': () {
                      context.go('/home/<USER>');
                    },
                  },
                  {
                    'icon': SolarIconsOutline.headphonesRound,
                    'label': strings.support,
                    'onPressed': () {},
                  },
                  {
                    'icon': Iconsax.flag_copy,
                    'label': strings.policies,
                    'onPressed': () {
                      context.go('/home/<USER>');
                    },
                  },
                ],
                positionShift: (offset, size) =>
                    offset.translate(0, size.height + 8),
              );
            },
          ),
        ],
      ),
      floatingActionButton: Transform.translate(
        offset: Offset(
          Directionality.of(context) == TextDirection.rtl ? 7.5 : -7.5,
          0,
        ),
        child: Transform.rotate(
          angle: -pi / 4,
          child: InkWell(
            splashFactory: NoSplash.splashFactory,
            onTap: () {
              context.go('/home/<USER>');
            },
            borderRadius: BorderRadius.circular(4),
            child: Container(
                width: 40,
                height: 40,
                padding: const EdgeInsets.all(4),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  gradient: DesignColors.primaryGradient,
                  /* Brand-Primary */
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4)),
                ),
                child: Transform.rotate(
                  angle: pi / 4,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      strings.ai,
                      style: textStyles.headline.copyWith(
                        fontSize: 20,
                        color: Colors.white,
                      ),
                      textScaler: TextScaler.noScaling,
                      textAlign: TextAlign.center,
                    ),
                  ),
                )),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // dashboard stats
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: LayoutBuilder(builder: (context, constraints) {
                final data = [
                  {
                    'icon': SolarIconsOutline.banknote,
                    // localize
                    'value': formatCurrency(114000, context),
                    'label': strings.thisMonthsPayroll,
                  },
                  {
                    'icon': SolarIconsOutline.usersGroupTwoRounded,
                    // localize
                    'value': '18',
                    'label': strings.employeesWorkingToday,
                  },
                  {
                    'icon': SolarIconsOutline.usersGroupTwoRounded,
                    // localize
                    'value': '105',
                    'label': strings.totalEmployees,
                  },
                  {
                    'icon': Iconsax.menu_board_copy,
                    // localize
                    'value': '67',
                    'label': strings.pendingLeaveRequests,
                  },
                ];
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(child: _buildStatsContainer(data[0])),
                        const SizedBox(width: 8),
                        Expanded(child: _buildStatsContainer(data[1])),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(child: _buildStatsContainer(data[2])),
                        const SizedBox(width: 8),
                        Expanded(child: _buildStatsContainer(data[3])),
                      ],
                    )
                  ],
                );
              }),
            ),
            AnimatedContainer(
              height: 220,
              margin: EdgeInsets.fromLTRB(16, 16, 16, 0),
              duration: 300.milliseconds,
              foregroundDecoration: BoxDecoration(
                border: Border.all(
                  color: colors.primaryVariant,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: DashboardLocationMap(
                  employees: [
                    Employee(
                      id: 'EMP001',
                      name: 'Rajesh Kumar',
                      photoUrl: 'https://i.pravatar.cc/150?img=11',
                      location: LatLng(28.6139, 77.2090), // Connaught Place
                    ),
                    Employee(
                      id: 'EMP002',
                      name: 'Priya Sharma',
                      photoUrl: 'https://i.pravatar.cc/150?img=12',
                      location: LatLng(28.5355, 77.3910), // Gurgaon
                    ),
                    Employee(
                      id: 'EMP003',
                      name: 'Amit Patel',
                      photoUrl: 'https://i.pravatar.cc/150?img=13',
                      location: LatLng(28.7041, 77.1025), // North Delhi
                    ),
                    Employee(
                      id: 'EMP004',
                      name: 'Neha Gupta',
                      photoUrl: 'https://i.pravatar.cc/150?img=14',
                      location: LatLng(28.4595, 77.0266), // South Delhi
                    ),
                  ],
                  onTap: () {
                    context.go('/employees?tab=track');
                  },
                ),
              ),
            ),
            // payroll summary card
            Container(
              margin: EdgeInsets.fromLTRB(16, 16, 16, 16),
              decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: colors.primaryVariant),
                  borderRadius: BorderRadius.circular(8),
                  color: colors.backgroundContainer),
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // localize
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Text(
                              strings.payrollSummary,
                              style: textStyles.headline4.copyWith(
                                  fontSize: 12,
                                  color: colors.primaryText,
                                  height: 1.5),
                            ),
                            Builder(builder: (context) {
                              final data = [
                                {
                                  'gradient': DesignColors.primaryGradient,
                                  'label': strings.grossSalary,
                                },
                                {
                                  'gradient': DesignColors.secondaryGradient,
                                  'label': strings.taxes,
                                },
                                {
                                  'color': colors.primary,
                                  'label': strings.netSalary,
                                }
                              ];
                              return Wrap(
                                spacing: 6,
                                children: data.map((e) {
                                  return Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        height: 4,
                                        width: 4,
                                        margin:
                                            EdgeInsetsDirectional.only(end: 4),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(2),
                                            gradient:
                                                e['gradient'] as Gradient?,
                                            color: e['color'] as Color?),
                                      ),
                                      Text(
                                        e['label'] as String,
                                        style: textStyles.body3.copyWith(
                                            fontSize: 10,
                                            color: colors.tertiaryText),
                                      )
                                    ],
                                  );
                                }).toList(),
                              );
                            })
                          ],
                        ),
                      ),
                      InkWell(
                        key: _chartTimelineDropdownButtonKey,
                        onTap: () {
                          showLocalContextMenu(
                            context: context,
                            buttonKey: _chartTimelineDropdownButtonKey,
                            showDivider: true,
                            items: [
                              {
                                'label': strings.weekly,
                                'onPressed': () {},
                              },
                              {
                                'label': strings.monthly,
                                'onPressed': () {},
                              },
                              {
                                'label': strings.yearly,
                                'onPressed': () {},
                              },
                            ],
                            maxWidth: 150,
                            positionShift: (offset, size) =>
                                offset.translate(0, size.height + 8),
                          );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 1, color: colors.primaryVariant),
                              borderRadius: BorderRadius.circular(4),
                              color: colors.background),
                          padding: const EdgeInsets.all(4),
                          child: Row(
                            children: [
                              Icon(
                                HugeIcons.strokeRoundedArrowDown01,
                                size: 12,
                                color: colors.primary,
                              ),
                              const SizedBox(width: 8),
                              // localize
                              Text(
                                strings.yearly,
                                style: textStyles.body3.copyWith(
                                    fontSize: 10, color: colors.secondaryText),
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  // TODO: find charts lib, replace later..
                  FittedBox(
                    fit: BoxFit.fitWidth,
                    child: SvgPicture.asset(
                      'assets/images/sample-chart-${theme.brightness == Brightness.dark ? 'dark' : 'light'}.svg',
                      // fit: BoxFit.cover,
                      // width: double.infinity,
                    ),
                  ),
                ],
              ),
            ),
            SectionHeading(
              title: strings.employees,
              actionText: strings.showAll,
              onActionPressed: () {
                context.go('/home/<USER>');
              },
            ),
            DashboardEmployeeCarousel(
              employeeIds: [
                '1',
                '2',
                '3',
                '4',
                '5'
              ], // Replace with actual employee IDs
              onCardTap: (data, index) {
                context.push('/salaries/details', extra: data);
              },
            ),

            // payment status  chart
            Container(
              margin: EdgeInsets.fromLTRB(16, 16, 16, 16),
              decoration: BoxDecoration(
                  border: Border.all(width: 0.5, color: colors.primaryVariant),
                  borderRadius: BorderRadius.circular(8),
                  color: colors.backgroundContainer),
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(strings.paymentStatus, style: textStyles.headline2),

                  // TODO: find charts lib, replace later..
                  const SizedBox(height: 12),
                  FittedBox(
                    fit: BoxFit.fitWidth,
                    child: SvgPicture.asset(
                      'assets/images/sample-pie-chart-${theme.brightness == Brightness.dark ? 'dark' : 'light'}.svg',
                      // fit: BoxFit.cover,
                      // width: double.infinity,
                    ),
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: Builder(builder: (context) {
                      final data = [
                        {
                          'gradient': DesignColors.primaryGradient,
                          'label': strings.successfullyPaid,
                        },
                        {
                          'gradient': DesignColors.secondaryGradient,
                          'label': strings.unpaid,
                        },
                        {
                          'color': Color(0xffffba66),
                          'label': strings.pending,
                        }
                      ];
                      return Wrap(
                        spacing: 6,
                        children: data.map((e) {
                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                height: 6,
                                width: 6,
                                margin: EdgeInsetsDirectional.only(end: 4),
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        width: 0.66, color: Colors.white),
                                    borderRadius: BorderRadius.circular(3),
                                    gradient: e['gradient'] as Gradient?,
                                    color: e['color'] as Color?),
                              ),
                              Text(
                                e['label'] as String,
                                style: textStyles.body3.copyWith(
                                    fontSize: 10, color: colors.primaryText),
                              )
                            ],
                          );
                        }).toList(),
                      );
                    }),
                  )
                ],
              ),
            ),
            SectionHeading(
              title: strings.employeeTimeOff,
              actionText: strings.showAll,
              onActionPressed: () {
                context.go('/home/<USER>');
              },
            ),
            AdaptiveHeightCarousel(
              itemCount: 4,
              itemBuilder: (context, index) => GestureDetector(
                  onTap: () {
                    showAdaptivePopup(
                      context,
                      (context, sc) => EmployeeLeaveDetailsPopup(
                        data: dummyEmployees[index % dummyEmployees.length],
                        leaveStart: DateTime(2025, 7, 26, 9, 0),
                        leaveEnd: index % 2 == 0
                            ? DateTime(2025, 7, 26, 9, 0)
                            : index % 3 == 0
                                ? DateTime(2025, 7, 26, 9, 0)
                                : DateTime(2025, 7, 28, 17, 0),
                        onApprove: () {},
                        onReject: () {},
                        statusType: 'pending',
                        dayType: index % 2 == 0
                            ? 'allday'
                            : index % 3 == 0
                                ? 'halfday'
                                : null,
                      ),
                    );
                  },
                  child: const EmployeeLeaveCarouselCard()),
              itemPadding: const EdgeInsetsDirectional.symmetric(horizontal: 4),
            ),
            const SizedBox(height: 16 + 55),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsContainer(Map<String, dynamic> data) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: colors.primaryVariant,
          ),
          borderRadius: BorderRadius.circular(8),
          color: colors.backgroundContainer),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Align(
            alignment: AlignmentDirectional.topStart,
            child: Icon(
              data['icon'],
              size: 24,
              color: colors.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            data['value'],
            style: textStyles.headline4.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 8),
          Text(
            data['label'],
            style: textStyles.body3.copyWith(color: colors.secondaryText),
          ),
        ],
      ),
    );
  }
}
