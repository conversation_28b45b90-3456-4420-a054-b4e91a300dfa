import 'package:ako_basma/components/list/adaptive_height_carousel.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/home/<USER>/employee_carousel_card.dart';
import 'package:flutter/material.dart';

class DashboardEmployeeCarousel extends StatelessWidget {
  final List<String> employeeIds;
  final void Function(EmployeeData data, int index)? onCardTap;

  const DashboardEmployeeCarousel({
    super.key,
    required this.employeeIds,
    this.onCardTap,
  });

  @override
  Widget build(BuildContext context) {
    return AdaptiveHeightCarousel(
      itemCount: employeeIds.length,
      itemBuilder: (context, index) => GestureDetector(
        onTap: () => onCardTap?.call(
            dummyEmployees[index % dummyEmployees.length], index),
        child: EmployeeCarouselCard(
          data: dummyEmployees[index % dummyEmployees.length],
        ),
      ),
      itemPadding: const EdgeInsetsDirectional.symmetric(horizontal: 4),
    );
  }
}
