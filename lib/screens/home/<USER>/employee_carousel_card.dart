import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/models/employee_data.dart';

class EmployeeCarouselCard extends StatelessWidget {
  const EmployeeCarouselCard({
    super.key,
    required this.data,
  });

  final EmployeeData data;

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: appTheme.colors.backgroundContainer,
        border: Border.all(color: appTheme.colors.primaryVariant, width: 1),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          // Top Row: Avatar, Name, Status
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar
              Stack(
                children: [
                  SizedBox(
                      height: 48,
                      width: 48,
                      child: ClipRRect(
                        borderRadius: BorderRadiusGeometry.circular(24),
                        child: ImageContainer(
                          url: data.imageUrl,
                          placeholderAsset: 'assets/images/person.png',
                          placeholderFit: BoxFit.cover,
                          fit: BoxFit.cover,
                        ),
                      )),
                  PositionedDirectional(
                    bottom: 2.5,
                    end: 2.5,
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: appTheme.colors.success,
                        shape: BoxShape.circle,
                      ),
                    ),
                  )
                ],
              ),
              const SizedBox(width: 8),
              // Name and Active
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.name,
                      style: appTheme.textStyles.headline4
                          .copyWith(color: appTheme.colors.primaryText),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      strings.active,
                      style: appTheme.textStyles.body2.copyWith(
                        color: appTheme.colors.tertiaryText,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 100,
                margin: const EdgeInsetsDirectional.only(start: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                decoration: BoxDecoration(
                  color: appTheme.colors.successContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    strings.good,
                    style: appTheme.textStyles.body
                        .copyWith(color: appTheme.colors.success),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Net Salary Section
          Container(
            decoration: BoxDecoration(
              color: appTheme.colors.background,
              borderRadius: BorderRadius.circular(8),
              border:
                  Border.all(color: appTheme.colors.primaryVariant, width: 1),
            ),
            padding: const EdgeInsets.all(8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // Salary Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        strings.netSalary,
                        style: appTheme.textStyles.body3.copyWith(
                          color: appTheme.colors.tertiaryText,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        formatCurrency(1100000, context),
                        style: appTheme.textStyles.textButton.copyWith(
                          fontSize: 14,
                          color: appTheme.colors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ),
                // Paid badge
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                  decoration: BoxDecoration(
                    color: appTheme.colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    strings.paid,
                    style: appTheme.textStyles.body
                        .copyWith(color: appTheme.colors.primaryText),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
