import 'dart:async';
import 'package:ako_basma/constants/map.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart' hide Theme;
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/map/marker_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/components/loading/dots_loading.dart';

class DashboardLocationMap extends ConsumerStatefulWidget {
  final List<Employee> employees;
  final void Function()? onTap;

  const DashboardLocationMap({
    super.key,
    required this.employees,
    this.onTap,
  });

  @override
  ConsumerState<DashboardLocationMap> createState() =>
      _LocationSharingMapState();
}

class _LocationSharingMapState extends ConsumerState<DashboardLocationMap> {
  final Completer<GoogleMapController> _controller = Completer();
  final Set<Marker> _markers = {};
  bool _markersCreated = false;
  bool _isMapDark = false;
  bool _loading = true;

  late CameraPosition _initialCameraPosition;

  @override
  void initState() {
    super.initState();
    // Center on first employee or fallback to a default location
    final LatLng initialLatLng = widget.employees.isNotEmpty
        ? widget.employees.first.location
        : const LatLng(36.1911, 44.0092); // Default: Erbil
    _initialCameraPosition = CameraPosition(
      target: initialLatLng,
      zoom: 9.0,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_markersCreated) {
      _createEmployeeMarkers(context);
      _markersCreated = true;
    }
    _setMapStyle();
  }

  void _setMapStyle() async {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    if (_isMapDark == isDark) return;

    final controller = await _controller.future;
    final style = isDark ? mapNightStyleString : null;
    controller.setMapStyle(style);
    setState(() {
      _isMapDark = isDark;
    });
  }

  Future<void> _createEmployeeMarkers(BuildContext context) async {
    setState(() {
      _loading = true;
    });
    final List<Employee> employees = widget.employees;
    final Set<Marker> newMarkers = {};
    for (final employee in employees) {
      final BitmapDescriptor customMarker =
          await _getCustomMarker(employee.photoUrl);
      newMarkers.add(
        Marker(
          markerId: MarkerId(employee.id),
          position: employee.location,
          icon: customMarker,
          anchor: const Offset(0.5, 1),
          onTap: () {
            widget.onTap?.call();
          },
        ),
      );
    }
    if (mounted) {
      setState(() {
        _markers.clear();
        _markers.addAll(newMarkers);
        _loading = false;
      });
    }
  }

  Future<BitmapDescriptor> _getCustomMarker(String photoUrl) async {
    final devicePixelRatio = MediaQuery.devicePixelRatioOf(context);
    const markerRatio = 63 / 51;
    const markerHeight = 63.0;
    const markerWidth = 51.0;
    // Load the image first, then use it in the ClipOval
    final ImageProvider imageProvider = NetworkImage(photoUrl);
    await precacheImage(imageProvider, context);

    return await SizedBox(
            height: markerHeight,
            width: markerWidth,
            child: Stack(
              children: [
                SvgPicture.asset(
                  'assets/icons/map_photo_marker_${ref.watch(themeProvider).name}.svg',
                  height: markerHeight,
                  width: markerWidth,
                ),
                Positioned(
                    top: 8,
                    left: 7,
                    right: 7,
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: SizedBox.square(
                        dimension: 43,
                        child: ClipOval(
                          child: Image(
                            image: imageProvider,
                            height: 43,
                            width: 43,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ))
              ],
            ))
        .toBitmapDescriptor(
            logicalSize: Size(51, 63),
            imageSize: Size(51 * devicePixelRatio, 63 * devicePixelRatio));
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return SizedBox(
      height: 220,
      child: Stack(
        children: [
          GoogleMap(
            mapType: MapType.normal,
            initialCameraPosition: _initialCameraPosition,
            onMapCreated: (GoogleMapController controller) {
              if (!_controller.isCompleted) {
                _controller.complete(controller);
                _setMapStyle();
              }
            },
            onTap: (_) {
              widget.onTap?.call();
            },
            gestureRecognizers: const {
              Factory<OneSequenceGestureRecognizer>(EagerGestureRecognizer.new),
            },
            markers: _markers,
            zoomControlsEnabled: false,
            tiltGesturesEnabled: false,
            rotateGesturesEnabled: false,
            myLocationButtonEnabled: false,
            myLocationEnabled: true,
          ),
          if (_loading)
            const PositionedDirectional(
              top: 20,
              start: 16,
              child: SizedBox(
                child: DotsLoadingIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
