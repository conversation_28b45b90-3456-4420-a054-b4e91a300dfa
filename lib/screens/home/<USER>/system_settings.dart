import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/radio_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/providers/locale/locale_provider.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ako_basma/styles/theme_extensions.dart'; // Import AppTheme
import '../../../i18n/util/remote_translation_service.dart';

class SystemSettingsScreen extends ConsumerStatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  ConsumerState<SystemSettingsScreen> createState() =>
      _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends ConsumerState<SystemSettingsScreen> {
  String? _selectedLanguage;
  String? _selectedTimeZone;
  ThemeMode _selectedTheme = ThemeMode.system;

  final List<String> _timeZones = [
    'UTC-12',
    'UTC-11',
    'UTC',
    'UTC+1',
    'UTC+14'
  ];

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final themeValue = ref.watch(themeProvider);
    final locale = ref.watch(localeProvider);
    final strings = AppLocalizations.of(context)!;

    print(locale);
    const languages = [
      ChipItem(
        label: 'عربي',
        // change if we want localized labels
        // label: strings.arabic,

        tag: 'ar',
      ),
      ChipItem(
        label: 'كوردى',
        tag: 'ku',
      ),
      ChipItem(
        label: "English",
        // change if we want localized labels
        // label: strings.english,
        tag: 'en',
      ),
    ];

    return Scaffold(
        appBar: MyAppbar(
          // title: remoteStrings.t('generalSettings'),
          title: strings.generalSettings,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                    color: theme.colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(16)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      strings.generalSettings,
                      style: theme.textStyles.headline3,
                    ),
                    const SizedBox(height: 16),
                    CustomDropdown(
                      // localize
                      hintText: strings.language,
                      initialItem: languages.firstWhere(
                        (language) =>
                            ((language.tag ?? "en") == locale.languageCode),
                        orElse: () =>
                            const ChipItem(label: 'English', tag: 'en'),
                      ),
                      items: languages,
                      onChanged: (value) async {
                        final id = value?.tag ?? "en";
                        final currentLocale = ref.read(localeProvider);
                        if (currentLocale.languageCode == id) {
                          return;
                        } // No change, do nothing

                        ref.read(localeProvider.notifier).setLocale(id);
                        // final locale = ref.read(localeProvider);
                        await remoteStrings.initialize(
                            Locale.locales[id] ?? Locale.defaultLocale);
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomDropdown(
                        // localize
                        hintText: strings.timeZone,
                        items: ['Asia/Kolkata', 'Asia/Baghdad'],
                        onChanged: (value) {}),
                    // const SizedBox(height: 16),
                    Divider(
                      height: 31,
                      color: theme.colors.strokeColor,
                      thickness: 1,
                      indent: 1,
                      endIndent: 1,
                    ),
                    Text(
                      strings.theme,
                      style: theme.textStyles.textButton
                          .copyWith(color: theme.colors.primaryText),
                    ),
                    const SizedBox(height: 8),

                    Wrap(
                      spacing: 10,
                      children: [
                        AppRadioButton.tab(
                          value: 'light',
                          groupValue: themeValue.name,
                          onChanged: (value) {
                            ref.read(themeProvider.notifier).toggleTheme();
                          },
                          label: strings.light,
                        ),
                        AppRadioButton.tab(
                          value: 'dark',
                          groupValue: themeValue.name,
                          onChanged: (value) {
                            ref.read(themeProvider.notifier).toggleTheme();
                          },
                          label: strings.dark,
                        ),
                        // build('dark', 'Dark', 'light', (c) {}),
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }
}
