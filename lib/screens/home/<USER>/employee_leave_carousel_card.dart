import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';

class EmployeeLeaveCarouselCard extends StatelessWidget {
  const EmployeeLeaveCarouselCard({super.key});

  @override
  Widget build(BuildContext context) {
    final appTheme = AppTheme.of(context);

    // localize - in the file./
    return Container(
      decoration: BoxDecoration(
        color: appTheme.colors.backgroundContainer,
        border: Border.all(color: appTheme.colors.primaryVariant, width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Avatar
          SizedBox(
              height: 48,
              width: 48,
              child: ClipRRect(
                borderRadius: BorderRadiusGeometry.circular(24),
                child: const ImageContainer(
                  url: null,
                  placeholderAsset: 'assets/images/person.png',
                  placeholderFit: BoxFit.cover,
                  fit: BoxFit.cover,
                ),
              )),
          const SizedBox(width: 8),
          // Name and Active
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Jane Cooper',
                  style: appTheme.textStyles.textButton
                      .copyWith(color: appTheme.colors.primaryText),
                ),
                const SizedBox(height: 8),
                Text(
                  'Sick Leave',
                  style: appTheme.textStyles.body3.copyWith(
                    color: appTheme.colors.tertiaryText,
                  ),
                ),
              ],
            ),
          ),
          // TODO localize date.
          Text(
            // DateTime(2025, 7, 28, 15, 45),
            // DateTime(2025, 7, 28, 16, 35),
            // 'expected': '3:45 PM - 4:35 PM 28 Jul 2025',

            // Same month, different days
            // DateTime(2025, 7, 26),
            // DateTime(2025, 7, 28),
            // 'expected': '26 - 28 Jul 2025',

            // Different months, same year

            // DateTime(2025, 4, 26),
            // DateTime(2025, 7, 28),
            // 'expected': '26 Apr - 28 Jul 2025',

            // Different years

            // DateTime(2024, 4, 24),
            // DateTime(2025, 7, 28),
            // 'expected': '24 Apr 2024 - 28 Jul 2025',

            formatCompactDateRangeText(
              DateTime(2025, 7, 26),
              DateTime(2025, 7, 28),
              context,
            ),
            style: appTheme.textStyles.body3.copyWith(
              color: appTheme.colors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
