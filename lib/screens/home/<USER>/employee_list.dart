import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/employee_carousel_card.dart';
import 'package:flutter/material.dart';

class HomeEmployeeListScreen extends StatefulWidget {
  const HomeEmployeeListScreen({super.key});

  @override
  State<HomeEmployeeListScreen> createState() => _HomeEmployeeListScreenState();
}

class _HomeEmployeeListScreenState extends State<HomeEmployeeListScreen> {
  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(title: strings.employees),
      body: Column(
        children: [
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(16),
              itemBuilder: (ctx, index) => EmployeeCarouselCard(
                data: dummyEmployees[index],
              ),
              itemCount: dummyEmployees.length,
              separatorBuilder: (context, index) => const SizedBox(
                height: 8,
              ),
            ),
          )
        ],
      ),
    );
  }
}
