import 'dart:io';

import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/attachment/cover_attachment_card.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/providers/auth/auth.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _isEditing = false;
  final _logoAttachmentCardKey = GlobalKey();
  File? _selectedLogo;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(
        title: strings.profile,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildButtonsRow(onEdit: () {
              setState(() {
                _isEditing = true;
              });
            }, onCancel: () {
              unfocus();
              setState(() {
                _isEditing = false;
              });
              // revert changes.
            }, onSave: () async {
              unfocus();

              // await Future.delayed(1.seconds);
              setState(() {
                _isEditing = false;
              });
              showAppSnackbar(context,
                  title: strings.savedSuccessfully, type: 'success');
            }),
            const SizedBox(height: 16),
            _buildSection(
              icon: SolarIconsOutline.buildings_3,
              heading: strings.companyInformation,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 8),
                  SimpleTextField(
                    initialValue: 'Ako',
                    decoration: InputDecoration(labelText: strings.companyName),
                    readOnly: !_isEditing,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    initialValue: 'Iraq,mm.29',
                    decoration: InputDecoration(labelText: strings.address),
                    readOnly: !_isEditing,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    initialValue: embedLtr('<EMAIL>'),
                    decoration: InputDecoration(labelText: strings.email),
                    readOnly: !_isEditing,
                    forceLtr: true,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    initialValue: embedLtr('09876667778777878'),
                    decoration:
                        InputDecoration(labelText: strings.contactNumber),
                    readOnly: !_isEditing,
                    forceLtr: true,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    initialValue: embedLtr('https://akosayara.com/'),
                    decoration: InputDecoration(labelText: strings.website),
                    readOnly: !_isEditing,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    initialValue: '034',
                    decoration: InputDecoration(
                        labelText: strings.companyJobNumberStart),
                    readOnly: !_isEditing,
                  ),
                  const SizedBox(height: 12),
                  Text(strings.logo, style: theme.textStyles.button),
                  const SizedBox(height: 16),
                  GestureDetector(
                    key: _logoAttachmentCardKey,
                    onTap: _isEditing ? _handleLogoAttachment : null,
                    child: LayoutBuilder(builder: (context, constraints) {
                      if (_selectedLogo != null) {
                        return CoverAttachmentCard(
                          filePath: _selectedLogo!.path,
                          height: 160,
                          fit: BoxFit.contain,
                          width: constraints.maxWidth,
                          centerEditIcon: false,
                        );
                      } else {
                        return AttachmentPlaceholderCard(
                          preset: 'upload',
                          uploadTitle: strings.clickToUpload,
                          uploadSubtitle: strings.maxFileSizeInMB(25),
                        );
                      }
                    }),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildSection(
              icon: SolarIconsOutline.usersGroupTwoRounded,
              heading: strings.management,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 8),
                  Text(strings.hrManager, style: theme.textStyles.button),
                  const SizedBox(height: 16),
                  _buildManagementCard(
                    theme,
                    name: 'Layla Fadel Jassim',
                    role: strings.hrManager,
                    imageUrl:
                        'https://randomuser.me/api/portraits/women/44.jpg',
                  ),
                  const SizedBox(height: 12),
                  Text(strings.companyDirector, style: theme.textStyles.button),
                  const SizedBox(height: 16),
                  _buildManagementCard(
                    theme,
                    name: 'Layla Fadel Jassim',
                    role: strings.companyDirector,
                    imageUrl:
                        'https://randomuser.me/api/portraits/women/44.jpg',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildSection(
              icon: Iconsax.setting_copy,
              heading: strings.settings,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 24),
                  CustomDropdown(
                      // localize
                      onContainer: true,
                      hintText: strings.timeZone,
                      items: ['Asia/Kolkata', strings.asiaBaghdad],
                      onChanged: (value) {}),
                  const SizedBox(height: 16),
                  CustomDropdown(
                      // localize
                      onContainer: true,
                      hintText: strings.currency,
                      items: ['IQD', 'USD', 'INR'],
                      onChanged: (value) {}),
                ],
              ),
            ),
            const SizedBox(height: 16),
            AppOutlinedButton.async(
              label: strings.logOut,
              tintColor: theme.colors.error,
              textStyle:
                  theme.textStyles.body3.copyWith(color: theme.colors.error),
              onPressed: ref.read(authStateProvider.notifier).logOut,
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required dynamic icon,
    required String heading,
    required Widget child,
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              if (icon is String)
                SvgPicture.asset(
                  icon,
                  height: 24,
                  width: 24,
                  color: theme.colors.primary,
                )
              else if (icon is IconData)
                Icon(
                  icon,
                  color: theme.colors.primary,
                  size: 24,
                ),
              const SizedBox(width: 16),
              Expanded(
                  child: Text(
                heading,
                style: theme.textStyles.buttonMedium
                    .copyWith(color: theme.colors.primary),
              )),
            ],
          ),
          child,
        ],
      ),
    );
  }

  Widget _buildManagementCard(AppTheme theme,
      {required String name, required String role, required String imageUrl}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colors.background,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          SizedBox.square(
            dimension: 56,
            child: ClipOval(
              child: ImageContainer(
                url: imageUrl,
                height: 56,
                width: 56,
              ),
            ),
          ),
          SizedBox(
            height: 56,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: VerticalDivider(
                width: 0,
                thickness: 1,
                color: theme.colors.strokeColor,
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(name,
                    style: theme.textStyles.headline4.copyWith(fontSize: 14)),
                const SizedBox(height: 4),
                Align(
                  alignment: AlignmentDirectional.topStart,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                        color: theme.colors.infoContainer,
                        borderRadius: BorderRadius.circular(8)),
                    child: Text(role,
                        style: theme.textStyles.body
                            .copyWith(color: theme.colors.info)),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonsRow({
    required void Function()? onEdit,
    required void Function()? onCancel,
    required void Function()? onSave,
  }) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return LayoutBuilder(builder: (context, constraints) {
      final totalWidth = constraints.maxWidth;
      const spacing = 8.0;
      final availableWidth = totalWidth - spacing;

      final editCancelWidth = availableWidth / 3;
      final saveWidth = availableWidth * 2 / 3;
      final editCancelBtn = InkWell(
        splashFactory: NoSplash.splashFactory,
        onTap: !_isEditing ? onEdit : onCancel,
        borderRadius: BorderRadius.circular(8),
        child: AnimatedContainer(
          width: editCancelWidth,
          duration: 200.milliseconds,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(
                color: !_isEditing
                    ? theme.colors.primary
                    : theme.colors.secondaryText),
            borderRadius: BorderRadius.circular(8),
          ),
          child: !_isEditing
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/icons/edit.svg',
                      width: 24,
                      height: 24,
                      color: theme.colors.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      strings.edit,
                      style: theme.textStyles.body
                          .copyWith(color: theme.colors.primary),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                )
              : Center(
                  child: Text(
                    strings.cancel,
                    maxLines: 1,
                    style: theme.textStyles.body
                        .copyWith(color: theme.colors.secondaryText),
                    textAlign: TextAlign.center,
                  ),
                ),
        ),
      );

      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (_isEditing)
            InkWell(
              onTap: onSave,
              splashFactory: NoSplash.splashFactory,
              borderRadius: BorderRadius.circular(8),
              child: AnimatedContainer(
                width: saveWidth,
                duration: 300.milliseconds,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: theme.colors.primary),
                  color: theme.colors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CardTheme(
                  child: Text(
                    strings.save,
                    style: theme.textStyles.textButton
                        .copyWith(color: Colors.white),
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ).animate().fadeIn(),
          const SizedBox(width: 8),
          AnimatedSize(duration: 300.milliseconds, child: editCancelBtn)
        ],
      );
    });
  }

  /// Handle group photo selection from gallery
  Future<void> _handleLogoAttachment() async {
    final strings = AppLocalizations.of(context)!;
    try {
      final res = await showLocalPickerMenu(
        buttonKey: _logoAttachmentCardKey,
        context: context,
        allowedTypes: ['image'],
        allowMultiple: false,
        maxSizeInMB: 25,
      );
      setState(() {
        if (res is File) {
          _selectedLogo = (res);
        }
      });
    } catch (e) {
      // Handle image selection error
      if (mounted) {
        showAppSnackbar(context,
            title: strings.sorrySomeErrorOccured, type: 'error');
      }
    }
  }
}
