import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:solar_icons/solar_icons.dart';

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() =>
      _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  final List<Map<String, dynamic>> notifications = [
    // {
    //   'message': 'Enjoy your time off',
    //   'time': DateTime(2024, 3, 20, 8, 0), // Today, 08:00 AM
    // },
    // {
    //   'message': 'Check your account for details',
    //   'time': DateTime(2024, 3, 19, 8, 0), // Yesterday, 08:00 AM
    // },
    // {
    //   'message': 'The company will be closed',
    //   'time': DateTime(2024, 3, 21), // Tomorrow
    // },
    // {
    //   'message': 'Don\'t forget to clock in',
    //   'time': DateTime(2024, 3, 20, 9, 30), // Today, 09:30 AM
    // },
    // {
    //   'message': 'Please clock in as soon as possible',
    //   'time': DateTime(2024, 3, 20, 9, 10), // Today, 09:10 AM
    // },
    // {
    //   'message': 'You\'ve been selected as Employee of the Month',
    //   'time': DateTime(2024, 3, 1), // March 1st
    // },
    // {
    //   'message':
    //       'You\'ve been selected as Employee of the Month. You\'ve been selected as Employee of the Month.',
    //   'time': DateTime(2024, 2, 1), // February 1st
    // },
  ];

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final timeStr =
        '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';

    if (time.year == today.year &&
        time.month == today.month &&
        time.day == today.day) {
      return 'Today, $timeStr';
    } else if (time.year == yesterday.year &&
        time.month == yesterday.month &&
        time.day == yesterday.day) {
      return 'Yesterday, $timeStr';
    } else {
      return '${time.day.toString().padLeft(2, '0')}/${time.month.toString().padLeft(2, '0')}/${time.year}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(title: strings.notifications),
      body: SafeArea(
        child: Column(
          children: [
            // Notifications list
            Expanded(
              child: notifications.isEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SvgPicture.asset(
                                'assets/images/notification-empty-${ref.watch(themeProvider).name}.svg'),
                            const SizedBox(height: 10),
                            Text(
                              strings.thereAreNoNotifications,
                              style: theme.textStyles.body,
                            ),
                          ],
                        ),
                      ), 
                    )
                  : ListView.separated(
                      padding: const EdgeInsets.all(16),
                      itemCount: notifications.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: 8),
                      itemBuilder: (context, index) {
                        final notification = notifications[index];
                        final time = notification['time'] as DateTime;
                        return _buildNotificationItem(
                          message: notification['message']!,
                          time: time,
                          unread: index == 0,
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem({
    required String message,
    required DateTime time,
    bool unread = false,
  }) {
    final theme = AppTheme.of(context);

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: theme.colors.primaryVariant),
            color: theme.colors.backgroundContainer,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                message,
                style: theme.textStyles.body2,
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 12,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(SolarIconsOutline.calendarMark,
                          color: theme.colors.tertiaryText, size: 12),
                      const SizedBox(width: 4),
                      Text(
                        formatDateDmy(time, context),
                        style: theme.textStyles.body3.copyWith(
                          color: theme.colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(SolarIconsOutline.clockCircle,
                          color: theme.colors.tertiaryText, size: 12),
                      const SizedBox(width: 4),
                      Text(
                        formatTime(time, context),
                        style: theme.textStyles.body3.copyWith(
                          color: theme.colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        if (unread)
          PositionedDirectional(
              start: 0,
              top: 10,
              bottom: 10,
              child: Container(
                width: 3,
                margin: const EdgeInsetsDirectional.only(start: 1),
                decoration: BoxDecoration(
                    color: theme.colors.primary,
                    borderRadius: const BorderRadiusDirectional.horizontal(
                        end: Radius.circular(8))),
              ))
      ],
    );
  }
}
