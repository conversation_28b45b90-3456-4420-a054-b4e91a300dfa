import 'dart:math';

import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/tasks/components/cards/task_card.dart';
import 'package:ako_basma/screens/tasks/screens/task_search_popup.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/screens/tasks/screens/project_selection_popup.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen> {
  String? _selectedProject;
  bool _hasTasks = false;

  final _projectList = <ChipItem>[
    // use projectData later// using chipitem for now.
    // ChipItem(label: 'Design Work', tag: 'design_work'),
    // ChipItem(label: 'Staff Application', tag: 'staff_application'),
    // ChipItem(label: 'Admin Application', tag: 'admin_application'),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      floatingActionButton: Transform.translate(
        offset: Offset(
          Directionality.of(context) == TextDirection.rtl ? 7.5 : -7.5,
          0,
        ),
        child: Transform.rotate(
          angle: -pi / 4,
          child: InkWell(
            splashFactory: NoSplash.splashFactory,
            onTap: () {
              context.push('/home/<USER>');
            },
            borderRadius: BorderRadius.circular(4),
            child: Container(
                width: 40,
                height: 40,
                padding: const EdgeInsets.all(4),
                clipBehavior: Clip.antiAlias,
                decoration: ShapeDecoration(
                  gradient: DesignColors.primaryGradient,
                  /* Brand-Primary */
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4)),
                ),
                child: Transform.rotate(
                  angle: pi / 4,
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      strings.ai,
                      style: theme.textStyles.headline.copyWith(
                        fontSize: 20,
                        color: Colors.white,
                      ),
                      textScaler: TextScaler.noScaling,
                      textAlign: TextAlign.center,
                    ),
                  ),
                )),
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(context),
            Expanded(
              child: _buildBody(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    // Using a fully custom container instead of the default AppBar so that
    // we have full control over vertical alignment of all elements.

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                InkWell(
                  splashFactory: NoSplash.splashFactory,
                  onTap: _handleProjectSelection,
                  child: Row(
                    children: [
                      Text(
                        (_projectList
                                .where((e) => e.tag == _selectedProject)
                                .cast<ChipItem>()
                                .isNotEmpty
                            ? _projectList
                                .where((e) => e.tag == _selectedProject)
                                .cast<ChipItem>()
                                .first
                                .label
                            : strings.allProjects),
                        style: theme.textStyles.headline4,
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        HugeIcons.strokeRoundedArrowDown01,
                        size: 24,
                        color: theme.colors.primary,
                      ),
                    ],
                  ),
                ),
                Text(
                  strings.manageProject,
                  style: theme.textStyles.body3
                      .copyWith(color: theme.colors.tertiaryText),
                ),
              ],
            ),
          ),
          ActionIconButton(
            icon: Iconsax.add_copy,
            iconColor: theme.colors.primary,
            iconSize: 24,
            onPressed: () =>
                _selectedProject != null && _selectedProject!.isNotEmpty
                    ? _handleAddTask()
                    : _handleAddProject(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyProjectsInfo() {
    final strings = AppLocalizations.of(context)!;

    final theme = AppTheme.of(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Empty state icon
        Icon(
          SolarIconsBold.serverMinimalistic,
          size: 64,
          color: theme.colors.tertiaryText,
          applyTextScaling: false,
        ),
        const SizedBox(height: 16),
        Text(
          strings.noProjectFound,
          style:
              theme.textStyles.button.copyWith(color: theme.colors.primaryText),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        Text(
          strings.pleaseCreateAProject,
          style: theme.textStyles.body3
              .copyWith(color: theme.colors.secondaryText),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Align(
          alignment: Alignment.center,
          child: PrimaryButton(
            label: strings.createAProject,
            onTap: _handleAddProject,
            prefixIcon: Iconsax.add_copy,
            expand: false,
            isCompact: true,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyTasksInfo() {
    final strings = AppLocalizations.of(context)!;

    final theme = AppTheme.of(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Empty state icon
        Icon(
          SolarIconsBold.checklistMinimalistic,
          size: 64,
          color: theme.colors.tertiaryText,
          applyTextScaling: false,
        ),
        const SizedBox(height: 16),
        Text(
          strings.noTasksFound,
          style:
              theme.textStyles.button.copyWith(color: theme.colors.primaryText),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),

        Text(
          strings.pleaseCreateATask,
          style: theme.textStyles.body3
              .copyWith(color: theme.colors.secondaryText),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Align(
          alignment: Alignment.center,
          child: PrimaryButton(
            label: strings.createATask,
            onTap: _handleAddTask,
            prefixIcon: Iconsax.add_copy,
            expand: false,
            isCompact: true,
          ),
        ),
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    // assume some dummy logic to show / hide default tasks..
    // say if project name is tasks, we can see tasks or empty otherwise.
    final hasTasks = _selectedProject == 'tasks';

    if (_selectedProject == null && _projectList.isEmpty) {
      // Show empty state for no projects
      return Center(child: _buildEmptyProjectsInfo());
    } else if (_selectedProject != null && !hasTasks) {
      return Center(child: _buildEmptyTasksInfo());
    } else {
      // Show tasks list which are my task card as of now
      return Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: SearchTextField(
              // hintText: 'Search for a task',
              // margin: EdgeInsets.zero,
              onChanged: (value) {
                print(value);
              },
              readOnly: true,
              onTap: () {
                showAdaptivePopup(context, (ctx, sc) {
                  return TaskSearchPopup(sc: sc);
                },
                    useRootNavigator: true,
                    scrollable: true,
                    contentPadding: EdgeInsets.all(0));
              },
            ),
          ),

          // Tasks list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              itemCount: 5, // For now, show 5 sample tasks
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () {
                    context.go('/tasks/taskDetails');
                  },
                  child: const TaskCard(),
                );
              },
            ),
          ),
        ],
      );
    }
  }

  void _handleAddProject() {
    context.go('/tasks/newProject', extra: {
      'onSuccess': (chip) {
        if (chip is ChipItem) {
          setState(() {
            _projectList.add(chip);
            _selectedProject = chip.tag ?? chip.label;
          });
        } else {
          // Optionally show error or do nothing
        }
      }
    });
  }

  void _handleAddTask() {
    final strings = AppLocalizations.of(context)!;

    context.go('/tasks/newTask', extra: {
      'onSuccess': (chip) {
        if (chip is ChipItem) {
          // setState(() {
          //   _projectList.add(chip);
          //   _selectedProject = chip.tag ?? chip.label;
          // });
          showAppSnackbar(context,
              title: strings.taskAddedSuccessfully, type: 'success');
        } else {
          // Optionally show error or do nothing
        }
      }
    });
  }

  void _handleProjectSelection() {
    showAdaptivePopup(
      context,
      (ctx, sc) {
        return ProjectSelectionPopup(
          sc: sc,
          selectedId: _selectedProject ?? "",
          projects: _projectList,
          onSelected: (id) {
            setState(() {
              _selectedProject = id;
            });
          },
          onCreate: (chip) {
            setState(() {
              _projectList.add(chip);
              _selectedProject = chip.tag ?? chip.label;
            });
          },
        );
      },
      useRootNavigator: true,
    );
  }
}
