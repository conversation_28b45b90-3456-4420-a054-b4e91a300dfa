import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class CommentCard extends StatelessWidget {
  final String imageUrl;
  final String userName;
  final DateTime date;
  final String? commentText;

  const CommentCard({
    super.key,
    required this.imageUrl,
    required this.userName,
    required this.date,
    this.commentText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.colors.background,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User avatar
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colors.strokeColor,
            ),
            clipBehavior: Clip.hardEdge,
            child: ImageContainer(
              url: imageUrl,
              width: 24,
              height: 24,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 8),
          // Comment content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // User name and timestamp row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // User name
                    Expanded(
                      child: Text(
                        userName,
                        style: theme.textStyles.headline4.copyWith(
                          color: theme.colors.primaryText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // Timestamp
                    Text(
                      formatTime(date, context),
                      style: theme.textStyles.body2.copyWith(
                        color: theme.colors.tertiaryText,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                // Comment text
                Text(
                  commentText ??
                      "Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System's Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.",
                  style: theme.textStyles.body2
                      .copyWith(color: theme.colors.secondaryText),
                  textAlign: TextAlign.start,
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.fromSTEB(0, 6, 6, 4),
                  child: Row(
                    children: [
                      Icon(SolarIconsOutline.reply,
                          color: theme.colors.primary, size: 14),
                      const SizedBox(width: 4),
                      Text(
                        strings.reply,
                        style: theme.textStyles.buttonSmall
                            .copyWith(color: theme.colors.primary),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
