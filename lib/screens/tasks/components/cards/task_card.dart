import 'package:ako_basma/components/image/avatar_row.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class TaskCard extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final bool onContainer;
  const TaskCard({
    super.key,
    this.margin,
    this.onContainer = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;
    final assignees = List.of(dummyEmployees)..shuffle();
    final assigneesSample = assignees.take(3).toList();
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: onContainer ? colors.background : colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colors.primaryVariant),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Model Answer',
                    style: textStyles.headline4.copyWith(
                      color: colors.primaryText,
                      fontSize: 12,
                    ),
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Iconsax.task_copy,
                      size: 18,
                      color: colors.primaryVariantDark,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '4',
                      style: textStyles.headline.copyWith(
                        fontSize: 10,
                        color: colors.primaryVariantDark,
                        fontWeight: FontWeight.w800,
                        // height: 2.0,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                buildTagChip(
                  context,
                  '#UI007',
                  bgColor: colors.background,
                  borderColor: colors.strokeColor,
                  textColor: colors.secondaryText,
                ),
                buildTagChip(
                  context,
                  "Design",
                  bgColor: colors.successContainer,
                  textColor: colors.success,
                ),
                buildTagChip(
                  context,
                  strings.backlog,
                  bgColor: colors.warningContainer,
                  textColor: colors.warning,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AvatarRow(
                  imageUrls: assigneesSample.map((e) => e.imageUrl).toList(),
                  overlapFactor: 0.25,
                  restriction: 2,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Stack(
                    children: [
                      Container(
                        width: 31,
                        height: 31,
                        margin: const EdgeInsets.all(1),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: colors.background,
                        ),
                        child: Icon(
                          Icons.add_circle_outline,
                          size: 16,
                          color: colors.tertiaryText,
                        ),
                      ),
                      Positioned.fill(
                          child: DottedBorder(
                        borderType: BorderType.Circle,
                        color: colors.strokeColor,
                        strokeWidth: 2,
                        dashPattern: const [4, 4],
                        borderPadding: EdgeInsets.zero,
                        padding: EdgeInsets.zero,
                        child: const SizedBox(),
                      ))
                    ],
                  ),
                ),
                Spacer(),
                Row(
                  children: [
                    // Document icon
                    Row(
                      children: [
                        Icon(
                          SolarIconsOutline.file,
                          size: 20,
                          color: colors.secondaryText,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '2',
                          style: textStyles.headline4.copyWith(
                            fontSize: 12,
                            color: colors.secondaryText,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 8),
                    Row(
                      children: [
                        Icon(
                          SolarIconsOutline.chatRoundLine,
                          size: 20,
                          color: colors.warning,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '2',
                          style: textStyles.headline4.copyWith(
                            fontSize: 12,
                            color: colors.warning,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTagChip(BuildContext context, String label,
      {Color? bgColor, Color? borderColor, Color? textColor}) {
    final theme = AppTheme.of(context);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100),
        color: bgColor ?? theme.colors.primaryVariant,
        border: Border.all(
            color: borderColor ?? bgColor ?? theme.colors.primaryVariant),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
      child: Text(
        label,
        style:
            theme.textStyles.headline.copyWith(color: textColor, fontSize: 10),
      ),
    );

    // OutlinedButton(
    //   onPressed: () {},
    //   style: OutlinedButton.styleFrom(
    //     foregroundColor: textColor,
    //     backgroundColor: bgColor,
    //     side: BorderSide(width: 1, color: borderColor),
    //     padding: const EdgeInsets.fromLTRB(8, 5, 8, 5),
    //     minimumSize: const Size(0, 20),
    //     shape: RoundedRectangleBorder(
    //       borderRadius: BorderRadius.circular(16),
    //     ),
    //   ),
    //   child:
    // );
  }
}
