import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:solar_icons/solar_icons.dart';

class AssigneeChip extends StatelessWidget {
  const AssigneeChip({super.key, required this.data, this.onDelete});

  final EmployeeData data;
  final void Function()? onDelete;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colors.strokeColor,
          )),
      padding: const EdgeInsets.all(4),
      child: Row(
        children: [
          Container(
            height: 21,
            width: 21,
            margin: const EdgeInsets.all(1.5),
            clipBehavior: Clip.hardEdge,
            decoration: ShapeDecoration(
              shape: CircleBorder(),
            ),
            child: ImageContainer(
              url: data.imageUrl,
              height: 21,
              width: 21,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
              child: Text(
            data.name,
            style: theme.textStyles.body2.copyWith(
              color: theme.colors.secondaryText,
            ),
          )),
          if (onDelete != null)
            InkWell(
              onTap: onDelete,
              splashFactory: NoSplash.splashFactory,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                child: Icon(
                  Icons.close_rounded,
                  size: 16,
                  color: theme.colors.error,
                ).animate().fadeIn(),
              ),
            ),
        ],
      ),
    );
    ;
  }
}
