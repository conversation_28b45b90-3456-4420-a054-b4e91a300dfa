import 'package:ako_basma/components/animated_dropdown/theme_custom_dropdown.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/tasks/components/cards/task_card.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';

class TaskSearchPopup extends StatefulWidget {
  const TaskSearchPopup({
    super.key,
    this.sc,
  });
  final ScrollController? sc;
  @override
  State<TaskSearchPopup> createState() => _TaskSearchPopupState();
}

class _TaskSearchPopupState extends State<TaskSearchPopup> {
  final GlobalKey projectButtonKey = GlobalKey();
  final GlobalKey departmentButtonKey = GlobalKey();
  final GlobalKey assigneeButtonKey = GlobalKey();

  // State variables for selected values
  String? _selectedProjectId;
  String? _selectedDepartmentId;
  String? _selectedAssigneeId;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
final strings = AppLocalizations.of(context)!;

    // Helper function to build dropdown button

    // Sample items for context menus
    final projectItems = [
      {'label': 'Design Work', 'value': 'design_work'},
      {'label': 'Staff Application', 'value': 'staff_application'},
      {'label': 'Admin Application', 'value': 'admin_application'},
      {'label': 'Website Redesign', 'value': 'website_redesign'},
      if (_selectedProjectId != null)
        {
          'label': strings.clearSelection,
          'value': 'clear',
          'color': theme.colors.error,
          'onPressed': () {
            setState(() {
              _selectedProjectId = null;
            });
          }
        },
    ];

    final departmentItems = [
      {'label': 'Design', 'value': 'design'},
      {'label': 'HR', 'value': 'hr'},
      {'label': 'Finance', 'value': 'finance'},
      {'label': 'IT', 'value': 'it'},
      if (_selectedDepartmentId != null)
        {
          'label': strings.clearSelection,
          'value': 'clear',
          'color': theme.colors.error,
          'onPressed': () {
            setState(() {
              _selectedDepartmentId = null;
            });
          }
        },
    ];

    final assigneeItems = [
      {'label': 'John Doe', 'value': 'john_doe'},
      {'label': 'Jane Smith', 'value': 'jane_smith'},
      {'label': 'Mike Johnson', 'value': 'mike_johnson'},
      {'label': 'Sarah Wilson', 'value': 'sarah_wilson'},
      if (_selectedAssigneeId != null)
        {
          'label': strings.clearSelection,
          'value': 'clear',
          'color': theme.colors.error,
          'onPressed': () {
            setState(() {
              _selectedAssigneeId = null;
            });
          }
        },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              const SizedBox(width: 8),
              Expanded(
                child: SearchTextField(
                  hintText: strings.searchingForATask,
                ),
              ),
              InkWell(
                onTap: () {
                  context.pop();
                },
                splashFactory: NoSplash.splashFactory,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Text(
                    strings.cancel,
                    style: theme.textStyles.body.copyWith(
                      color: theme.colors.secondaryText,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
          child: Row(
            children: [
              // IntrinsicWidth(
              //   child: ConstrainedBox(
              //     constraints: BoxConstraints(minWidth: 110, maxWidth: 200),
              //     child: ThemeCustomDropdown(
              //       items: [
              //         {
              //           'item': ChipItem(
              //               label: 'All Departments', tag: 'all_departments')
              //         },
              //         {'item': ChipItem(label: 'Design', tag: 'design')},
              //         {'item': ChipItem(label: 'HR', tag: 'hr')},
              //         {'item': ChipItem(label: 'Finance', tag: 'finance')},
              //       ],
              //       initialItem: null,
              //       onChanged: (val) {},
              //       headerTextAlign: TextAlign.start,
              //       listItemTextAlign: TextAlign.start,
              //       hintText: 'Department',
              //     ),
              //   ),
              // ),
              _buildDropdownButton(
                buttonKey: projectButtonKey,
                items: projectItems,
                selectedId: _selectedProjectId,
                defaultText: strings.project,
                onItemPress: (value) {
                  setState(() {
                    _selectedProjectId = value;
                  });
                },
              ),
              const SizedBox(width: 8),
              _buildDropdownButton(
                buttonKey: departmentButtonKey,
                items: departmentItems,
                selectedId: _selectedDepartmentId,
                defaultText: strings.department,
                onItemPress: (value) {
                  setState(() {
                    _selectedDepartmentId = value;
                  });
                },
              ),
              const SizedBox(width: 8),

              _buildDropdownButton(
                buttonKey: assigneeButtonKey,
                items: assigneeItems,
                selectedId: _selectedAssigneeId,
                defaultText: strings.assignee,
                onItemPress: (value) {
                  setState(() {
                    _selectedAssigneeId = value;
                  });
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: Text(
            strings.recentlyViewed,
            style: theme.textStyles.headline4.copyWith(
              color: theme.colors.secondaryText,
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            controller: widget.sc,
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            itemCount: 5, // For now, show 5 sample tasks
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  context.pop();
                  context.go('/tasks/taskDetails');
                },
                child: const TaskCard(
                  onContainer: true,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownButton({
    required GlobalKey buttonKey,
    required List<Map<String, dynamic>> items,
    required String? selectedId,
    required String defaultText,
    required void Function(String) onItemPress,
  }) {
    final theme = AppTheme.of(context);
    return InkWell(
      key: buttonKey,
      splashFactory: NoSplash.splashFactory,
      onTap: () {
        showLocalContextMenu(
          context: context,
          buttonKey: buttonKey,
          items: items,
          menuPadding: const EdgeInsets.all(4),
          maxWidth: 150,
          textStyle: theme.textStyles.button.copyWith(fontSize: 14),
          onItemPress: (value) {
            setState(() {
              onItemPress(value.toString());
            });
          },
        );
      },
      child: AnimatedContainer(
        duration: 300.milliseconds,
        decoration: BoxDecoration(
            border: Border.all(
              color: selectedId == null
                  ? theme.colors.strokeColor
                  : theme.colors.primary,
            ),
            borderRadius: BorderRadius.circular(8),
            color: theme.colors.backgroundContainer),
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        child: Row(
          children: [
            Text(
              selectedId != null
                  ? _getLabelByValue(items, selectedId)
                  : defaultText,
              style: theme.textStyles.button
                  .copyWith(fontSize: 14, color: theme.colors.secondaryText),
            ),
            const SizedBox(width: 8),
            Icon(
              HugeIcons.strokeRoundedArrowDown01,
              size: 24,
              color: theme.colors.primary,
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to get label by value
  String _getLabelByValue(List<Map<String, dynamic>> items, String? value) {
    if (value == null) return '';
    final item = items.firstWhere(
      (item) => item['value'] == value,
      orElse: () => <String, String>{},
    );
    return item['label'] ?? '';
  }
}
