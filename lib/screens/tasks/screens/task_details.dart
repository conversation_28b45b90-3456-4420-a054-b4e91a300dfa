import 'dart:io';
import 'dart:math';

import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/animated_dropdown/theme_custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/components/form/attachment/attachment_grid_card.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/tasks/components/cards/assignee_chip.dart';
import 'package:ako_basma/screens/tasks/components/cards/comment_card.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class TaskDetailsScreen extends StatefulWidget {
  final VoidCallback? onBack;

  const TaskDetailsScreen({super.key, this.onBack});

  @override
  State<TaskDetailsScreen> createState() => _TaskDetailsScreenState();
}

class _TaskDetailsScreenState extends State<TaskDetailsScreen> {
  bool _editing = false;
  List<File> _attachments = [];
  DateTime? _deadlineDate;
  final _deadlineController = TextEditingController();
  final _commentController = TextEditingController();
  final _titleController = TextEditingController();
  List<EmployeeData> _assigneeList = [dummyEmployees[0], dummyEmployees[1]];
  final _assigneeController = MultiSelectController(
    [dummyEmployees[0], dummyEmployees[1]]
        .map((e) => ChipItem(label: e.name, tag: e.id.toString()))
        .toList(),
  );
  @override
  void initState() {
    _titleController.text = 'Change email option process';
    _assigneeController.addListener(() {
      print('printing for assignee controller');
      print(_assigneeController.value);
      setState(() {
        _assigneeList = _assigneeController.value
            .map((e) => dummyEmployees.firstWhere(
                (element) => element.id == int.tryParse(e.tag ?? "")))
            .toList();
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: theme.colors.background,
      appBar: MyAppbar(title: strings.taskDetails),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Main task card with dropdowns, description, attachment
            Container(
              decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // CardHeader

                  AnimatedSize(
                    duration: 1000.milliseconds,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.colors.background,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: theme.colors.strokeColor),
                          ),
                          child: Text(
                            '#UI007',
                            style: theme.textStyles.body.copyWith(
                              fontSize: 18,
                              color: theme.colors.secondaryText,
                            ),
                          ),
                        ),
                        !_editing
                            ? InkWell(
                                splashFactory: NoSplash.splashFactory,
                                onTap: () {
                                  setState(() {
                                    _editing = true;
                                  });
                                },
                                child: Padding(
                                  padding: const EdgeInsetsDirectional.only(
                                      start: 6),
                                  child: SvgPicture.asset(
                                    'assets/icons/edit.svg',
                                    height: 24,
                                    width: 24,
                                    color: theme.colors.secondaryText,
                                  ),
                                ),
                              )
                            : AppOutlinedButton.async(
                                label: strings.save,
                                onPressed: () async {
                                  await Future.delayed(1.seconds);
                                  setState(() {
                                    _editing = false;
                                  });
                                },
                                tintColor: theme.colors.primary,
                                expand: false,
                                padding: EdgeInsets.symmetric(vertical: 2),
                              ).animate().fadeIn()
                      ],
                    ),
                  ),

                  // TaskTitle
                  const SizedBox(height: 12),
                  if (!_editing)
                    Text(
                      _titleController.text,
                      style: Theme.of(context)
                          .extension<TextStyles>()!
                          .headline3
                          .copyWith(
                            color: Theme.of(context)
                                .extension<AppColors>()!
                                .primaryText,
                          ),
                    )
                  else
                    SimpleTextField(
                      controller: _titleController,
                      decoration: InputDecoration(labelText: strings.title),
                      readOnly: !_editing,
                    ),
                  const SizedBox(height: 16),
                  Divider(
                    height: 0,
                    color: theme.colors.strokeColor,
                  ),
                  const SizedBox(height: 24),

                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    decoration: BoxDecoration(
                      color: theme.colors.background,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        //  status selectionm
                        Row(
                          children: [
                            Expanded(
                                flex: 45,
                                child: Padding(
                                  padding:
                                      const EdgeInsetsDirectional.only(end: 8),
                                  child: Text(
                                    strings.status,
                                    style: theme.textStyles.body.copyWith(
                                      color: theme.colors.tertiaryText,
                                    ),
                                  ),
                                )),
                            Expanded(
                              flex: 55,
                              child: ThemeCustomDropdown(
                                items: [
                                  {
                                    'item': ChipItem(
                                        label: strings.backlog, tag: 'backlog'),
                                    'color': theme.colors.warning
                                  },
                                  {
                                    'item': ChipItem(
                                        label: strings.inProgress,
                                        tag: 'in_progress'),
                                    'color': theme.colors.info
                                  },
                                  {
                                    'item': ChipItem(
                                        label: strings.review, tag: 'review'),
                                    'color': theme.colors.secondary
                                  },
                                  {
                                    'item': ChipItem(
                                        label: strings.completed,
                                        tag: 'completed'),
                                    'color': theme.colors.success
                                  },
                                ],
                                enabled: _editing,
                                initialItem: ChipItem(
                                    label: strings.backlog, tag: 'backlog'),
                                onChanged: (val) {},
                                closedHeaderPadding: EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 16),
                                headerTextStyle: theme.textStyles.buttonSmall
                                    .copyWith(fontSize: 14),
                                listItemTextStyle: theme.textStyles.buttonSmall
                                    .copyWith(fontSize: 14),
                                // ...other CustomDropdown params as needed
                              ),
                            ),
                          ],
                        ),
                        // const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                                flex: 45,
                                child: Padding(
                                  padding:
                                      const EdgeInsetsDirectional.only(end: 8),
                                  child: Text(
                                    strings.deadline,
                                    style: theme.textStyles.body.copyWith(
                                      color: theme.colors.tertiaryText,
                                    ),
                                  ),
                                )),
                            Expanded(
                              flex: 55,
                              child: SimpleTextField(
                                controller: _deadlineController,
                                onTap: _editing ? () => _selectDate() : null,
                                readOnly: true,
                                decoration: InputDecoration(
                                    isDense: true,
                                    hintText: strings.deadline,
                                    contentPadding: EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16)),
                              ),
                            ),
                          ],
                        ),
                        // department selection
                        Row(
                          children: [
                            Expanded(
                                flex: 45,
                                child: Padding(
                                  padding:
                                      const EdgeInsetsDirectional.only(end: 8),
                                  child: Text(
                                    strings.department,
                                    style: theme.textStyles.body.copyWith(
                                      color: theme.colors.tertiaryText,
                                    ),
                                  ),
                                )),
                            Expanded(
                              flex: 55,
                              child: ThemeCustomDropdown(
                                items: [
                                  {
                                    'item': ChipItem(
                                        label: 'Design', tag: 'design'),
                                  },
                                  {
                                    'item': ChipItem(
                                        label: 'Development',
                                        tag: 'development'),
                                  },
                                  {
                                    'item': ChipItem(label: 'QA', tag: 'qa'),
                                  },
                                  {
                                    'item': ChipItem(
                                        label: 'Marketing', tag: 'marketing'),
                                  },
                                ],
                                enabled: _editing,
                                initialItem:
                                    ChipItem(label: 'Design', tag: 'design'),
                                onChanged: (val) {},
                                closedHeaderPadding: EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 16),
                                headerTextStyle: theme.textStyles.buttonSmall
                                    .copyWith(fontSize: 14),
                                listItemTextStyle: theme.textStyles.buttonSmall
                                    .copyWith(fontSize: 14),

                                // ...other CustomDropdown params as needed
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),

                        AnimatedSize(
                            duration: 300.milliseconds,
                            alignment: Alignment.topCenter,
                            child: _buildAssigneesSection()),
                      ],
                    ),
                  ),
                  //
                  const SizedBox(height: 24),
                  SimpleTextField(
                    initialValue:
                        'Design A User Interface For The [Interface Description] Page, Ensuring It Aligns With The User Experience And Reflects The System\'s Visual Identity. The Design Includes Organizing Content, Defining Interactions, And Ensuring Ease Of Use And Navigation Within The Interface.',
                    decoration: InputDecoration(labelText: strings.description),
                    maxLines: 10,
                    minLines: 9,
                    readOnly: !_editing,
                  ),
                  SectionHeading(
                    title: '${strings.attachment} (2)',
                    padding: EdgeInsets.fromLTRB(0, 24, 0, 16),
                    action: InkWell(
                      onTap: () {},
                      splashFactory: NoSplash.splashFactory,
                      child: Padding(
                        padding: const EdgeInsetsDirectional.only(start: 4),
                        child: Row(
                          children: [
                            Icon(
                              Iconsax.document_download_copy,
                              color: theme.colors.primary,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              strings.downloadAll,
                              style: theme.textStyles.body3
                                  .copyWith(color: theme.colors.primary),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),

                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        SizedBox(
                          width: 150,
                          child: AttachmentPlaceholderCard(
                            url: 'https://pdfobject.com/pdf/sample.pdf',
                            previewHeight: 100,
                            previewWidth: 114,
                            onDelete: _editing ? () {} : null,
                          ),
                        ),
                        SizedBox(
                          width: 150,
                          child: AttachmentPlaceholderCard(
                            url: 'https://pdfobject.com/pdf/sample.pdf',
                            previewHeight: 100,
                            previewWidth: 114,
                            onDelete: _editing ? () {} : null,
                          ),
                        ),
                        ..._attachments.map(
                          (e) => SizedBox(
                            width: 150,
                            child: AttachmentPlaceholderCard(
                              filePath: e.path,
                              previewHeight: 100,
                              previewWidth: 114,
                              onDelete: _editing ? () {} : null,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (_editing)
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: AttachmentPlaceholderCard(
                        preset: 'other',
                        onTapPos: (offset) => _pickFiles(offset),
                      ),
                    ),
                  // AttachmentSection
                  // For brevity, you can inline or import AttachmentSection as needed
                ],
              ),
            ),

            const SizedBox(height: 16),
            // Inline CommentSection
            Container(
              decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colors.strokeColor),
              ),
              padding: const EdgeInsets.fromLTRB(0, 16, 0, 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section title
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _commentController,
                          decoration: InputDecoration(
                              border: InputBorder.none,
                              labelText: strings.addAComment,
                              labelStyle: theme.textStyles.body2.copyWith(
                                color: theme.colors.tertiaryText,
                              ),
                              isDense: true,
                              contentPadding: const EdgeInsets.symmetric(
                                  vertical: 2, horizontal: 18)),
                          maxLines: 1,
                          minLines: 1,
                          onChanged: (value) {
                            setState(() {});
                          },
                        ),
                      ),
                      AnimatedOpacity(
                        duration: 200.milliseconds,
                        opacity: _commentController.text.trim().isEmpty ? 0 : 1,
                        child: Padding(
                          padding:
                              const EdgeInsetsDirectional.fromSTEB(4, 0, 16, 0),
                          child: Icon(Iconsax.send_2_copy,
                              color: theme.colors.primary),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),
                  Builder(builder: (context) {
                    final presets = [
                      ChipItem(
                          label: strings.canIGetMoreInfo, tag: 'more_info'),
                      ChipItem(
                          label: strings.statusUpdate, tag: 'status_update'),
                      ChipItem(label: strings.statusUpdate, tag: 'thanks')
                    ];
                    return ChipSelector(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      items: presets,
                      selectedItems: const [],
                      onItemTap: (item) {
                        setState(() {
                          _commentController.text = presets
                              .firstWhere((element) => element.tag == item)
                              .label;
                        });
                      },
                      unselectedChipColor: theme.colors.background,
                      unselectedBorderColor: theme.colors.strokeColor,
                      borderRadius: BorderRadius.circular(8),
                      unselectedTextStyle: theme.textStyles.body2
                          .copyWith(color: theme.colors.secondaryText),
                    );
                  })
                ],
              ),
            ),
            const SizedBox(height: 16),

            Container(
              decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colors.strokeColor),
              ),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Comments header with count
                  Text(
                    '${strings.comments} (3)',
                    style: theme.textStyles.buttonSmall.copyWith(
                      fontSize: 14,
                      color: theme.colors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Comments list
                  ...(
                      // replace.
                      [1, 1, 1]
                          .indexed
                          .map(
                            (e) => Padding(
                              padding: EdgeInsetsGeometry.only(
                                  bottom: e.$1 ==
                                          (
                                              // replace.
                                              [1, 1, 1].length - 1)
                                      ? 0
                                      : 16),
                              child: CommentCard(
                                imageUrl:
                                    'https://randomuser.me/api/portraits/men/32.jpg',
                                userName: 'John Doe',
                                date: DateTime.now(),
                              ),
                            ),
                          )
                          .toList()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssigneesSection() {
    final strings = AppLocalizations.of(context)!;
    final theme = AppTheme.of(context);
    if (!_editing) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
// assignee list..
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Expanded(
                flex: 45,
                child: Text(
                  strings.assignee,
                  style: theme.textStyles.body.copyWith(
                    color: theme.colors.tertiaryText,
                  ),
                )),
            Expanded(
              flex: 55,
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxHeight: 150),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    spacing: 8,
                    children: [
                      ..._assigneeList.map(
                        (e) => AssigneeChip(
                          data: e,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ]),
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          strings.assignee,
          style: theme.textStyles.body.copyWith(
            color: theme.colors.tertiaryText,
          ),
        ),
        CustomDropdown.multiSelectSearch(
          multiSelectController: _assigneeController,
          items: dummyEmployees
              .map((e) => ChipItem(label: e.name, tag: e.id.toString()))
              .toList(),
          onListChanged: (items) {},
          closedHeaderPadding:
              const EdgeInsets.symmetric(vertical: 6, horizontal: 18),
          hintText: strings.selectAssignees,
          headerListBuilder: (context, items, enabled) => Text(
              '${strings.selectAssignees} ${items.isNotEmpty ? '(${items.length})' : ''}'),
        ),
        if (_assigneeController.value.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              spacing: 8,
              children: [
                ..._assigneeList.map(
                  (e) => AssigneeChip(
                    data: e,
                    onDelete: () {
                      // setState(() {
                      _assigneeController.remove(
                          ChipItem(label: e.name, tag: e.id.toString()));
                      // });
                    },
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<void> _pickFiles(Offset? offset) async {
    try {
      final res = await showLocalPickerMenu(
        pointOffset: offset,
        context: context,
        allowedTypes: ['any'],
        allowMultiple: true,
        maxSizeInMB: 25,
      );
      setState(() {
        if (res is File) {
          _attachments.add(res);
        }
        if (res is List<File>) {
          _attachments.addAll(res);
        }
      });
    } catch (e) {
      debugPrint('Error picking files: $e');
    }
  }

  Future<void> _selectDate() async {
    final initialDate = _deadlineDate ?? DateTime.now();
    final DateTime? picked = await showDatePickerDialog(
        context: context,
        initialDate: initialDate,
        selectedDate: _deadlineDate,
        minDate: DateTime(2000),
        maxDate: DateTime(2100),
        onClearTap: () {
          setState(() {
            _deadlineDate = null;
            _deadlineController.clear();
          });
        });
    if (picked != null) {
      setState(() {
        _deadlineDate = picked;
        _deadlineController.text = formatDateDmy(picked, context);
      });
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    _assigneeController.dispose();
    _titleController.dispose();
    _deadlineController.dispose();
    _attachments.clear();
    _assigneeList.clear();
    super.dispose();
  }
}
