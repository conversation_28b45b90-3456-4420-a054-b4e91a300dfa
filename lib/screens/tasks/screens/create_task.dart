import 'dart:io';

import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/chip_selector.dart';

class CreateTaskScreen extends StatefulWidget {
  final VoidCallback? onBack;
  final Function? onSuccess;

  const CreateTaskScreen({
    super.key,
    this.onBack,
    this.onSuccess,
  });

  @override
  State<CreateTaskScreen> createState() => _CreateTaskScreenState();
}

class _CreateTaskScreenState extends State<CreateTaskScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final _attachmentButtonKey = GlobalKey();
  List<File> attachments = [];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickFiles(Offset? offset) async {
    try {
      final res = await showLocalPickerMenu(
        pointOffset: offset,
        context: context,
        allowedTypes: ['any'],
        allowMultiple: true,
        maxSizeInMB: 25,
      );
      setState(() {
        attachments.addAll(res);
      });
    } catch (e) {
      debugPrint('Error picking files: $e');
    }
  }

  void _removeAttachment(int index) {
    setState(() {
      attachments.removeAt(index);
    });
  }

  Future<void> _handleAddTask() async {
    final title = _titleController.text.trim();
    final strings = AppLocalizations.of(context)!;

    if (title.isEmpty) {
      return;
    }
    try {
      // Simulate project creation
      await Future.delayed(1.seconds);
      final tag = title.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]+'), '_');
      final chip = ChipItem(label: title, tag: tag);
      widget.onSuccess?.call(chip);
      Navigator.pop(context, true);
    } catch (e) {
      showAppSnackbar(context, title: strings.someErrorOcurred, type: 'error');
      widget.onSuccess?.call(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      backgroundColor: colors.background,
      appBar: MyAppbar(
        title: strings.addTask,
        onBack: widget.onBack ?? () => Navigator.pop(context),
      ),
      body: Column(
        children: [
          // Form content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.fromLTRB(16, 16, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Title field
                  SimpleTextField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: strings.title,
                    ),
                    onChanged: (_) {
                      setState(() {});
                    },
                  ),
                  const SizedBox(height: 8),
                  SimpleTextField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: strings.description,
                    ),
                    onChanged: (_) {
                      setState(() {});
                    },
                  ),
                  const SizedBox(height: 8),

                  CustomDropdown(
                    items: [
                      ChipItem(label: strings.critical, tag: 'critical'),
                      ChipItem(label: strings.high, tag: 'high'),
                      ChipItem(label: strings.medium, tag: 'medium'),
                      ChipItem(label: strings.low, tag: 'low'),
                    ],
                    onChanged: (value) {},
                    hintText: strings.priority,
                  ),

                  const SizedBox(height: 8),

                  CustomDropdown(
                    items: [
                      ChipItem(label: 'Person A', tag: 'person_a'),
                      ChipItem(label: 'Person B', tag: 'person_b'),
                      ChipItem(label: 'Person C', tag: 'person_c'),
                      ChipItem(label: 'Person D', tag: 'person_d'),
                    ],
                    onChanged: (value) {},
                    hintText: strings.assign,
                  ),
                  const SizedBox(height: 10),

                  // File upload area
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      AttachmentPlaceholderCard(
                        key: _attachmentButtonKey,
                        preset: 'upload',
                        onTapPos: (pos) => _pickFiles(pos),
                      ),
                      const SizedBox(height: 10),

                      if (attachments.isNotEmpty)
                        ...attachments.asMap().entries.map((entry) => Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: AttachmentPlaceholderCard(
                                preset: 'other',
                                filePath: entry.value.path,
                                onDelete: () => _removeAttachment(entry.key),
                              ),
                            )),
                      // Add new document card
                    ],
                  ),
                ],
              ),
            ),
          ),
          PrimaryButton.async(
            label: strings.save,
            onPressed: _handleAddTask,
            wrapBottomPadding: true,
          ),
        ],
      ),
    );
  }
}
