import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/radio_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';

class ProjectSelectionPopup extends StatefulWidget {
  final String selectedId;
  // replace with projectData
  final List<ChipItem> projects;
  final Function(String) onSelected;
  final ScrollController? sc;
  final void Function(ChipItem) onCreate;

  const ProjectSelectionPopup({
    super.key,
    required this.selectedId,
    required this.projects,
    required this.onSelected,
    this.sc,
    required this.onCreate,
  });

  @override
  State<ProjectSelectionPopup> createState() => _ProjectSelectionPopupState();
}

class _ProjectSelectionPopupState extends State<ProjectSelectionPopup> {
  String? _selectedId;

  @override
  void initState() {
    super.initState();
    _selectedId = widget.selectedId;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    final radioItems = widget.projects.indexed
        .map((e) => Padding(
              padding: const EdgeInsetsGeometry.only(bottom: 12),
              child: AppRadioButton.tile(
                value: e.$2.tag ?? "",
                groupValue: _selectedId ?? "",
                onChanged: (String? value) {
                  setState(() {
                    _selectedId = value;
                  });
                },
                // localize
                label: e.$2.label,
              ),
            ))
        .toList();
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header
        Flexible(
          // fit: FlexFit.tight,
          child: SingleChildScrollView(
            controller: widget.sc,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        strings.projects,
                        style: textStyles.textButton.copyWith(
                          color: colors.primaryVariantDark,
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        context.push('/tasks/newProject', extra: {
                          'onSuccess': (chip) {
                            if (chip is ChipItem) {
                              context.pop();
                              widget.onCreate(chip);
                            }
                          }
                        });
                      },
                      splashFactory: NoSplash.splashFactory,
                      child: Row(
                        children: [
                          Icon(
                            HugeIcons.strokeRoundedAdd01,
                            size: 12,
                            color: colors.primary,
                          ),
                          const SizedBox(width: 5),
                          Text(
                            strings.create,
                            style: textStyles.textButton.copyWith(
                                color: colors.secondaryText, fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (radioItems.isEmpty)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Text(
                      strings.noProjectFound,
                      style:
                          textStyles.body.copyWith(color: colors.secondaryText),
                    ),
                  )
                else
                  ...radioItems,
              ],
            ),
          ),
        ),
        // Buttons
        if (radioItems.isEmpty)
          AppOutlinedButton(
              label: strings.back,
              onTap: () {
                context.pop();
              })
        else
          FooterFormButton(
            onCancel: () {
              context.pop();
            },
            onSubmit: () {
              // some saved pref logic
              widget.onSelected(_selectedId ?? "");
              context.pop();
            },
          )
      ],
    );
  }
}
