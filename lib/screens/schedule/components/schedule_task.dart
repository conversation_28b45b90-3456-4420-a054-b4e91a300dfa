import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:intl/intl.dart';

class ScheduleTaskModal extends StatefulWidget {
  final String title;
  final Function(Map<String, dynamic>)? onAdd;
  final Function()? onCancel;

  const ScheduleTaskModal({
    super.key,
    required this.title,
    this.onAdd,
    this.onCancel,
  });

  @override
  State<ScheduleTaskModal> createState() => _ScheduleTaskModalState();
}

class _ScheduleTaskModalState extends State<ScheduleTaskModal> {
  // Radio button selection
  String _selectedAssignmentType = 'Specific Employees';

  // Form controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();

  // Dropdown selections
  ChipItem? _selectedAssign;
  ChipItem? _selectedPriority;

  // Selected date and times
  DateTime? _selectedDate;
  TimeOfDay? _selectedStartTime;
  TimeOfDay? _selectedEndTime;

  // File attachments
  final List<File> _attachments = [];
  final _attachmentButtonKey = GlobalKey();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _dateController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    _attachments.clear();
    super.dispose();
  }

  Future<void> _pickFiles(Offset? offset) async {
    try {
      final res = await showLocalPickerMenu(
        pointOffset: offset,
        context: context,
        allowedTypes: ['any'],
        allowMultiple: true,
        maxSizeInMB: 25,
      );
      setState(() {
        if (res is File) {
          _attachments.add(res);
        }
        if (res is List<File>) {
          _attachments.addAll(res);
        }
      });
    } catch (e) {
      debugPrint('Error picking files: $e');
    }
  }

  void _removeAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  void _showDatePicker() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme,
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = formatDateDmy(picked, context);
      });
    }
  }

  void _showStartTimePicker() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedStartTime ?? TimeOfDay.now(),
    );

    if (picked != null && picked != _selectedStartTime) {
      setState(() {
        _selectedStartTime = picked;
        _startTimeController.text = picked.format(context);

        // Auto-set end time to 1 hour later if not set
        if (_selectedEndTime == null) {
          final endTime = TimeOfDay(
            hour: (picked.hour + 1) % 24,
            minute: picked.minute,
          );
          _selectedEndTime = endTime;
          _endTimeController.text = endTime.format(context);
        }
      });
    }
  }

  void _showEndTimePicker() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedEndTime ??
          (_selectedStartTime != null
              ? TimeOfDay(
                  hour: (_selectedStartTime!.hour + 1) % 24,
                  minute: _selectedStartTime!.minute,
                )
              : TimeOfDay.now()),
    );

    if (picked != null && picked != _selectedEndTime) {
      setState(() {
        _selectedEndTime = picked;
        _endTimeController.text = picked.format(context);
      });
    }
  }

  void _handleAdd() {
    final data = {
      'assignmentType': _selectedAssignmentType,
      'assign': _selectedAssign?.label,
      'title': _titleController.text,
      'description': _descriptionController.text,
      'priority': _selectedPriority?.label,
      'date': _selectedDate,
      'startTime': _selectedStartTime,
      'endTime': _selectedEndTime,
      'attachments': _attachments,
    };

    widget.onAdd?.call(data);
    Navigator.of(context).pop();
  }

  void _handleCancel() {
    widget.onCancel?.call();
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;
    final strings = AppLocalizations.of(context)!;

    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(40)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            child: Text(
              widget.title,
              style: textStyles.headline3.copyWith(
                fontWeight: FontWeight.bold,
                color: colors.primaryText,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Select Section
                  Text(
                    'Select',
                    style: textStyles.body.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Radio buttons
                  _buildRadioGroup(),
                  const SizedBox(height: 24),

                  // Assign Field
                  _buildDropdownField(
                    label: 'Assign',
                    hint: 'Assign',
                    selectedItem: _selectedAssign,
                    items: [
                      const ChipItem(label: 'Employee 1', tag: 'emp1'),
                      const ChipItem(label: 'Employee 2', tag: 'emp2'),
                      const ChipItem(label: 'Employee 3', tag: 'emp3'),
                      const ChipItem(label: 'Employee 4', tag: 'emp4'),
                    ],
                    onChanged: (item) {
                      setState(() {
                        _selectedAssign = item;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Title Field
                  _buildTextField(
                    controller: _titleController,
                    label: 'Title',
                    hint: 'Title',
                  ),
                  const SizedBox(height: 16),

                  // Description Field
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'Description',
                    hint: 'Description',
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),

                  // Priority Section
                  _buildDropdownField(
                    label: 'Priority',
                    hint: 'Select Priority',
                    selectedItem: _selectedPriority,
                    items: [
                      const ChipItem(label: 'High', tag: 'high'),
                      const ChipItem(label: 'Medium', tag: 'medium'),
                      const ChipItem(label: 'Low', tag: 'low'),
                    ],
                    onChanged: (item) {
                      setState(() {
                        _selectedPriority = item;
                      });
                    },
                  ),
                  const SizedBox(height: 24),

                  // file picker
                  _buildFilePicker(),

                  // Date Field
                  _buildDateField(),
                  const SizedBox(height: 16),

                  // Time Fields
                  Row(
                    children: [
                      Expanded(
                        child: _buildTimeField(
                          label: 'Start Time',
                          hint: 'Select start time',
                          controller: _startTimeController,
                          onTap: _showStartTimePicker,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTimeField(
                          label: 'End Time',
                          hint: 'Select end time',
                          controller: _endTimeController,
                          onTap: _showEndTimePicker,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10), // Space for bottom buttons
                ],
              ),
            ),
          ),

          // Bottom Action Buttons
          Container(
            padding: const EdgeInsetsDirectional.all(24),
            child: Row(
              children: [
                Flexible(
                  flex: 2,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1.5,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _handleCancel,
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          alignment: Alignment.center,
                          child: Text(
                            strings.cancel,
                            style:
                                AppTheme.of(context).textStyles.body.copyWith(
                                      color: colors.secondaryText,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 16,
                                    ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Flexible(
                  flex: 3,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppTheme.of(context).colors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _handleAdd,
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          alignment: Alignment.center,
                          child: Text(
                            'Add',
                            style:
                                AppTheme.of(context).textStyles.body.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 16,
                                    ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioGroup() {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    final options = [
      'All Employees',
      'Managers Only',
      'Specific Department',
      'Specific Employees',
    ];

    return Column(
      children: options.map((option) {
        return RadioListTile<String>(
          title: Text(
            option,
            style: textStyles.body.copyWith(
              color: colors.primaryText,
            ),
          ),
          value: option,
          groupValue: _selectedAssignmentType,
          onChanged: (value) {
            setState(() {
              _selectedAssignmentType = value!;
            });
          },
          activeColor: colors.primary,
          contentPadding: EdgeInsets.zero,
          visualDensity: VisualDensity.compact,
          dense: true,
        );
      }).toList(),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required ChipItem? selectedItem,
    required List<ChipItem> items,
    required Function(ChipItem?) onChanged,
  }) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        CustomDropdown<ChipItem>(
          items: items,
          initialItem: selectedItem,
          hintText: hint,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    IconData? suffixIcon,
    int maxLines = 1,
  }) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: textStyles.body.copyWith(
              color: colors.tertiaryText,
            ),
            suffixIcon: suffixIcon != null
                ? Icon(
                    suffixIcon,
                    color: colors.tertiaryText,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colors.strokeColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colors.strokeColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colors.primary),
            ),
            filled: true,
            fillColor: colors.background,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          style: textStyles.body.copyWith(
            color: colors.primaryText,
          ),
        ),
      ],
    );
  }

  Widget _buildFilePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Attachments',
          style: AppTheme.of(context).textStyles.body.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.of(context).colors.primaryText,
              ),
        ),
        const SizedBox(height: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            AttachmentPlaceholderCard(
              key: _attachmentButtonKey,
              preset: 'upload',
              onTapPos: (pos) => _pickFiles(pos),
            ),
            const SizedBox(height: 10),
            if (_attachments.isNotEmpty)
              ..._attachments.asMap().entries.map((entry) => Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: AttachmentPlaceholderCard(
                      preset: 'other',
                      filePath: entry.value.path,
                      onDelete: () => _removeAttachment(entry.key),
                    ),
                  )),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField() {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date',
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _showDatePicker,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colors.strokeColor),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _dateController,
                    enabled: false,
                    decoration: InputDecoration(
                      hintText: 'Select date',
                      hintStyle: textStyles.body.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                    style: textStyles.body.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: colors.tertiaryText,
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeField({
    required String label,
    required String hint,
    required TextEditingController controller,
    required VoidCallback onTap,
  }) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colors.strokeColor),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    enabled: false,
                    decoration: InputDecoration(
                      hintText: hint,
                      hintStyle: textStyles.body.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                    style: textStyles.body.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ),
                Icon(
                  Icons.access_time,
                  color: colors.tertiaryText,
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Helper function to show the modal
Future<void> showScheduleTaskModal({
  required BuildContext context,
  required String title,
  Function(Map<String, dynamic>)? onAdd,
  Function()? onCancel,
}) async {
  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    useSafeArea: true,
    builder: (context) => ScheduleTaskModal(
      title: title,
      onAdd: onAdd,
      onCancel: onCancel,
    ),
  );
}
