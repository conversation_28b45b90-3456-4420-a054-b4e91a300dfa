import 'package:flutter/material.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:intl/intl.dart';

class AddHolidayModal extends StatefulWidget {
  final Function(Map<String, dynamic>)? onAdd;
  final Function()? onCancel;
  final String title;

  const AddHolidayModal({
    super.key,
    this.onAdd,
    this.onCancel,
    required this.title,
  });

  @override
  State<AddHolidayModal> createState() => _AddHolidayModalState();
}

class _AddHolidayModalState extends State<AddHolidayModal> {
  // Radio button selection
  String _selectedHolidayType = 'One Time';

  // Form controllers
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _startTimeController = TextEditingController();
  final TextEditingController _endTimeController = TextEditingController();

  // Selected date and times
  DateTime? _selectedDate;
  TimeOfDay? _selectedStartTime;
  TimeOfDay? _selectedEndTime;
  bool _isAllDay = true;

  @override
  void dispose() {
    _titleController.dispose();
    _dateController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  void _handleAdd() {
    final data = {
      'holidayType': _selectedHolidayType,
      'title': _titleController.text,
      'date': _selectedDate,
      'isAllDay': _isAllDay,
      'startTime': _isAllDay ? null : _selectedStartTime,
      'endTime': _isAllDay ? null : _selectedEndTime,
    };

    widget.onAdd?.call(data);
    Navigator.of(context).pop();
  }

  void _handleCancel() {
    widget.onCancel?.call();
    Navigator.of(context).pop();
  }

  void _showDatePicker() async {
    final DateTime? picked = await showDatePickerDialog(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      selectedDate: _selectedDate,
      minDate: DateTime(2000),
      maxDate: DateTime(2100),
      onClearTap: () {
        setState(() {
          _selectedDate = null;
          _dateController.clear();
        });
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = formatDateDmy(picked, context);
      });
    }
  }

  void _showStartTimePicker() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedStartTime ?? TimeOfDay.now(),
    );

    if (picked != null && picked != _selectedStartTime) {
      setState(() {
        _selectedStartTime = picked;
        _startTimeController.text = picked.format(context);

        // Auto-set end time to 1 hour later if not set
        if (_selectedEndTime == null) {
          final endTime = TimeOfDay(
            hour: (picked.hour + 1) % 24,
            minute: picked.minute,
          );
          _selectedEndTime = endTime;
          _endTimeController.text = endTime.format(context);
        }
      });
    }
  }

  void _showEndTimePicker() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedEndTime ??
          (_selectedStartTime != null
              ? TimeOfDay(
                  hour: (_selectedStartTime!.hour + 1) % 24,
                  minute: _selectedStartTime!.minute,
                )
              : TimeOfDay.now()),
    );

    if (picked != null && picked != _selectedEndTime) {
      setState(() {
        _selectedEndTime = picked;
        _endTimeController.text = picked.format(context);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;
    final strings = AppLocalizations.of(context)!;

    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(40)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            child: Text(
              'Add Holiday',
              style: textStyles.headline3.copyWith(
                fontWeight: FontWeight.bold,
                color: colors.primaryText,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Select Holiday Type Section
                  Text(
                    'Select Holiday Type',
                    style: textStyles.body.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colors.primaryText,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Radio buttons
                  _buildRadioGroup(),
                  const SizedBox(height: 24),

                  // Title Field
                  _buildTextField(),
                  const SizedBox(height: 24),

                  // Date Field
                  _buildDateField(),
                  const SizedBox(height: 16),

                  // All Day Toggle
                  Row(
                    children: [
                      Text(
                        'All Day',
                        style: textStyles.body.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colors.primaryText,
                        ),
                      ),
                      const Spacer(),
                      Switch(
                        value: _isAllDay,
                        onChanged: (value) {
                          setState(() {
                            _isAllDay = value;
                            if (value) {
                              _startTimeController.clear();
                              _endTimeController.clear();
                              _selectedStartTime = null;
                              _selectedEndTime = null;
                            }
                          });
                        },
                        activeColor: colors.primary,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Time Fields (only show if not all day)
                  if (!_isAllDay) ...[
                    Row(
                      children: [
                        Expanded(
                          child: _buildTimeField(
                            label: 'Start Time',
                            hint: 'Select start time',
                            controller: _startTimeController,
                            onTap: _showStartTimePicker,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildTimeField(
                            label: 'End Time',
                            hint: 'Select end time',
                            controller: _endTimeController,
                            onTap: _showEndTimePicker,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],

                  const SizedBox(height: 100), // Space for bottom buttons
                ],
              ),
            ),
          ),

          // Bottom Action Buttons
          Container(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Flexible(
                  flex: 2,
                  child: AppOutlinedButton(
                    label: strings.cancel,
                    onTap: _handleCancel,
                    expand: true,
                  ),
                ),
                const SizedBox(width: 16),
                Flexible(
                  flex: 3,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: colors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: _handleAdd,
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          alignment: Alignment.center,
                          child: Text(
                            'Add',
                            style: textStyles.body.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioGroup() {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    final options = [
      'One Time',
      'Every year',
    ];

    return Row(
      children: options.map((option) {
        return Expanded(
          child: RadioListTile<String>(
            title: Text(
              option,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
              ),
            ),
            value: option,
            groupValue: _selectedHolidayType,
            onChanged: (value) {
              setState(() {
                _selectedHolidayType = value!;
              });
            },
            activeColor: colors.primary,
            contentPadding: EdgeInsets.zero,
            visualDensity: VisualDensity.compact,
            dense: true,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTextField() {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Title',
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _titleController,
          decoration: InputDecoration(
            hintText: 'Title',
            hintStyle: textStyles.body.copyWith(
              color: colors.tertiaryText,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colors.strokeColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colors.strokeColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colors.primary),
            ),
            filled: true,
            fillColor: colors.background,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          style: textStyles.body.copyWith(
            color: colors.primaryText,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date',
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _showDatePicker,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colors.strokeColor),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _dateController,
                    enabled: false,
                    decoration: InputDecoration(
                      hintText: 'Select date',
                      hintStyle: textStyles.body.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                    style: textStyles.body.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: colors.tertiaryText,
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeField({
    required String label,
    required String hint,
    required TextEditingController controller,
    required VoidCallback onTap,
  }) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: textStyles.body.copyWith(
            fontWeight: FontWeight.w600,
            color: colors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colors.strokeColor),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller,
                    enabled: false,
                    decoration: InputDecoration(
                      hintText: hint,
                      hintStyle: textStyles.body.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                    style: textStyles.body.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ),
                Icon(
                  Icons.access_time,
                  color: colors.tertiaryText,
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// Helper function to show the modal
Future<void> showAddHolidayModal({
  required BuildContext context,
  Function(Map<String, dynamic>)? onAdd,
  Function()? onCancel,
}) async {
  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    useSafeArea: true,
    builder: (context) => AddHolidayModal(
      title: AppLocalizations.of(context)!.addHoliday,
      onAdd: onAdd,
      onCancel: onCancel,
    ),
  );
}
