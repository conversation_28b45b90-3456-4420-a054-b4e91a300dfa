// only for testing

import 'package:flutter/material.dart';
import 'package:ako_basma/screens/schedule/components/schedule_task.dart';

class ScheduleTaskExample extends StatelessWidget {
  const ScheduleTaskExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Schedule Task Example'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            showScheduleTaskModal(
              context: context,
              title: 'Schedule a Task',
              onAdd: (data) {
                // Handle the task data
                print('Task Data: $data');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Task scheduled: ${data['title']}'),
                  ),
                );
              },
              onCancel: () {
                print('Task scheduling cancelled');
              },
            );
          },
          child: const Text('Open Schedule Task Modal'),
        ),
      ),
    );
  }
}
