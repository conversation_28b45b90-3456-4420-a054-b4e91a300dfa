// only for testing

import 'package:flutter/material.dart';
import 'package:ako_basma/screens/schedule/components/add_holiday.dart';

class AddHolidayExample extends StatelessWidget {
  const AddHolidayExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Holiday Example'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            showAddHolidayModal(
              context: context,
              onAdd: (data) {
                // Handle the holiday data
                print('Holiday Data: $data');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Holiday added: ${data['title']}'),
                  ),
                );
              },
              onCancel: () {
                print('Holiday addition cancelled');
              },
            );
          },
          child: const Text('Open Add Holiday Modal'),
        ),
      ),
    );
  }
}
