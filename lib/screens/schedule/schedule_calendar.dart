import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/tab_selector.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/schedule/components/add_event.dart';
import 'package:ako_basma/screens/schedule/components/add_holiday.dart';
import 'package:ako_basma/screens/schedule/components/schedule_task.dart';
import 'package:ako_basma/screens/schedule/widgets/coming.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:kalender/kalender.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:intl/intl.dart';

class ScheduleCalendarScreen extends StatefulWidget {
  const ScheduleCalendarScreen({super.key});

  @override
  State<ScheduleCalendarScreen> createState() => _ScheduleCalendarScreenState();
}

class _ScheduleCalendarScreenState extends State<ScheduleCalendarScreen> {
  final _addBtnKey = GlobalKey();
  final _eventsController = DefaultEventsController();
  final _calendarController = CalendarController();
  String _selectedView = 'day';

  // Current visible date/range
  DateTime _currentDate = DateTime.now();
  DateTimeRange? _currentRange;

  @override
  void initState() {
    super.initState();
    _initializeEvents();
    _updateCurrentRange();
  }

  void _initializeEvents() {
    final now = DateTime.now().copyWith(hour: 8);

    // Add sample events
    _eventsController.addEvent(CalendarEvent(
      dateTimeRange:
          DateTimeRange(start: now, end: now.add(const Duration(hours: 1))),
      data: {
        'title': 'Morning Meeting',
        'type': 'task',
        'description': 'Daily standup meeting',
      },
    ));

    _eventsController.addEvent(CalendarEvent(
      dateTimeRange: DateTimeRange(
          start: now.add(const Duration(hours: 2)),
          end: now.add(const Duration(hours: 3))),
      data: {
        'title': 'Project Review',
        'type': 'event',
        'description': 'Review project progress',
      },
    ));

    _eventsController.addEvent(CalendarEvent(
      dateTimeRange: DateTimeRange(
          start: now.add(const Duration(hours: 4)),
          end: now.add(const Duration(hours: 5))),
      data: {
        'title': 'Lunch Break',
        'type': 'break',
        'description': 'Lunch break',
      },
    ));
  }

  void _updateCurrentRange() {
    switch (_selectedView) {
      case 'day':
        _currentRange = DateTimeRange(
          start: _currentDate.copyWith(hour: 6),
          end: _currentDate.copyWith(hour: 6).add(const Duration(days: 1)),
        );
        break;
      case 'week':
        // Find Monday of current week
        final monday =
            _currentDate.subtract(Duration(days: _currentDate.weekday - 1));
        _currentRange = DateTimeRange(
          start: monday.copyWith(hour: 6),
          end: monday.add(const Duration(days: 7)).copyWith(hour: 6),
        );
        break;
      case 'month':
        final firstDayOfMonth =
            DateTime(_currentDate.year, _currentDate.month, 1);
        final lastDayOfMonth =
            DateTime(_currentDate.year, _currentDate.month + 1, 0);
        _currentRange = DateTimeRange(
          start: firstDayOfMonth,
          end: lastDayOfMonth.add(const Duration(days: 1)),
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final localization = AppLocalizations.of(context)!;

    // Create proper view configuration based on selected view
    final viewConfiguration = switch (_selectedView) {
      'week' => MultiDayViewConfiguration.week(
          firstDayOfWeek: DateTime.monday,
          timeOfDayRange: TimeOfDayRange(
              start: const TimeOfDay(hour: 6, minute: 0),
              end: const TimeOfDay(hour: 22, minute: 0)),
        ),
      'month' => MonthViewConfiguration.singleMonth(
          firstDayOfWeek: DateTime.monday,
        ),
      'day' => MultiDayViewConfiguration.singleDay(
          firstDayOfWeek: DateTime.monday,
          timeOfDayRange: TimeOfDayRange(
              start: const TimeOfDay(hour: 6, minute: 0),
              end: const TimeOfDay(hour: 22, minute: 0)),
        ),
      _ => MultiDayViewConfiguration.singleDay(
          firstDayOfWeek: DateTime.monday,
          timeOfDayRange: TimeOfDayRange(
              start: const TimeOfDay(hour: 6, minute: 0),
              end: const TimeOfDay(hour: 22, minute: 0)),
        ),
    };

    return Scaffold(
      appBar: MyAppbar(
        title: localization.schedule,
        actions: [
          ActionIconButton(
            icon: SolarIconsOutline.calendar,
            iconSize: 24,
            onPressed: _handleCalendarPress,
          ),
          const SizedBox(width: 8),
          ActionIconButton(
            key: _addBtnKey,
            icon: Iconsax.add_copy,
            iconSize: 24,
            iconColor: theme.colors.primary,
            onPressed: _handleAddPress,
          ),
        ],
      ),
      body: Column(
        children: [
          // View selector
          Container(
            margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: theme.colors.backgroundContainer,
              border: Border.all(color: theme.colors.strokeColor),
            ),
            padding: const EdgeInsets.all(8),
            child: TabSelector(
              items: [
                ChipItem(label: localization.day, tag: 'day'),
                ChipItem(label: localization.week, tag: 'week'),
                ChipItem(label: localization.month, tag: 'month'),
              ],
              selectedItems: [_selectedView],
              onItemTap: (item) {
                setState(() {
                  _selectedView = item;
                  _updateCurrentRange();
                });
              },
            ),
          ),

          // Calendar header with navigation
          _buildCalendarHeader(theme, localization),

          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: theme.colors.backgroundContainer,
                border: Border.all(color: theme.colors.strokeColor),
              ),
              child: Column(
                children: [
                  // Debug info
                  Container(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      'Calendar View: $_selectedView | Date: ${DateFormat('MMM dd, yyyy').format(_currentDate)}',
                      style: theme.textStyles.body3.copyWith(
                        color: theme.colors.secondaryText,
                      ),
                    ),
                  ),
                  Expanded(
                    child: _eventsController.events.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 64,
                                  color: theme.colors.secondaryText,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No events scheduled',
                                  style: theme.textStyles.body.copyWith(
                                    color: theme.colors.secondaryText,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Tap the + button to add an event',
                                  style: theme.textStyles.body3.copyWith(
                                    color: theme.colors.secondaryText,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : CalendarView(
                            key: ValueKey(_selectedView),
                            eventsController: _eventsController,
                            calendarController: _calendarController,
                            viewConfiguration: viewConfiguration,
                            callbacks: CalendarCallbacks(
                              onEventTapped: _handleEventTap,
                              onEventCreate: (event) => event.copyWith(
                                data: {
                                  'title': 'New Event',
                                  'type': 'event',
                                  'description': 'New event created',
                                },
                                interaction:
                                    EventInteraction(allowEndResize: true),
                              ),
                              onEventCreated: (event) {
                                _eventsController.addEvent(event);
                                _handleEventTap(event, null);
                              },
                              onTapped: _handleEmptySpaceTap,
                            ),
                            body: CalendarBody(
                              scheduleTileComponents: ScheduleTileComponents(
                                tileBuilder: (event, date) =>
                                    _buildEventTile(event, date),
                              ),
                            ),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarHeader(AppTheme theme, AppLocalizations localization) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Row(
        children: [
          // Navigation buttons
          IconButton(
            onPressed: _navigatePrevious,
            icon: Icon(Icons.chevron_left, color: theme.colors.primary),
          ),

          Expanded(
            child: GestureDetector(
              onTap: _showDatePicker,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: theme.colors.strokeColor),
                ),
                child: Text(
                  _getCurrentDateText(),
                  style: theme.textStyles.body.copyWith(
                    color: theme.colors.primaryText,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),

          IconButton(
            onPressed: _navigateNext,
            icon: Icon(Icons.chevron_right, color: theme.colors.primary),
          ),

          // Today button
          TextButton(
            onPressed: _goToToday,
            child: Text(
              localization.today,
              style: theme.textStyles.body.copyWith(
                color: theme.colors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventTile(CalendarEvent event, DateTimeRange<DateTime> date) {
    final theme = AppTheme.of(context);
    final eventData = event.data as Map<String, dynamic>?;
    final title = eventData?['title'] ?? 'Event';
    final type = eventData?['type'] ?? 'event';
    final priority = eventData?['priority'];
    final isAllDay = eventData?['isAllDay'] as bool? ?? false;

    // Enhanced color scheme based on type
    Color backgroundColor;
    Color borderColor;
    switch (type) {
      case 'task':
        backgroundColor = theme.colors.primary;
        borderColor = theme.colors.primary;
        break;
      case 'shift':
        backgroundColor = theme.colors.info;
        borderColor = theme.colors.info;
        break;
      case 'event':
        backgroundColor = theme.colors.success;
        borderColor = theme.colors.success;
        break;
      case 'holiday':
        backgroundColor = theme.colors.warning;
        borderColor = theme.colors.warning;
        break;
      case 'break':
        backgroundColor = theme.colors.secondary;
        borderColor = theme.colors.secondary;
        break;
      default:
        backgroundColor = theme.colors.secondary;
        borderColor = theme.colors.secondary;
    }

    // Adjust opacity based on priority for tasks
    double opacity = 0.8;
    if (type == 'task' && priority != null) {
      switch (priority.toString().toLowerCase()) {
        case 'high':
          opacity = 0.95;
          break;
        case 'medium':
          opacity = 0.8;
          break;
        case 'low':
          opacity = 0.65;
          break;
      }
    }

    // Format time display
    String timeText = '';
    if (!isAllDay) {
      final startTime = TimeOfDay.fromDateTime(event.dateTimeRange.start);
      final endTime = TimeOfDay.fromDateTime(event.dateTimeRange.end);
      timeText = '${startTime.format(context)} - ${endTime.format(context)}';
    }

    return Container(
      margin: const EdgeInsets.all(1),
      decoration: BoxDecoration(
        color: backgroundColor.withOpacity(opacity),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: borderColor,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: borderColor.withOpacity(0.2),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title with priority indicator
            Row(
              children: [
                if (type == 'task' && priority != null) ...[
                  Container(
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: priority.toString().toLowerCase() == 'high'
                          ? theme.colors.error
                          : priority.toString().toLowerCase() == 'medium'
                              ? theme.colors.warning
                              : theme.colors.success,
                    ),
                  ),
                  const SizedBox(width: 4),
                ],
                Expanded(
                  child: Text(
                    title,
                    style: theme.textStyles.body3.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 11,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            // Time display for non-all-day events
            if (!isAllDay && timeText.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                timeText,
                style: theme.textStyles.body3.copyWith(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 9,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            // Description if available and space permits
            if (eventData?['description'] != null &&
                eventData!['description'].toString().isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                eventData['description'],
                style: theme.textStyles.body3.copyWith(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 9,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getCurrentDateText() {
    switch (_selectedView) {
      case 'day':
        return DateFormat('EEEE, MMMM d, y').format(_currentDate);
      case 'week':
        final monday =
            _currentDate.subtract(Duration(days: _currentDate.weekday - 1));
        final sunday = monday.add(const Duration(days: 6));
        return '${DateFormat('MMM d').format(monday)} - ${DateFormat('MMM d, y').format(sunday)}';
      case 'month':
        return DateFormat('MMMM y').format(_currentDate);
      default:
        return DateFormat('EEEE, MMMM d, y').format(_currentDate);
    }
  }

  void _navigatePrevious() {
    setState(() {
      switch (_selectedView) {
        case 'day':
          _currentDate = _currentDate.subtract(const Duration(days: 1));
          break;
        case 'week':
          _currentDate = _currentDate.subtract(const Duration(days: 7));
          break;
        case 'month':
          _currentDate = DateTime(_currentDate.year, _currentDate.month - 1, 1);
          break;
      }
      _updateCurrentRange();
    });
  }

  void _navigateNext() {
    setState(() {
      switch (_selectedView) {
        case 'day':
          _currentDate = _currentDate.add(const Duration(days: 1));
          break;
        case 'week':
          _currentDate = _currentDate.add(const Duration(days: 7));
          break;
        case 'month':
          _currentDate = DateTime(_currentDate.year, _currentDate.month + 1, 1);
          break;
      }
      _updateCurrentRange();
    });
  }

  void _goToToday() {
    setState(() {
      _currentDate = DateTime.now();
      _updateCurrentRange();
    });
  }

  void _showDatePicker() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        _currentDate = picked;
        _updateCurrentRange();
      });
    }
  }

  void _handleEventTap(CalendarEvent event, RenderBox? renderBox) {
    final eventData = event.data as Map<String, dynamic>?;
    final localization = AppLocalizations.of(context)!;
    final theme = AppTheme.of(context);

    showLocalContextMenu(
      context: context,
      items: [
        {
          'icon': Iconsax.edit,
          'label': localization.edit,
          'iconColor': theme.colors.primary,
          'onPressed': () => _editEvent(event),
        },
        {
          'icon': Iconsax.trash,
          'label': localization.delete,
          'iconColor': theme.colors.error,
          'onPressed': () => _deleteEvent(event),
        },
      ],
      buttonKey: _addBtnKey,
    );
  }

  void _handleEmptySpaceTap(DateTime date) {
    final localization = AppLocalizations.of(context)!;
    final theme = AppTheme.of(context);

    showLocalContextMenu(
      context: context,
      items: [
        {
          'icon': Iconsax.menu_board_copy,
          'label': localization.scheduleATask,
          'iconColor': theme.colors.primary,
          'onPressed': () => _addEventAtTime(date, 'task'),
        },
        {
          'icon': SolarIconsOutline.clockCircle,
          'label': localization.scheduleAShift,
          'iconColor': theme.colors.primary,
          'onPressed': () => _addEventAtTime(date, 'shift'),
        },
        {
          'icon': SolarIconsOutline.cupHot,
          'label': localization.addHoliday,
          'iconColor': theme.colors.primary,
          'onPressed': () => _addEventAtTime(date, 'holiday'),
        },
        {
          'icon': SolarIconsOutline.calendarAdd,
          'label': localization.addEvent,
          'iconColor': theme.colors.primary,
          'onPressed': () => _addEventAtTime(date, 'event'),
        },
      ],
      buttonKey: _addBtnKey,
    );
  }

  void _addEventAtTime(DateTime date, String type) {
    // Create a proper time-based event when clicking on calendar
    final startTime = TimeOfDay.fromDateTime(date);
    final endTime = TimeOfDay(
      hour: (startTime.hour + 1) % 24,
      minute: startTime.minute,
    );

    final startDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      startTime.hour,
      startTime.minute,
    );

    final endDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      endTime.hour,
      endTime.minute,
    );

    final newEvent = CalendarEvent(
      dateTimeRange: DateTimeRange(
        start: startDateTime,
        end: endDateTime,
      ),
      data: {
        'title': 'New ${type.capitalize()}',
        'type': type,
        'description': 'New $type created',
        'isAllDay': false,
      },
    );

    _eventsController.addEvent(newEvent);
    _editEvent(newEvent);
  }

  void _editEvent(CalendarEvent event) {
    // TODO: Implement event editing popup
    print('Edit event: ${event.data}');
  }

  void _deleteEvent(CalendarEvent event) {
    _eventsController.removeEvent(event);
  }

  // Helper method to create DateTime from date and time
  DateTime _createDateTime(DateTime? date, TimeOfDay? time) {
    final selectedDate = date ?? DateTime.now();
    final selectedTime = time ?? TimeOfDay.now();
    return DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      selectedTime.hour,
      selectedTime.minute,
    );
  }

  void _handleAddPress() {
    final theme = AppTheme.of(context);
    final localization = AppLocalizations.of(context)!;

    showLocalContextMenu(
      context: context,
      items: [
        {
          'icon': Iconsax.menu_board_copy,
          'label': localization.scheduleATask,
          'iconColor': theme.colors.primary,
          'onPressed': () {
            showScheduleTaskModal(
              context: context,
              title: localization.scheduleATask,
              onAdd: (taskData) {
                final startDateTime = _createDateTime(
                  taskData['date'] as DateTime?,
                  taskData['startTime'] as TimeOfDay?,
                );
                final endDateTime = _createDateTime(
                  taskData['date'] as DateTime?,
                  taskData['endTime'] as TimeOfDay?,
                );

                final newEvent = CalendarEvent(
                  dateTimeRange: DateTimeRange(
                    start: startDateTime,
                    end: endDateTime,
                  ),
                  data: {
                    'title': taskData['title'] ?? '',
                    'type': 'task',
                    'description': taskData['description'] ?? '',
                    'priority': taskData['priority'],
                    'assignmentType': taskData['assignmentType'],
                    'assign': taskData['assign'],
                  },
                );
                _eventsController.addEvent(newEvent);
                _editEvent(newEvent);
              },
            );
          },
        },
        {
          'icon': SolarIconsOutline.clockCircle,
          'label': localization.scheduleAShift,
          'iconColor': theme.colors.primary,
          'onPressed': () {
            showScheduleTaskModal(
              context: context,
              title: localization.scheduleAShift,
              onAdd: (shiftData) {
                final startDateTime = _createDateTime(
                  shiftData['date'] as DateTime?,
                  shiftData['startTime'] as TimeOfDay?,
                );
                final endDateTime = _createDateTime(
                  shiftData['date'] as DateTime?,
                  shiftData['endTime'] as TimeOfDay?,
                );

                final newEvent = CalendarEvent(
                  dateTimeRange: DateTimeRange(
                    start: startDateTime,
                    end: endDateTime,
                  ),
                  data: {
                    'title': shiftData['title'] ?? '',
                    'type': 'shift',
                    'description': shiftData['description'] ?? '',
                    'priority': shiftData['priority'],
                    'assignmentType': shiftData['assignmentType'],
                    'assign': shiftData['assign'],
                  },
                );
                _eventsController.addEvent(newEvent);
                _editEvent(newEvent);
              },
            );
          },
        },
        {
          'icon': SolarIconsOutline.cupHot,
          'label': localization.addHoliday,
          'iconColor': theme.colors.primary,
          'onPressed': () {
            showAddHolidayModal(
              context: context,
              onAdd: (holidayData) {
                final isAllDay = holidayData['isAllDay'] as bool? ?? true;
                final selectedDate =
                    holidayData['date'] as DateTime? ?? DateTime.now();

                DateTime startDateTime, endDateTime;
                if (isAllDay) {
                  startDateTime = DateTime(
                      selectedDate.year, selectedDate.month, selectedDate.day);
                  endDateTime = startDateTime.add(const Duration(days: 1));
                } else {
                  startDateTime = _createDateTime(
                    selectedDate,
                    holidayData['startTime'] as TimeOfDay?,
                  );
                  endDateTime = _createDateTime(
                    selectedDate,
                    holidayData['endTime'] as TimeOfDay?,
                  );
                }

                final newEvent = CalendarEvent(
                  dateTimeRange: DateTimeRange(
                    start: startDateTime,
                    end: endDateTime,
                  ),
                  data: {
                    'title': holidayData['title'] ?? '',
                    'type': 'holiday',
                    'description': holidayData['description'] ?? '',
                    'holidayType': holidayData['holidayType'],
                    'isAllDay': isAllDay,
                  },
                );
                _eventsController.addEvent(newEvent);
                _editEvent(newEvent);
              },
            );
          },
        },
        {
          'icon': SolarIconsOutline.calendarAdd,
          'label': localization.addEvent,
          'iconColor': theme.colors.primary,
          'onPressed': () {
            showAddEventModal(
              context: context,
              onAdd: (eventData) {
                final startDateTime = _createDateTime(
                  eventData['date'] as DateTime?,
                  eventData['startTime'] as TimeOfDay?,
                );
                final endDateTime = _createDateTime(
                  eventData['date'] as DateTime?,
                  eventData['endTime'] as TimeOfDay?,
                );

                final newEvent = CalendarEvent(
                  dateTimeRange: DateTimeRange(
                    start: startDateTime,
                    end: endDateTime,
                  ),
                  data: {
                    'title': eventData['title'] ?? '',
                    'type': 'event',
                    'description': eventData['description'] ?? '',
                    'assignmentType': eventData['assignmentType'],
                    'assign': eventData['assign'],
                  },
                );
                _eventsController.addEvent(newEvent);
                _editEvent(newEvent);
              },
            );
          },
        },
      ],
      buttonKey: _addBtnKey,
    );
  }

  void _handleCalendarPress() {
    // Show the coming events widget
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useSafeArea: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: AppTheme.of(context).colors.backgroundContainer,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(40)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.of(context).colors.strokeColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Coming events content
            Expanded(
              child: ComingWidget(
                events: _getSampleComingEvents(),
                onEventTap: () {
                  // Handle event tap
                  print('Event tapped');
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<ComingEvent> _getSampleComingEvents() {
    return [
      ComingEvent(
        title: 'Daily Standup',
        dateTime: DateTime.now().add(const Duration(hours: 2)),
      ),
      ComingEvent(
        title: 'Daily Standup',
        dateTime: DateTime.now().add(const Duration(days: 1, hours: 8)),
      ),
      ComingEvent(
        title: 'Daily Standup',
        dateTime: DateTime.now().add(const Duration(days: 13, hours: 8)),
      ),
      ComingEvent(
        title: 'Daily Standup',
        dateTime: DateTime.now().add(const Duration(days: 14, hours: 8)),
      ),
      ComingEvent(
        title: 'Daily Standup',
        dateTime: DateTime.now().add(const Duration(days: 15, hours: 8)),
      ),
      ComingEvent(
        title: 'Daily Standup',
        dateTime: DateTime.now().add(const Duration(days: 16, hours: 8)),
      ),
    ];
  }

  // Public methods for external navigation
  void navigateToDate(DateTime date) {
    setState(() {
      _currentDate = date;
      _updateCurrentRange();
    });
  }

  void navigateToMonth(int year, int month) {
    setState(() {
      _currentDate = DateTime(year, month, 1);
      _selectedView = 'month';
      _updateCurrentRange();
    });
  }

  void navigateToWeek(DateTime weekStart) {
    setState(() {
      _currentDate = weekStart;
      _selectedView = 'week';
      _updateCurrentRange();
    });
  }

  void navigateToDay(DateTime day) {
    setState(() {
      _currentDate = day;
      _selectedView = 'day';
      _updateCurrentRange();
    });
  }

  // Getters for current state
  DateTime get currentDate => _currentDate;
  DateTimeRange? get currentRange => _currentRange;
  String get currentView => _selectedView;
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
