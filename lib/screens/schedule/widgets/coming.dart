import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class ComingWidget extends StatelessWidget {
  final List<ComingEvent> events;
  final VoidCallback? onEventTap;

  const ComingWidget({
    super.key,
    required this.events,
    this.onEventTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
          child: Text(
            'Will Come',
            style: textStyles.headline3.copyWith(
              fontWeight: FontWeight.bold,
              color: colors.primaryText,
            ),
          ),
        ),

        // Events List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: events.length,
            itemBuilder: (context, index) {
              final event = events[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _ComingEventCard(
                  event: event,
                  onTap: onEventTap,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _ComingEventCard extends StatelessWidget {
  final ComingEvent event;
  final VoidCallback? onTap;

  const _ComingEventCard({
    required this.event,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final colors = theme.colors;
    final textStyles = theme.textStyles;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colors.background,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colors.strokeColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Calendar Icon
            Icon(
              SolarIconsOutline.calendarMinimalistic,
              color: colors.secondaryText,
              size: 20,
            ),

            const SizedBox(width: 16),

            // Event Title
            Expanded(
              child: Text(
                event.title,
                style: textStyles.body2.copyWith(
                  fontWeight: FontWeight.w500,
                  color: colors.secondaryText,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Time/Date
            Text(
              _formatEventTime(event.dateTime, context),
              style: textStyles.body2.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatEventTime(DateTime dateTime, BuildContext context) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final eventDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (eventDate.isAtSameMomentAs(today)) {
      return 'Today ${formatTime(dateTime, context)}';
    } else if (eventDate.isAtSameMomentAs(today.add(const Duration(days: 1)))) {
      return 'Tomorrow ${formatTime(dateTime, context)}';
    } else {
      return '${dateTime.day} ${_getMonthName(dateTime.month)} at ${formatTime(dateTime, context)}';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }
}

class ComingEvent {
  final String title;
  final DateTime dateTime;
  final String? description;
  final String? assignee;

  const ComingEvent({
    required this.title,
    required this.dateTime,
    this.description,
    this.assignee,
  });
}

// // Example usage with sample data
// class ComingWidgetExample extends StatelessWidget {
//   const ComingWidgetExample({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final sampleEvents = [
//       ComingEvent(
//         title: 'Daily Standup',
//         dateTime: DateTime.now().add(const Duration(hours: 2)),
//       ),
//       ComingEvent(
//         title: 'Daily Standup',
//         dateTime: DateTime.now().add(const Duration(days: 1, hours: 8)),
//       ),
//       ComingEvent(
//         title: 'Daily Standup',
//         dateTime: DateTime.now().add(const Duration(days: 13, hours: 8)),
//       ),
//       ComingEvent(
//         title: 'Daily Standup',
//         dateTime: DateTime.now().add(const Duration(days: 14, hours: 8)),
//       ),
//       ComingEvent(
//         title: 'Daily Standup',
//         dateTime: DateTime.now().add(const Duration(days: 15, hours: 8)),
//       ),
//       ComingEvent(
//         title: 'Daily Standup',
//         dateTime: DateTime.now().add(const Duration(days: 16, hours: 8)),
//       ),
//     ];

//     return Scaffold(
//       body: ComingWidget(
//         events: sampleEvents,
//         onEventTap: () {
//           // Handle event tap
//           print('Event tapped');
//         },
//       ),
//     );
//   }
// }
