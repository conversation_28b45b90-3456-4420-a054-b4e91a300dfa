import 'dart:async';

import 'package:ako_basma/providers/auth/login_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gif_view/gif_view.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({
    super.key,
    required this.onComplete,
    required this.onFadeStart,
    required this.onContentReady,
  });

  /// called when the anim is completed.
  final VoidCallback onComplete;

  /// called when the anim is done halfway and logo starts fading out
  final VoidCallback onFadeStart;

  /// called when the we should be ready to show the content behind
  final VoidCallback onContentReady;
  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  late GifController _controller;
  late final ProviderSubscription<LoginStates> sub;
  final halfAnimCompleter = Completer<void>();
  bool _increasedFPS = false;

  static const _frameCount = 61;
  static const _halfFrame = 31;
  static const _fadeStartFrame = 40;
  @override
  void initState() {
    super.initState();
    _controller = GifController();

    print('playing anim');

    Future.wait<void>([
      halfAnimCompleter.future,
      waitForLoginStateResolved().then((_) {
        print('initializer completed with $_');
      })
    ]).then((_) => {_resumeAnim()});
  }

  void _resumeAnim() {
    setState(() {
      _increasedFPS = true;
    });
    _controller.play(initialFrame: _halfFrame);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: GifView.asset(
          'assets/images/splash/splash-${Theme.of(context).brightness == Brightness.dark ? 'dark' : 'light'}.gif',
          controller: _controller,
          autoPlay: true,
          onFrame: (frame) {
            if (frame == _halfFrame) {
              _controller.pause();
              halfAnimCompleter.complete();
            }
            if (frame == _fadeStartFrame - 5) {
              widget.onContentReady();
            }
            if (frame == _fadeStartFrame) {
              widget.onFadeStart();
            }
          },
          onFinish: () {
            widget.onComplete();
          },
          height: size.height,
          width: size.width,
          frameRate: 30,
          fit: BoxFit.cover,
          loop: false,
        ),
      ),
    );
  }

  Future<LoginStates> waitForLoginStateResolved() {
    final completer = Completer<LoginStates>();
    sub = ref.listenManual<LoginStates>(
      loginStateProvider,
      (previous, next) {
        if (next != LoginStates.waiting && !completer.isCompleted) {
          completer.complete(next);
          sub.close();
        }
      },
    );
    final current = ref.read(loginStateProvider);
    if (current != LoginStates.waiting && !completer.isCompleted) {
      completer.complete(current);
      sub.close();
    }
    return completer.future;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
