import 'dart:async';

import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/model/profile.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:jumping_dot/jumping_dot.dart';
import 'package:remixicon/remixicon.dart';

import '../../components/button/primary_button.dart';
import '../../components/image/image_widget.dart';
import '../../constants/assets.dart';
import '../../providers/auth/auth.dart';
import '../../providers/auth/login_state.dart';
import '../../util/hive/hive_util.dart';

/// intermediate b/w splash and first screen of the app...
/// shows loading/errors when the profile is fetched on startup.
class VerifyScreen extends ConsumerStatefulWidget {
  const VerifyScreen({super.key});

  @override
  ConsumerState<VerifyScreen> createState() => _VerifyScreenState();
}

class _VerifyScreenState extends ConsumerState<VerifyScreen> {
  // late Timer _timer;
  // bool _timerLoaded = false;
  // bool _isDelayed = false;

  @override
  void initState() {
    super.initState();

    // final waiting = ref.read(loginStateProvider) == LoginStates.waiting;
    // if (waiting) {
    //   _timer = Timer(5.seconds, () {
    //     if (context.mounted) {
    //       setState(() {
    //         _isDelayed = true;
    //       });
    //     }
    //   });
    //   _timerLoaded = true;
    // }
  }

  @override
  Widget build(BuildContext context) {
    final locked = ref.watch(loginStateProvider);
    print(locked);
    final localProfile = HiveUtils.profile();

    return Scaffold(
        body: Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            switch (locked) {
              LoginStates.waiting => SizedBox(),
              // const Center(
              //     child: DotsLoadingIndicator(),
              //   ),
              //  _buildLoading(),

              /// some login restriction.
              LoginStates.notAllowed => _buildRestricted(),
              LoginStates.error => _buildError(),
              LoginStates.expired => _buildExpired(),
              LoginStates.offline => _buildOffline(localProfile),
              LoginStates() => Container()
            },
          ],
        ),
      ),
    ));
  }

  Padding _buildOffline(Profile? localProfile) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Remix.cloud_off_line,
            size: 60,
            color: colors(context).primary.withOpacity(0.5),
          ),
          const SizedBox(height: 8),
          Text(
            'You appear to be offline',
            style: textStyles(context).titleLarge,
            textAlign: TextAlign.center,
          ),
          Text(
            'Connect to a network to access the app',
            textAlign: TextAlign.center,
            style: textStyles(context).titleSmall,
          ),
          const SizedBox(height: 18),
          Align(
            alignment: Alignment.center,
            child: PrimaryButton(
              onTap: () {
                ref.read(loginStateProvider.notifier).refreshLoginState();
              },
              label: 'Retry ',
            ),
          ),
        ],
      ).animate().fadeIn(duration: 500.milliseconds),
    );
  }

  Padding _buildExpired() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Remix.error_warning_line,
            size: 80,
            color: colors(context).primary.withOpacity(0.5),
          ),
          const SizedBox(height: 8),
          Text(
            'Session Expired',
            style: textStyles(context).titleLarge,
            textAlign: TextAlign.center,
          ),
          Text(
            'We require you to sign in again before continuing.',
            textAlign: TextAlign.center,
            style: textStyles(context).titleSmall,
          ),
          const SizedBox(height: 24),
          Align(
            alignment: Alignment.center,
            child: TextButton.icon(
              onPressed: () {
                ref.read(authStateProvider.notifier).logOut();
              },
              label: Text(
                'Login Again',
              ),
              icon: Icon(FontAwesomeIcons.rightFromBracket,
                  color: colors(context).primary),
            ),
          ),
        ],
      ).animate().fadeIn(duration: 500.milliseconds),
    );
  }

  Padding _buildError() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Remix.cloud_off_line,
            size: 60,
            color: colors(context).primary.withOpacity(0.5),
          ),
          const SizedBox(height: 8),
          Text(
            'We couldn\'t log you in at the moment.',
            style: textStyles(context).titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          Align(
            alignment: Alignment.center,
            child: PrimaryButton(
              onTap: () {
                ref.read(loginStateProvider.notifier).refreshLoginState();
              },
              label: 'Retry ',
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Align(
              alignment: Alignment.center,
              child: TextButton.icon(
                onPressed: () {
                  ref.read(authStateProvider.notifier).logOut();
                },
                icon: Icon(
                  FontAwesomeIcons.rightFromBracket,
                  size: 18,
                  color: colors(context).primary,
                ),
                label: Text(
                  'Log out',
                  // style: AppTextStyle.primaryTextButton,
                ),
              ),
            ),
          ),
        ],
      ).animate().fadeIn(duration: 500.milliseconds),
    );
  }

  Padding _buildRestricted() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Image.asset(
          //   Assets.logoImage,
          // ),
          Icon(
            Remix.timer_2_line,
            size: 80,
            color: colors(context).outline.withOpacity(0.5),
          ),
          const SizedBox(height: 8),

          Text(
            'Not Accessible',
            style: textStyles(context).titleLarge,
            textAlign: TextAlign.center,
          ),
          Text(
            'Your account is waiting to be approved. This may take some time.',
            textAlign: TextAlign.center,
            style: textStyles(context).bodyMedium,
          ),
          const SizedBox(height: 24),
          Align(
            alignment: Alignment.center,
            child: TextButton.icon(
              onPressed: () {
                ref.read(authStateProvider.notifier).logOut();
              },
              label: Text(
                'Sign Out',
                // style: AppTextStyle.primaryTextButton.copyWith(
                //   color: AppColors.primary,
                // ),
              ),
              icon: Icon(FontAwesomeIcons.rightFromBracket,
                  color: colors(context).primary),
            ),
          ),
        ],
      ).animate().fadeIn(duration: 500.milliseconds),
    );
  }

  Padding _buildLoading() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ImageContainer(
            url: null,
            placeholderAsset: Assets.logoImage,
            height: 150,
          ),
          const SizedBox(height: 24),
          Text(
            'Just a moment',
            style: textStyles(context).titleSmall,
          ),
          const SizedBox(height: 8),
          JumpingDots(
            verticalOffset: -5,
            radius: 7,
            color: colors(context).primary,
          )
        ],
      )
          .animate()
          .fadeIn(duration: 500.milliseconds)
          .slideY(duration: 500.milliseconds, begin: -0.1, end: 0),
      // )
    );
  }

  @override
  void dispose() {
    // if (_timerLoaded) {
    //   _timer.cancel();
    // }
    super.dispose();
  }
}
