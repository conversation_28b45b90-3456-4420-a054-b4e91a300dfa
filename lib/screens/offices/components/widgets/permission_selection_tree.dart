import 'package:ako_basma/components/form/radio_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:solar_icons/solar_icons.dart';

class PermissionSelectionTree extends StatefulWidget {
  const PermissionSelectionTree({
    super.key,
    required this.initialPermissions,
    required this.onUpdate,
  });

  /// accepts permissions like this
  /// {
  ///   permissions:[
  ///     {
  ///       'id: 'salary_management',
  ///       'label':'Salary Manageemtn',
  ///       'permission':'view',
  ///     },
  ///     {
  ///       'id: 'employee_management',
  ///       'label':'Employee Manageemtn',
  ///       'permission':'none',
  ///     },
  ///     {
  ///       'id: 'leave_management',
  ///       'label':'Leave Manageemtn',
  ///       'permission':'edit',
  ///     },
  ///   ]
  /// }
  final List<Map<String, String>> initialPermissions;
  final void Function(List<Map<String, String>>) onUpdate;

  @override
  State<PermissionSelectionTree> createState() =>
      _PermissionSelectionTreeState();
}

class _PermissionSelectionTreeState extends State<PermissionSelectionTree> {
  List<Map<String, String>> _permissions = [];
  Set<String> _expandedSections = {};

  @override
  void initState() {
    _permissions = List.from(widget.initialPermissions);
    _expandedSections = Set.from(widget.initialPermissions.map((e) => e['id']));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return Column(
      children: [
        _buildMasterSwitch(),
        const SizedBox(height: 5),
        ..._permissions.indexed.map((entry) {
          final i = entry.$1;
          final permission = entry.$2;
          final isLast = i == _permissions.length - 1;
          return Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 5),
            child: _buildPermissionSection(permission),
          );
        }),
      ],
    );
  }

  Widget _buildMasterSwitch() {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final hasFullAccess = _permissions
        .every((permissionObj) => permissionObj['permission'] == 'edit');
    void update([bool? value]) {
      setState(() {
        if (value == true) {
          for (int i = 0; i < _permissions.length; i++) {
            _permissions[i]['permission'] = 'edit';
          }
        } else {
          for (int i = 0; i < _permissions.length; i++) {
            _permissions[i]['permission'] = 'none';
          }
        }
      });
      widget.onUpdate(_permissions);
    }

    return InkWell(
      onTap: () {
        update(!hasFullAccess);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          children: [
            SizedBox.square(
              dimension: 20,
              child: Checkbox(
                value: hasFullAccess,
                onChanged: (value) {
                  update(value);
                },
                activeColor: theme.colors.primary,
                side: BorderSide(
                  color: hasFullAccess
                      ? theme.colors.primary
                      : theme.colors.tertiaryText,
                ),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4)),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            const SizedBox(width: 8),
            Text(strings.fullAccess, style: theme.textStyles.body2),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionSection(Map<String, String> permission) {
    final id = permission['id'];
    void update([bool? value]) {
      setState(() {
        try {
          if (value == true) {
            _expandedSections.add(id ?? "");
          }
          _permissions.firstWhere((obj) => obj['id'] == id)['permission'] =
              value == true ? 'view' : 'none';
        } catch (e) {}
      });
      widget.onUpdate(_permissions);
    }

    final isActive = permission['permission'] != 'none';
    final expanded = _expandedSections.contains(permission['id']);
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return AnimatedSize(
      duration: 300.milliseconds,
      alignment: Alignment.topCenter,
      child: Column(
        children: [
          // header
          InkWell(
            onTap: () => update(!isActive),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        if (expanded) {
                          _expandedSections.remove(permission['id']);
                        } else {
                          _expandedSections.add(permission['id'] ?? "");
                        }
                      });
                    },
                    splashFactory: NoSplash.splashFactory,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(
                        expanded
                            ? SolarIconsBold.altArrowUp
                            : SolarIconsBold.altArrowDown,
                        size: 16,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  SizedBox.square(
                    dimension: 20,
                    child: Checkbox(
                      activeColor: theme.colors.primary,
                      value: isActive,
                      onChanged: (v) => update(v),
                      side: BorderSide(
                        color: isActive
                            ? theme.colors.primary
                            : theme.colors.tertiaryText,
                      ),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4)),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      permission['label'] ?? "",
                      style: theme.textStyles.body2
                          .copyWith(color: theme.colors.primaryText),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (expanded)
            IgnorePointer(
              ignoring: !isActive,
              child: AnimatedOpacity(
                opacity: isActive ? 1 : 0.6,
                duration: 200.milliseconds,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      AppRadioButton.tab(
                        value: 'view',
                        groupValue: permission['permission'] ?? "",
                        onChanged: (v) {
                          setState(() {
                            _permissions.firstWhere(
                                    (obj) => obj['id'] == id)['permission'] =
                                v ?? 'none';
                          });
                        },
                        // localize
                        label: strings.canView,
                      ),
                      const SizedBox(height: 4),
                      AppRadioButton.tab(
                        value: 'edit',
                        groupValue: permission['permission'] ?? "",
                        onChanged: (v) {
                          setState(() {
                            _permissions.firstWhere(
                                    (obj) => obj['id'] == id)['permission'] =
                                v ?? 'none';
                          });
                        },
                        // localize
                        label: strings.canEdit,
                      )
                    ],
                  ),
                ),
              ),
            )
        ],
      ),
    );
  }
}
