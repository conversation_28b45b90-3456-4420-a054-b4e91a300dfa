import 'dart:async';
import 'dart:ui' as ui;

import 'package:ako_basma/constants/map.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/map/marker_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class OfficeMapCard extends StatefulWidget {
  final double latitude;
  final double longitude;

  const OfficeMapCard({
    super.key,
    required this.latitude,
    required this.longitude,
  });

  @override
  State<OfficeMapCard> createState() => _OfficeMapCardState();
}

class _OfficeMapCardState extends State<OfficeMapCard> {
  final Completer<GoogleMapController> _controller = Completer();
  final Set<Marker> _markers = {};
  bool _markerCreated = false;
  bool _isMapDark = false;

  late LatLng _officeLatLng;
  late CameraPosition _officeLocation;

  @override
  void initState() {
    super.initState();
    _officeLatLng = LatLng(widget.latitude, widget.longitude);
    _officeLocation = CameraPosition(
      target: _officeLatLng,
      zoom: 14.0,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_markerCreated) {
      _createCustomMarker(context);
      _markerCreated = true;
    }
    _setMapStyle();
  }

  void _setMapStyle() async {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    if (_isMapDark == isDark) return;

    final controller = await _controller.future;
    final style = isDark ? mapNightStyleString : null;
    controller.setMapStyle(style);
    setState(() {
      _isMapDark = isDark;
    });
  }

  Future<void> _createCustomMarker(BuildContext context) async {
    final BitmapDescriptor customMarker = await _getCustomMarker(context);
    if (mounted) {
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('office_location'),
            position: _officeLatLng,
            icon: customMarker,
            anchor: const Offset(0.5, 1),
          ),
        );
      });
    }
  }

  Future<BitmapDescriptor> _getCustomMarker(BuildContext context) async {
    final theme = AppTheme.of(context);
    final devicePixelRatio = MediaQuery.devicePixelRatioOf(context);

    const double markerWidth = 42.0;
    const double markerHeight = 54.0;
    const double iconSize = 24.0;

    final widget = SizedBox(
      width: markerWidth,
      height: markerHeight,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SvgPicture.asset(
            'assets/icons/dot_map_pin.svg',
            width: markerWidth,
            height: markerHeight,
            colorFilter: ColorFilter.mode(
              theme.colors.primary,
              BlendMode.srcIn,
            ),
          ),
          Positioned(
            top: 10,
            child: Icon(
              Iconsax.buildings_2_copy,
              size: iconSize,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );

    return widget.toBitmapDescriptor(
      logicalSize: const Size(markerWidth, markerHeight),
      imageSize:
          Size(markerWidth * devicePixelRatio, markerHeight * devicePixelRatio),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return SizedBox(
      height: 250,
      child: Stack(
        children: [
          GoogleMap(
            mapType: MapType.normal,
            initialCameraPosition: _officeLocation,
            onMapCreated: (GoogleMapController controller) {
              if (!_controller.isCompleted) {
                _controller.complete(controller);
                _setMapStyle();
              }
            },
            markers: _markers,
            zoomControlsEnabled: false,
            scrollGesturesEnabled: false,
            zoomGesturesEnabled: false,
            tiltGesturesEnabled: false,
            rotateGesturesEnabled: false,
            myLocationButtonEnabled: false,
            myLocationEnabled: true,
          ),
          Positioned(
            right: 16,
            bottom: 16,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(5),
                  margin: const EdgeInsets.only(bottom: 10),
                  decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(5)),
                  child: InkWell(
                    onTap: () async {
                      final GoogleMapController controller =
                          await _controller.future;
                      controller.animateCamera(CameraUpdate.zoomIn());
                    },
                    child: SvgPicture.asset(
                      'assets/icons/expand-outline.svg',
                      height: 24,
                      width: 24,
                      color: theme.colors.secondaryText,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(5)),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      InkWell(
                        onTap: () async {
                          final GoogleMapController controller =
                              await _controller.future;
                          controller.animateCamera(CameraUpdate.zoomIn());
                        },
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(5, 5, 5, 7.5),
                          child: Icon(
                            Icons.add_rounded,
                            size: 24,
                            color: theme.colors.secondaryText,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          final GoogleMapController controller =
                              await _controller.future;
                          controller.animateCamera(CameraUpdate.zoomOut());
                        },
                        child: Padding(
                          padding: const EdgeInsets.fromLTRB(5, 7.5, 5, 5),
                          child: Icon(
                            Icons.remove_rounded,
                            size: 24,
                            color: theme.colors.secondaryText,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildMapControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      elevation: 4,
      child: InkWell(
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Icon(icon, size: 20, color: Colors.black54),
        ),
      ),
    );
  }
}
