import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:intl/intl.dart';
import 'package:solar_icons/solar_icons.dart';

class OfficeInfoCard extends StatelessWidget {
  const OfficeInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: theme.colors.backgroundContainer,
          border: Border.all(
            color: theme.colors.strokeColor,
          ),
          borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          InkWell(
            onTap: () {},
            splashFactory: NoSplash.splashFactory,
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Icon(
                        SolarIconsOutline.usersGroupTwoRounded,
                        size: 20,
                        color: theme.colors.secondaryText,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        NumberFormat.compact().format(20), // Example: 20K
                        style: theme.textStyles.textButton.copyWith(
                          color: theme.colors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Text(
                              'Office A',
                              style: theme.textStyles.headline3,
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                                color: theme.colors.primaryVariant,
                                borderRadius: BorderRadius.circular(8)),
                            padding: const EdgeInsets.symmetric(
                                vertical: 3.5, horizontal: 12),
                            child: Text(
                              strings.branch,
                              style: theme.textStyles.body3
                                  .copyWith(color: theme.colors.primary),
                              textScaler: TextScaler.noScaling,
                            ),
                          )
                        ],
                      ),
                      Text(
                        '10 Blackstone Street, London, UK',
                        style: theme.textStyles.buttonSmall.copyWith(
                          color: theme.colors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          const SizedBox(height: 8),
          _buildInfoRow(context, 'email', '<EMAIL>'),
          const SizedBox(height: 4),
          _buildInfoRow(context, 'phone', '+44 ************'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String type, String? value) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Row(
        children: [
          Row(
            children: [
              Icon(
                type == 'email' ? Iconsax.sms_copy : SolarIconsOutline.phone,
                size: 16,
                color: theme.colors.secondaryText,
              ),
              const SizedBox(width: 4),
              Text(
                type == 'email' ? strings.email : strings.phone,
                style: theme.textStyles.body3.copyWith(
                  color: theme.colors.secondaryText,
                ),
              ),
              const SizedBox(width: 1),
              Text(
                ':',
                style: theme.textStyles.body3.copyWith(
                  color: theme.colors.secondaryText,
                ),
              ),
            ],
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              value != null ? embedLtr(value) : strings.unavailable,
              style: theme.textStyles.buttonSmall.copyWith(
                color: value == null || type == 'phone'
                    ? theme.colors.secondaryText
                    : theme.colors.primaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
