import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/l10n/util/app_localizationx.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:intl/intl.dart' as intl;
import 'package:solar_icons/solar_icons.dart';

class DepartmentInfoCard extends StatelessWidget {
  const DepartmentInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
          color: theme.colors.backgroundContainer,
          border: Border.all(
            color: theme.colors.strokeColor,
          ),
          borderRadius: BorderRadius.circular(8)),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'Design Department',
                        style: theme.textStyles.headline4,
                      ),
                      Text(
                        strings.groupMembersCount(20),
                        style: theme.textStyles.body2
                            .copyWith(color: theme.colors.tertiaryText),
                      ),
                    ],
                  ),
                ),
                Text(
                  strings.viewAll,
                  style: theme.textStyles.body2
                      .copyWith(color: theme.colors.primary),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Divider(
              height: 0,
              color: theme.colors.strokeColor,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              children: [
                _buildEmployeeTile(context,
                    imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
                    name: 'John Smith',
                    status: 'active',
                    onTap: () {}),
                _buildEmployeeTile(context,
                    imageUrl:
                        'https://randomuser.me/api/portraits/women/44.jpg',
                    name: 'Sarah Johnson',
                    status: 'active',
                    onTap: () {}),
                _buildEmployeeTile(context,
                    imageUrl: 'https://randomuser.me/api/portraits/men/67.jpg',
                    name: 'Mike Davis',
                    status: 'active',
                    onTap: () {}),
                _buildEmployeeTile(context,
                    imageUrl:
                        'https://randomuser.me/api/portraits/women/23.jpg',
                    name: 'Emily Wilson',
                    status: 'active',
                    onTap: () {}),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeTile(
    BuildContext context, {
    String? imageUrl,
    String? name,
    String? status,
    VoidCallback? onTap,
  }) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          children: [
            Container(
              height: 48,
              width: 48,
              margin: const EdgeInsets.all(1.5),
              clipBehavior: Clip.hardEdge,
              decoration: ShapeDecoration(
                shape: CircleBorder(),
              ),
              child: ImageContainer(
                url: imageUrl,
                height: 48,
                width: 48,
                fit: BoxFit.cover,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      name ?? strings.notAvailable,
                      style: theme.textStyles.headline4.copyWith(fontSize: 14),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      (status != null
                              ? strings.translatedLabel(status)
                              : null) ??
                          strings.notAvailable,
                      style: theme.textStyles.body3
                          .copyWith(color: theme.colors.tertiaryText),
                    ),
                  ],
                ),
              ),
            ),
            Icon(
              Directionality.of(context) == TextDirection.ltr
                  ? HugeIcons.strokeRoundedArrowRight01
                  : HugeIcons.strokeRoundedArrowLeft01,
              size: 24,
              color: theme.colors.primary,
            ),
          ],
        ),
      ),
    );
  }
}
