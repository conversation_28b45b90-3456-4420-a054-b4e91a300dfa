import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:intl/intl.dart';
import 'package:solar_icons/solar_icons.dart';

class ArticleListCard extends StatelessWidget {
  const ArticleListCard(
      {super.key,
      required this.title,
      required this.image,
      required this.date,
      required this.visiblity});
  final String title;
  final String image;
  final DateTime date;
  final String visiblity;
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
          color: theme.colors.backgroundContainer,
          border: Border.all(
            color: theme.colors.strokeColor,
          ),
          borderRadius: BorderRadius.circular(8)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Wrap(
            spacing: 3,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: visiblity == 'managers'
                      ? theme.colors.primaryVariant
                      : theme.colors.infoContainer,
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                // localize
                child: Text(
                  visiblity == 'managers'
                      ? strings.managersOnly
                      : strings.allEmployees,
                  style: theme.textStyles.body3.copyWith(
                    fontSize: 10,
                    color: visiblity == 'managers'
                        ? theme.colors.primary
                        : theme.colors.info,
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: theme.colors.strokeColor),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                // localize
                child: Text(
                  strings.officeOfficeName('Baghdad HQ'),
                  style: theme.textStyles.body3.copyWith(
                    fontSize: 10,
                    color: theme.colors.secondaryText,
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              // localize
              strings.publishedByNAME(strings.hrManager), // Example: 20K
              style: theme.textStyles.body3
                  .copyWith(color: theme.colors.tertiaryText, fontSize: 10),
            ),
          ),
          Container(
            height: 202,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: theme.colors.primaryVariant,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: ImageContainer(
                url: image,
                height: 202,
                placeholderFit: BoxFit.scaleDown,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              title,
              style: theme.textStyles.headline4.copyWith(fontSize: 14),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  // '${formatDateRelative(date)} ${formatTime(date, context)}',
                  formatDateTimeRelative(date, context),
                  style: theme.textStyles.body3
                      .copyWith(color: theme.colors.tertiaryText, fontSize: 10),
                ),
              ),
              Icon(
                Iconsax.more_copy,
                size: 20,
                color: theme.colors.tertiaryText,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
