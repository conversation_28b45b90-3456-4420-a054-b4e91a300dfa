import 'dart:io';
import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/attachment/cover_attachment_card.dart';
import 'package:ako_basma/components/form/attachment/cover_placeholder_card.dart';
import 'package:ako_basma/components/form/radio_button.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/offices/components/screens/article_details.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:image_picker/image_picker.dart';

class ArticleEditScreen extends StatefulWidget {
  const ArticleEditScreen({
    super.key,
    this.type,
    this.isNew = true,
    this.onSuccess,
  });

  final String? type;
  final bool isNew;
  final void Function(bool)? onSuccess;

  @override
  State<ArticleEditScreen> createState() => _ArticleEditScreenState();
}

class _ArticleEditScreenState extends State<ArticleEditScreen> {
  // localize
  String _selectedVisiblity = 'all';
  final List<File> _attachments = [];
  File? _selectedCoverFile;
  final _coverPlaceholderKey = GlobalKey(debugLabel: 'coverPlaceholderKey');
  final _attachmentButtonKey = GlobalKey(debugLabel: 'coverPlaceholderKey');

  Future<void> _pickAttachment() async {
    final res = await showLocalPickerMenu(
      buttonKey: _attachmentButtonKey,
      context: context,
      allowedTypes: ['any'],
      allowMultiple: true,
      maxSizeInMB: 25,
    );
    print(res.runtimeType);
    setState(() {
      if (res is File) {
        _attachments.add(res);
      }
      if (res is List<File>) {
        _attachments.addAll(res);
      }
    });
  }

  void _removeAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(
          title: widget.type == 'announcement'
              ? (widget.isNew
                  ? strings.addAnnouncement
                  : strings.editAnnouncement)
              : strings.employeeOfTheMonth),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    strings.selectAudience,
                    style: theme.textStyles.headline.copyWith(
                      fontSize: 14,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Wrap(
                      spacing: 16,
                      children: [
                        AppRadioButton.tab(
                          value: 'all',
                          groupValue: _selectedVisiblity,
                          onChanged: (value) {
                            setState(() {
                              _selectedVisiblity = value ?? "all";
                            });
                          },
                          label: strings.allEmployees,
                        ),
                        AppRadioButton.tab(
                          value: 'managers',
                          groupValue: _selectedVisiblity,
                          onChanged: (value) {
                            setState(() {
                              _selectedVisiblity = value ?? "all";
                            });
                          },
                          label: strings.managersOnly,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 0),
                  GestureDetector(
                      key: _coverPlaceholderKey,
                      onTapUp: (d) async {
                        final pos = d.globalPosition;
                        final res = await showLocalPickerMenu(
                          buttonKey: null,
                          pointOffset: pos,
                          context: context,
                          allowedTypes: ['image', 'video'],
                          allowMultiple: false,
                          maxSizeInMB: 25,
                        );
                        if (res is File) {
                          setState(() {
                            _selectedCoverFile = res;
                          });
                        }
                      },
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          return CoverAttachmentCard(
                            filePath: _selectedCoverFile?.path,
                            height: 148,
                            fit: BoxFit.cover,
                            width: constraints.maxWidth,
                          );
                        },
                      )),
                  const SizedBox(height: 16),
                  SimpleTextField(
                    decoration: InputDecoration(
                      hintText: strings.title,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (widget.type == 'eotm') ...[
                    CustomDropdown(
                      items: ['Employee A', 'Employee B', 'Employee C'],
                      onChanged: (v) {},
                      hintText: strings.assign,
                    ),
                    const SizedBox(height: 16),
                  ],
                  SimpleTextField(
                    decoration: InputDecoration(
                      hintText: strings.description,
                    ),
                    minLines: 6,
                    maxLines: 6,
                  ),
                  const SizedBox(height: 16),
                  // Attachments section
                  Column(
                    children: [
                      // const SizedBox(height: 10),
                      if (_attachments.isNotEmpty)
                        ..._attachments.asMap().entries.map((entry) => Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: SizedBox(
                                height: 180,
                                child: AttachmentPlaceholderCard(
                                  // preset: 'other',
                                  filePath: entry.value.path,
                                  onDelete: () => _removeAttachment(entry.key),
                                ),
                              ),
                            )),
                      if (_attachments.isNotEmpty) const SizedBox(height: 4),
                      AttachmentPlaceholderCard(
                        key: _attachmentButtonKey,
                        preset: 'upload',
                        onTap: _pickAttachment,
                        uploadSubtitle: strings.maxFileImageVideoSize(25),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          PrimaryButton.async(
            label: widget.isNew ? strings.add : strings.save,
            onPressed: () async {
              // TODO: Implement actual department creation logic
              await Future.delayed(1.seconds);
              if (mounted) {
                final res = await Navigator.push(context,
                    MaterialPageRoute(builder: (ctx) {
                  return ArticleDetailsScreen(
                    type: widget.type,
                    preview: true,
                    onSuccess: (value) {},
                  );
                }));
                // changes accepted in preview...
                if (res == true) {
                  widget.onSuccess?.call(true);
                }
                // draft..
                else if (res == 'draft') {
                }
                //back to editing. or errors..
                else {}
              }
            },
            wrapBottomPadding: true,
          ),
        ],
      ),
    );
  }
}
