import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_detail_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_info_header.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:solar_icons/solar_icons.dart';

class DepartmentDetailsScreen extends StatefulWidget {
  const DepartmentDetailsScreen({
    super.key,
  });
  @override
  State<DepartmentDetailsScreen> createState() =>
      _DepartmentDetailsScreenState();
}

class _DepartmentDetailsScreenState extends State<DepartmentDetailsScreen> {
  final _contextMenuButtonKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
        appBar: MyAppbar(
          title: strings.officeDetails,
          actions: [
            ActionIconButton(
              key: _contextMenuButtonKey,
              icon: SolarIconsOutline.menuDots,
              onPressed: () async {},
            )
          ],
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.fromLTRB(0, 12, 0, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: theme.colors.backgroundContainer,
                            borderRadius: BorderRadius.circular(8)),
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Icon(
                              SolarIconsOutline.usersGroupTwoRounded,
                              size: 20,
                              color: theme.colors.secondaryText,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              NumberFormat.compact().format(20), // Example: 20K
                              style: theme.textStyles.textButton.copyWith(
                                color: theme.colors.secondaryText,
                              ),
                            ),
                          ],
                        ),
                      ),
                      VerticalDivider(
                        color: theme.colors.strokeColor,
                        width: 32,
                        thickness: 1,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Office A',
                              style: theme.textStyles.headline3,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '10 Blackstone Street, London, UK',
                              style: theme.textStyles.body3.copyWith(
                                color: theme.colors.secondaryText,
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: PrimaryButton(
                  label: strings.addEmployee,
                  onTap: () {},
                  isCompact: true,
                  prefixIcon: Icons.add,
                ),
              ),
              EmployeeInfoHeader(
                data: EmployeeData(
                  id: 1,
                  imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
                  name: 'John Doe',
                  contract: 'Full Time',
                  jobTitle: 'Software Engineer',
                  department: 'Department Manager',
                  email: '<EMAIL>',
                  phone: '+44 ************',
                ),
                items: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 8),
                      Text(
                        strings.documents,
                        style: theme.textStyles.headline4,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 151,
                    child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          return SizedBox(
                            width: 122,
                            child: AttachmentPlaceholderCard(
                              url:
                                  'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
                              // previewHeight: 114,
                              // previewWidth: 100,
                            ),
                          );
                        },
                        separatorBuilder: (__, _) => const SizedBox(width: 8),
                        itemCount: 2),
                  ),
                ],
                showContract: false,
                showEmpId: false,
                showJoined: false,
              ),
              const SizedBox(height: 16),
              SectionHeading(
                title: strings.officeEmployees,
                titleColor: theme.colors.secondaryText,
              ),
              const SizedBox(height: 8),
              ...List.generate(
                dummyEmployees.length,
                (index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      EmployeeDetailCard(
                        data: dummyEmployees[index],
                      ),
                      if (index < dummyEmployees.length - 1)
                        const SizedBox(height: 8),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ));
  }


}
