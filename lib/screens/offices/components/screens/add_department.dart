import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/offices/components/widgets/permission_selection_tree.dart';

import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AddDepartmentScreen extends StatefulWidget {
  final void Function(bool)? onSuccess;

  const AddDepartmentScreen({super.key, this.onSuccess});

  @override
  State<AddDepartmentScreen> createState() => _AddDepartmentScreenState();
}

class _AddDepartmentScreenState extends State<AddDepartmentScreen> {
  final _departmentNameController = TextEditingController();
  final _numberOfEmployeesController = TextEditingController();
  final _emailAddressController = TextEditingController();
  final _phoneNumberController = TextEditingController();

  @override
  void dispose() {
    _departmentNameController.dispose();
    _numberOfEmployeesController.dispose();
    _emailAddressController.dispose();
    _phoneNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar:  MyAppbar(
        title: strings.addDepartment,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 16),
              children: [
                SectionHeading(
                  title: strings.departmentInformation,
                  titleStyle: theme.textStyles.headline4,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _departmentNameController,
                        decoration:
                             InputDecoration(labelText: strings.departmentName),
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _numberOfEmployeesController,
                        decoration:  InputDecoration(
                            labelText: strings.numberOfEmployees),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _emailAddressController,
                        decoration:
                             InputDecoration(labelText: strings.emailAddress),
                        keyboardType: TextInputType.emailAddress,
                        forceLtr: true,
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _phoneNumberController,
                        decoration:
                             InputDecoration(labelText: strings.phoneNumber),
                        keyboardType: TextInputType.phone,
                        forceLtr: true,

                      ),
                      const SizedBox(height: 12),
                      CustomDropdown(
                        hintText: strings.employees,
                        items: [
                          ChipItem(label: 'John Smith', tag: 'emp1'),
                          ChipItem(label: 'Emily Johnson', tag: 'emp2'),
                          ChipItem(label: 'Michael Brown', tag: 'emp3'),
                        ],
                        onChanged: (value) {},
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.managerInformation,
                  titleStyle: theme.textStyles.headline4,
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: CustomDropdown(
                    hintText: strings.manager,
                    items: [
                      ChipItem(label: 'John Smith', tag: 'manager1'),
                      ChipItem(label: 'Emily Johnson', tag: 'manager2'),
                      ChipItem(label: 'Michael Brown', tag: 'manager3'),
                    ],
                    onChanged: (value) {},
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.permissionsDepartment,
                  titleStyle: theme.textStyles.headline4,
                ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(8, 7, 8, 16),
                  child: PermissionSelectionTree(
                    initialPermissions: [
                      {
                        'id': 'salary_management',
                        'label': strings.salaryManagement,
                        'permission': 'view',
                      },
                      {
                        'id': 'employee_management',
                        'label': strings.employeeManagement,
                        'permission': 'view',
                      },
                      {
                        'id': 'leave_management',
                        'label': strings.leaveManagement,
                        'permission': 'edit',
                      },
                    ],
                    onUpdate: (permissions) {},
                  ),
                ),
              ],
            ),
          ),
          PrimaryButton.async(
            onPressed: () async {
              // TODO: Implement actual department creation logic
              await Future.delayed(1.seconds);
              widget.onSuccess?.call(true);
            },
            label: strings.next,
            wrapBottomPadding: true,
          )
        ],
      ),
    );
  }
}
