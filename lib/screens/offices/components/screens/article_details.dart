import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/tab_selector.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class ArticleDetailsScreen extends StatefulWidget {
  const ArticleDetailsScreen({
    super.key,
    this.type,
    this.preview = false,
    this.onSuccess,
  });

  final String? type;
  final bool preview;
  final void Function(bool)? onSuccess;

  @override
  State<ArticleDetailsScreen> createState() => _ArticleDetailsScreenState();
}

class _ArticleDetailsScreenState extends State<ArticleDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(
          title: widget.type == 'eotm'
              ? strings.employeeOfTheMonth
              : strings.announcement),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TabSelector(
              items: [
                ChipItem(label: strings.managersOnly, tag: 'visiblity'),
                ChipItem(
                    label: strings.officeOfficeName('Baghdad HQ'),
                    tag: 'office')
              ],
              selectedItems: ['visiblity'],
              onItemTap: (_) {},
              selectedTextStyle:
                  theme.textStyles.body2.copyWith(color: theme.colors.primary),
              unselectedTextStyle: theme.textStyles.body2
                  .copyWith(color: theme.colors.secondaryText),
              chipPadding:
                  const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: theme.colors.primary),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(SolarIconsOutline.archive,
                            size: 20, color: theme.colors.primary),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Text(
                            strings.archive,
                            style: theme.textStyles.body
                                .copyWith(color: theme.colors.primary),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (widget.preview) {
                        context.pop();
                      } else {
                        context.go(
                          '/home/<USER>/viewArticle/edit?type=${widget.type ?? "announcement"}',
                          extra: {
                            // ...article details,
                            'onSuccess': (bool value) {
                              context.pop(true);
                              showAppSnackbar(context,
                                  title: strings.updatedSuccessfully,
                                  type: 'success');
                            }
                          },
                        );
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.colors.tertiaryText),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            'assets/icons/edit.svg',
                            width: 20,
                            height: 20,
                            color: theme.colors.secondaryText,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              strings.edit,
                              style: theme.textStyles.body
                                  .copyWith(color: theme.colors.secondaryText),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (widget.preview)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: AppOutlinedButton.async(
                  label: strings.publish,
                  onPressed: () async {
                    await Future.delayed(1.seconds);
                    context.pop(true);
                  },
                  tintColor: theme.colors.success,
                ),
              ),
            const SizedBox(height: 16),
            // localize
            Text(
              widget.type == 'eotm'
                  ? strings.employeeOfTheMonth
                  :
                  // title
                  'New Health Insurance Plan Just Launched',
              style: theme.textStyles.headline2,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Text(
                // localize
                strings.publishedByNAME(strings.hrManager), // Example: 20K
                style: theme.textStyles.body2
                    .copyWith(color: theme.colors.tertiaryText),
              ),
            ),
            Text(
              // localize
              formatDateTimeRelative(DateTime.now(), context),
              style: theme.textStyles.body2
                  .copyWith(color: theme.colors.tertiaryText),
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                  color: theme.colors.backgroundContainer,
                  borderRadius: BorderRadius.circular(8)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    // height: 202,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.colors.primaryVariant,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: ImageContainer(
                        url: widget.type == 'eotm'
                            ? 'https://d1csarkz8obe9u.cloudfront.net/posterpreviews/brown-professional-best-employee-of-the-month-design-template-2fcdeec20f4041ce8906323fc126ac25_screen.jpg?ts=1719379853'
                            : 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf',
                        height: 202,
                        placeholderFit: BoxFit.scaleDown,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  if (widget.type == 'eotm')
                    // name (title)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        'Dwight Schrute',
                        style: theme.textStyles.headline2,
                      ),
                    ),
                  Text(
                    '''
We are pleased to introduce our new and improved Health Insurance Package, designed with your wellbeing in mind.
As part of our ongoing commitment to support the health and security of our employees, we have partnered with a new provider to offer a more comprehensive medical plan. This updated package includes:
Enhanced hospital coverage, including specialist care and private rooms
Increased outpatient benefits and faster claim processing
Coverage for mental health and wellness services
Improved support for family members (spouse and children)
A simplified registration and claim procedure via the HR portal''',
                    style: theme.textStyles.body.copyWith(
                      fontSize: 18,
                      color: theme.colors.secondaryText,
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
