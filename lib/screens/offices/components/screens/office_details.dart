import 'dart:async';
import 'dart:ui' as ui;

import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/attachment/attachment_grid_card.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_detail_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_info_header.dart';
import 'package:ako_basma/screens/offices/components/cards/office_map.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class OfficeDetailsScreen extends StatefulWidget {
  const OfficeDetailsScreen({
    super.key,
  });
  @override
  State<OfficeDetailsScreen> createState() => _OfficeDetailsScreenState();
}

class _OfficeDetailsScreenState extends State<OfficeDetailsScreen> {
  final _contextMenuButtonKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
        appBar: MyAppbar(
          title: strings.officeDetails,
          actions: [
            ActionIconButton(
              key: _contextMenuButtonKey,
              icon: SolarIconsOutline.menuDots,
              onPressed: () async {},
            )
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                margin: EdgeInsets.fromLTRB(16, 8, 16, 0),
                decoration: BoxDecoration(
                  color: theme.colors.backgroundContainer,
                  border: Border.all(
                    color: theme.colors.primaryVariant,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      strings.officeContactInformation,
                      style: theme.textStyles.headline4.copyWith(fontSize: 14),
                    ),
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _buildInfoRow(
                              context, 'email', '<EMAIL>'),
                          const SizedBox(height: 4),
                          _buildInfoRow(context, 'phone', '+44 ************'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              EmployeeInfoHeader(
                data: EmployeeData(
                  id: 1,
                  imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
                  name: 'John Doe',
                  contract: 'Full Time',
                  jobTitle: 'Software Engineer',
                  department: 'Branch Manager',
                  email: '<EMAIL>',
                  phone: '+44 ************',
                ),
                items: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 8),
                      Text(
                        strings.documents,
                        style: theme.textStyles.headline4,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 151,
                    child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          return SizedBox(
                            width: 122,
                            child: AttachmentPlaceholderCard(
                              url:
                                  'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
                              // previewHeight: 114,
                              // previewWidth: 100,
                            ),
                          );
                        },
                        separatorBuilder: (__, _) => const SizedBox(width: 8),
                        itemCount: 2),
                  ),

                  // Row(
                  //   children: [
                  //     SizedBox(
                  //       width: 122,
                  //       child: AttachmentGridCard(
                  //         url: 'https://pdfobject.com/pdf/sample.pdf',
                  //         width: 114,
                  //         height: 100,
                  //       ),
                  //     ),
                  //     const SizedBox(width: 24),
                  //     SizedBox(
                  //       width: 122,
                  //       child: AttachmentGridCard(
                  //         url: 'https://pdfobject.com/pdf/sample.pdf',
                  //         width: 114,
                  //         height: 100,
                  //       ),
                  //     ),
                  //   ],
                  // ),
                ],
                showContract: false,
                showEmpId: false,
                showJoined: false,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: AnimatedContainer(
                  duration: 300.milliseconds,
                  foregroundDecoration: BoxDecoration(
                    border: Border.all(
                      color: theme.colors.primaryVariant,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: OfficeMapCard(
                      latitude: 47.6540235,
                      longitude: -122.308572,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: SimpleTextField(
                  initialValue: 'Subway Station, Parking Garage',
                  decoration: InputDecoration(
                      labelText: strings.officeLocationDescription),
                ),
              ),
              SectionHeading(
                title: strings.officeEmployees,
                titleColor: theme.colors.secondaryText,
              ),
              const SizedBox(height: 8),
              ...List.generate(
                dummyEmployees.length,
                (index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      EmployeeDetailCard(
                        data: dummyEmployees[index],
                      ),
                      if (index < dummyEmployees.length - 1)
                        const SizedBox(height: 8),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ));
  }

  Widget _buildInfoRow(BuildContext context, String type, String? value) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Row(
        children: [
          Row(
            children: [
              Icon(
                type == 'email' ? Iconsax.sms_copy : SolarIconsOutline.phone,
                size: 20,
                color: theme.colors.secondaryText,
              ),
              const SizedBox(width: 4),
              Text(
                type == 'email' ? strings.email : strings.phone,
                style: theme.textStyles.body2.copyWith(
                  color: theme.colors.secondaryText,
                ),
              ),
              const SizedBox(width: 1),
              Text(
                ':',
                style: theme.textStyles.body2.copyWith(
                  color: theme.colors.secondaryText,
                ),
              ),
            ],
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              value != null ? embedLtr(value) : strings.unavailable,
              style: theme.textStyles.buttonSmall.copyWith(
                fontSize: 14,
                color: value == null
                    ? theme.colors.secondaryText
                    : theme.colors.primaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
