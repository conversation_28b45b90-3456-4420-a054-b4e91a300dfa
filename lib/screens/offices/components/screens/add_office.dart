import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/animated_dropdown/theme_custom_dropdown.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/offices/components/cards/office_map.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/components/picker/time_picker.dart' as timePicker;

class AddOfficeScreen extends StatefulWidget {
  final void Function(bool)? onSuccess;

  const AddOfficeScreen({
    super.key,
    this.onSuccess,
  });

  @override
  State<AddOfficeScreen> createState() => _AddOfficeScreenState();
}

class _AddOfficeScreenState extends State<AddOfficeScreen> {
  final _officeNameController = TextEditingController();
  final _officeAddressController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _emailAddressController = TextEditingController();
  final _numberOfEmployeesController = TextEditingController();
  final _operatingHoursController = TextEditingController();
  final _locationDescriptionController =
      TextEditingController(text: 'Subway Station, Parking Garage');

  TimeOfDay? _openingTime;
  TimeOfDay? _closingTime;

  @override
  void dispose() {
    _officeNameController.dispose();
    _officeAddressController.dispose();
    _phoneNumberController.dispose();
    _emailAddressController.dispose();
    _numberOfEmployeesController.dispose();
    _operatingHoursController.dispose();
    _locationDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: MyAppbar(
        title: strings.addNewOffice,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 16),
              children: [
                SectionHeading(
                  title: strings.officeInformation,
                  titleStyle: theme.textStyles.headline4,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _officeNameController,
                        decoration:
                            InputDecoration(labelText: strings.officeName),
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _officeAddressController,
                        decoration:
                            InputDecoration(labelText: strings.officeAddress),
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _phoneNumberController,
                        decoration:
                            InputDecoration(labelText: strings.phoneNumber),
                        keyboardType: TextInputType.phone,
                        forceLtr: true,

                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _emailAddressController,
                        decoration:
                            InputDecoration(labelText: strings.emailAddress),
                        keyboardType: TextInputType.emailAddress,
                        forceLtr: true,

                      ),
                      const SizedBox(height: 12),
                      CustomDropdown(
                        hintText: strings.officeType,
                        items: [
                          ChipItem(label: strings.headquarters, tag: 'hq'),
                          ChipItem(label: strings.branch, tag: 'branch'),
                        ],
                        onChanged: (value) {},
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _numberOfEmployeesController,
                        decoration: InputDecoration(
                            labelText: strings.numberOfEmployees),
                        keyboardType: const TextInputType.numberWithOptions(),
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        readOnly: true,
                        onTap: _selectOperatingHours,
                        controller: _operatingHoursController,
                        decoration: InputDecoration(
                          labelText: strings.operatingHours,
                          prefixIcon: const Icon(SolarIconsOutline.clockCircle),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.managerInformation,
                  titleStyle: theme.textStyles.headline4,
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: CustomDropdown(
                    hintText: strings.manager,
                    items: [
                      ChipItem(label: 'John Smith', tag: 'manager1'),
                      ChipItem(label: 'Emily Johnson', tag: 'manager2'),
                      ChipItem(label: 'Michael Brown', tag: 'manager3'),
                    ],
                    onChanged: (value) {},
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.officeLocation,
                  titleStyle: theme.textStyles.headline4,
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      AnimatedContainer(
                        duration: 300.milliseconds,
                        foregroundDecoration: BoxDecoration(
                          border: Border.all(
                            color: theme.colors.primaryVariant,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: OfficeMapCard(
                            latitude: 47.6540235,
                            longitude: -122.308572,
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      SimpleTextField(
                        controller: _locationDescriptionController,
                        decoration: InputDecoration(
                            labelText: strings.officeLocationDescription),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: PrimaryButton.async(
              onPressed: () async {
                // TODO: Implement actual office creation logic
                // For now, simulate success and trigger the callback
                await Future.delayed(1.seconds);
                widget.onSuccess?.call(true);
              },
              label: strings.next,
            ),
          )
        ],
      ),
    );
  }

  Future<void> _selectOperatingHours() async {
    final strings = AppLocalizations.of(context)!;

    final TimeOfDay? opening = await timePicker.showTimePicker(
      context: context,
      initialTime: _openingTime ?? TimeOfDay.now(),
      helpText: strings.selectOpeningTime,
    );
    if (opening == null) return;

    final TimeOfDay? closing = await timePicker.showTimePicker(
      context: context,
      initialTime: _closingTime ?? TimeOfDay.now(),
      helpText: strings.selectClosingTime,
    );
    if (closing == null) return;

    // a > b in TimeOfDay is not directly supported, so convert to minutes
    final openingMinutes = opening.hour * 60 + opening.minute;
    final closingMinutes = closing.hour * 60 + closing.minute;

    if (closingMinutes > openingMinutes) {
      setState(() {
        // Convert TimeOfDay to DateTime for both opening and closing times
        final now = DateTime.now();
        final openingDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          opening.hour,
          opening.minute,
        );
        final closingDateTime = DateTime(
          now.year,
          now.month,
          now.day,
          closing.hour,
          closing.minute,
        );
        _openingTime = opening;
        _closingTime = closing;
        _operatingHoursController.text =
            formatTimeRange(openingDateTime, closingDateTime, context);
        // '${opening.format(context)} - ${closing.format(context)}';
      });
    } else {
      // Show an error or a snackbar
      showAppSnackbar(context,
          title: strings.closingTimeMustBeAfterOpeningTime, type: 'error');
    }
  }
}
