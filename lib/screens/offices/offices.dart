import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/offices/components/cards/article_list_card.dart';
import 'package:ako_basma/screens/salaries/components/card/employee_salary_card.dart';
import 'package:ako_basma/screens/offices/components/cards/office_info_card.dart';
import 'package:ako_basma/screens/offices/components/cards/department_info_card.dart';
import 'package:ako_basma/screens/offices/components/screens/add_office.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class OfficesScreen extends StatefulWidget {
  const OfficesScreen({super.key});

  @override
  State<OfficesScreen> createState() => _OfficesScreenState();
}

class _OfficesScreenState extends State<OfficesScreen> {
  // 'offices','department', 'announcement','eotm',
  String _selectedTab = 'offices';
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
        body: SafeArea(
      bottom: false,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 5),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.of(context).maybePop(),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Directionality.of(context) == TextDirection.ltr
                          ? SolarIconsOutline.altArrowLeft
                          : SolarIconsOutline.altArrowRight,
                      color: theme.colors.primaryText,
                      size: 32,
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Text(
                            strings.allOffices,
                            style: theme.textStyles.headline4,
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            HugeIcons.strokeRoundedArrowDown01,
                            size: 24,
                            color: theme.colors.primary,
                          ),
                        ],
                      ),
                      Text(
                        strings.manageSalaries,
                        style: theme.textStyles.body3
                            .copyWith(color: theme.colors.tertiaryText),
                      ),
                    ],
                  ),
                ),
                ActionIconButton(
                  iconSize: 24,
                  icon: SolarIconsOutline.archive,
                  onPressed: () async {
                    // final DateTime? picked = await showDatePicker(
                    //   context: context,
                    //   initialDate: DateTime.now(),
                    //   firstDate: DateTime(2000),
                    //   lastDate: DateTime(2100),
                    // );
                    // if (picked != null) {
                    //   // You can handle the picked date here, e.g., setState or use it as needed
                    //   // print('Selected date: $picked');
                    // }
                  },
                ),
                const SizedBox(width: 8),
                ActionIconButton(
                  icon: Iconsax.add_copy,
                  iconSize: 24,
                  onPressed: _handleAddPress,
                ),
                const SizedBox(width: 8),
                ActionIconButton(
                  icon: Iconsax.search_normal_copy,
                  iconSize: 24,
                ),
              ],
            ),
          ),
          ChipSelector(
              items: [
                ChipItem(
                    label: strings.manageOffices,
                    tag: 'offices',
                    icon: Iconsax.buildings_2_copy),
                ChipItem(
                    label: strings.manageDepartment,
                    tag: 'department',
                    icon: Iconsax.map_copy),
                ChipItem(
                    label: strings.announcement,
                    tag: 'announcement',
                    icon: Iconsax.user_edit_copy),
                ChipItem(
                    label: strings.employeesOfTheMonth,
                    tag: 'eotm',
                    icon: Iconsax.map_copy),
              ],
              // selectedTextColor: theme.colors.primaryText,
              // unselectedTextStyle: theme.textStyles.body3.copyWith(
              //   color: theme.colors.secondaryText,
              // ),
              padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
              selectedItems: [_selectedTab],
              onItemTap: (tab) {
                setState(() {
                  _selectedTab = tab;
                });
              }),
          // Sample list of employee salary cards
          Expanded(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: _buildContent(),
            ),
          ),
        ],
      ),
    ));
  }

  Widget _buildContent() {
    switch (_selectedTab) {
      case 'offices':
        return _buildOfficesList();
      case 'department':
        return _buildDepartmentsList();
      case 'announcement':
        return _buildAnnouncementList();
      case 'eotm':
        return _buildEomList();
      default:
        return _buildOfficesList();
    }
  }

  Widget _buildOfficesList() {
    return ListView(
      key: const ValueKey('offices'),
      padding: const EdgeInsets.fromLTRB(16, 2, 16, 24),
      children: List.generate(
        5,
        (index) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
          child: GestureDetector(
              onTap: () {
                context.go('/home/<USER>/officeDetails');
              },
              child: const OfficeInfoCard()),
        ),
      ),
    );
  }

  Widget _buildDepartmentsList() {
    return ListView(
      key: const ValueKey('departments'),
      padding: const EdgeInsets.fromLTRB(16, 2, 16, 24),
      children: List.generate(
        5,
        (index) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
          child: GestureDetector(
              onTap: () {
                context.go('/home/<USER>/deptDetails');
              },
              child: const DepartmentInfoCard()),
        ),
      ),
    );
  }

  Widget _buildAnnouncementList() {
    final data = [
      {
        'title': 'New Office Policy Updates',
        'image': 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40',
        'date': DateTime.now(),
        'visibility': 'managers'
      },
      {
        'title': 'Employee Benefits Changes',
        'image': 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf',
        'date': DateTime.now().subtract(const Duration(days: 2)),
        'visibility': 'all'
      },
      {
        'title': 'Office Renovation Schedule',
        'image': 'https://images.unsplash.com/photo-1497366811353-6870744d04b2',
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'visibility': 'managers'
      }
    ];
    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
          child: GestureDetector(
              onTap: () {
                context.go('/home/<USER>/viewArticle?type=announcement');
              },
              child: ArticleListCard(
                title: data[index]['title'] as String,
                image: data[index]['image'] as String,
                date: data[index]['date'] as DateTime,
                visiblity: data[index]['visibility'] as String,
              )),
        );
      },
      key: const ValueKey('announcement'),
      padding: const EdgeInsets.fromLTRB(16, 2, 16, 24),
    );
  }

  Widget _buildEomList() {
    final data = [
      {
        'title': 'Jim Halpert',
        'image':
            'https://d1csarkz8obe9u.cloudfront.net/posterpreviews/brown-professional-best-employee-of-the-month-design-template-2fcdeec20f4041ce8906323fc126ac25_screen.jpg?ts=1719379853',
        'date': DateTime.now(),
        'visibility': 'all'
      },
      {
        'title': 'Dwight Schrute',
        'image':
            'https://d1csarkz8obe9u.cloudfront.net/posterpreviews/brown-professional-best-employee-of-the-month-design-template-2fcdeec20f4041ce8906323fc126ac25_screen.jpg?ts=1719379853',
        'date': DateTime.now().subtract(const Duration(days: 2)),
        'visibility': 'all'
      },
      {
        'title': 'Michael Scott',
        'image':
            'https://d1csarkz8obe9u.cloudfront.net/posterpreviews/brown-professional-best-employee-of-the-month-design-template-2fcdeec20f4041ce8906323fc126ac25_screen.jpg?ts=1719379853',
        'date': DateTime.now().subtract(const Duration(days: 5)),
        'visibility': 'all'
      }
    ];
    return ListView.builder(
      itemCount: data.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
          child: GestureDetector(
              onTap: () {
                context.go('/home/<USER>/viewArticle?type=eotm');
              },
              child: ArticleListCard(
                title: data[index]['title'] as String,
                image: data[index]['image'] as String,
                date: data[index]['date'] as DateTime,
                visiblity: data[index]['visibility'] as String,
              )),
        );
      },
      key: const ValueKey('eotm'),
      padding: const EdgeInsets.fromLTRB(16, 2, 16, 24),
    );
  }

  void _handleAddPress() {
    final strings = AppLocalizations.of(context)!;

    if (_selectedTab == 'offices') {
      context.go('/home/<USER>/addOffice', extra: {
        'onSuccess': (bool success) {
          if (success) {
            context.pop(); // Pop the AddOfficeScreen
            showAppSnackbar(
              context,
              title: strings.addedSuccessfully,
              type: 'success',
            );
          }
        },
      });
    } else if (_selectedTab == 'department') {
      context.go('/home/<USER>/addDepartment', extra: {
        'onSuccess': (bool success) {
          if (success) {
            context.pop();
            showAppSnackbar(
              context,
              title: strings.addedSuccessfully,
              type: 'success',
            );
          }
        },
      });
    } else if (_selectedTab == 'announcement') {
      context.go('/home/<USER>/addArticle?new=true&type=announcement', extra: {
        'onSuccess': (bool success) {
          if (success) {
            context.pop();
            showAppSnackbar(
              context,
              title: strings.addedSuccessfully,
              type: 'success',
            );
          }
        },
      });
    } else if (_selectedTab == 'eotm') {
      context.go('/home/<USER>/addArticle?new=true&type=eotm', extra: {
        'onSuccess': (bool success) {
          if (success) {
            context.pop();
            showAppSnackbar(
              context,
              title: strings.addedSuccessfully,
              type: 'success',
            );
          }
        },
      });
    }
  }
}
