import 'dart:math';

import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/salaries/components/card/employee_salary_card.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class SalariesScreen extends StatefulWidget {
  const SalariesScreen({super.key});

  @override
  State<SalariesScreen> createState() => _SalariesScreenState();
}

class _SalariesScreenState extends State<SalariesScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
        floatingActionButton: Transform.translate(
          offset: Offset(
            Directionality.of(context) == TextDirection.rtl ? 7.5 : -7.5,
            0,
          ),
          child: Transform.rotate(
            angle: -pi / 4,
            child: InkWell(
              splashFactory: NoSplash.splashFactory,
              onTap: () {
                context.push('/home/<USER>');
              },
              borderRadius: BorderRadius.circular(4),
              child: Container(
                  width: 40,
                  height: 40,
                  padding: const EdgeInsets.all(4),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    gradient: DesignColors.primaryGradient,
                    /* Brand-Primary */
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4)),
                  ),
                  child: Transform.rotate(
                    angle: pi / 4,
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        strings.ai,
                        style: theme.textStyles.headline.copyWith(
                          fontSize: 20,
                          color: Colors.white,
                        ),
                        textScaler: TextScaler.noScaling,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ),
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            children: [
                              Text(
                                strings.allOffices,
                                style: theme.textStyles.headline4,
                              ),
                              const SizedBox(width: 4),
                              Icon(
                                HugeIcons.strokeRoundedArrowDown01,
                                size: 24,
                                color: theme.colors.primary,
                              ),
                            ],
                          ),
                          Text(
                            strings.manageSalaries,
                            style: theme.textStyles.body3
                                .copyWith(color: theme.colors.tertiaryText),
                          ),
                        ],
                      ),
                    ),
                    ActionIconButton(
                      icon: Iconsax.filter_search_copy,
                      onPressed: () async {
                        final DateTime? picked = await showDatePickerDialog(
                          context: context,
                          initialDate: DateTime.now(),
                          minDate: DateTime(2000),
                          maxDate: DateTime(2100),
                        );
                        if (picked != null) {
                          // You can handle the picked date here, e.g., setState or use it as needed
                          // print('Selected date: $picked');
                        }
                      },
                    ),
                    const SizedBox(width: 8),
                    ActionIconButton(icon: Iconsax.search_normal_copy),
                  ],
                ),
              ),
              // Sample list of employee salary cards
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.only(bottom: 16),
                  children: [
                    EmployeeSalaryCard(
                      employeeData: dummyEmployees[0],
                      status: 'paid',
                    ),
                    EmployeeSalaryCard(
                      employeeData: dummyEmployees[1],
                      status: 'unpaid',
                    ),
                    EmployeeSalaryCard(
                      employeeData: dummyEmployees[2],
                      status: 'pending',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ));
  }
}
