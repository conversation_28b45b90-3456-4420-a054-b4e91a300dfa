import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/popups/common/icon_info_popup.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

class PayslipConfirmationForm extends StatelessWidget {
  const PayslipConfirmationForm({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
              height: 64,
              width: 64,
              decoration: ShapeDecoration(
                shape: const CircleBorder(),
                color: theme.colors.infoContainer,
              ),
              padding: EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 16),
              child: SvgPicture.asset(
                'assets/icons/banknote.svg',
                height: 40,
                width: 40,
              )),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8)),
            child: Text(
              strings.pending,
              style: theme.textStyles.body.copyWith(
                color: theme.colors.warning,
              ),
            ),
          ),
          // localize
          _buildDetailRow(
            context,
            label: strings.basicSalary,
            value: formatCurrency(1000000, context),
          ),
          _buildDetailRow(context,
              label: strings.reward, value: formatCurrency(100000, context)),
          _buildDetailRow(context,
              label: strings.deductions,
              value: formatCurrency(100000, context)),
          _buildDetailRow(context,
              label: strings.netSalary, value: formatCurrency(100000, context)),

          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: FooterFormButton(
              onCancel: () {
                context.pop();
              },
              onSubmitAsync: () async {
                context.pop(true);
              },
              // localize these labels
              submitLabel: strings.generatePayslip,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context, {
    required String label,
    required String value,
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
          color: theme.colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colors.strokeColor)),
      child: Row(
        children: [
          Text(
            label,
            style: theme.textStyles.body.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              value,
              style: theme.textStyles.body.copyWith(
                color: theme.colors.secondaryText,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
