import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';

class SalaryUpdateForm extends StatefulWidget {
  const SalaryUpdateForm({
    super.key,
    required this.onDismiss,
  });

  final void Function(bool?) onDismiss;

  @override
  State<SalaryUpdateForm> createState() => _SalaryUpdateFormState();
}

class _SalaryUpdateFormState extends State<SalaryUpdateForm> {
  final _newPosController = TextEditingController();
  final _newSalaryController = TextEditingController();
  final _noteController = TextEditingController();

  String _selectedUpdateType = '';

  // initial note?
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              height: 64,
              width: 64,
              decoration: ShapeDecoration(
                shape: const CircleBorder(),
                color: theme.colors.infoContainer,
              ),
              padding: EdgeInsets.all(8),
              margin: const EdgeInsets.only(bottom: 16),
              child: SvgPicture.asset(
                'assets/icons/banknote.svg',
                height: 40,
                width: 40,
              )),
          CustomDropdown(
            onContainer: true,
            hintText: strings.salaryEdit,
            items: [
              ChipItem(label: strings.increase, tag: 'increase'),
              ChipItem(label: strings.decrease, tag: 'decrease'),
            ],
            onChanged: (e) {
              setState(() {
                _selectedUpdateType = e?.tag ?? "";
              });
            },
          ),
          const SizedBox(height: 16),
          SimpleTextField(
            controller: _newPosController,
            onChanged: (value) {
              setState(() {});
            },
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              hintText: _selectedUpdateType == 'increase'
                  ? strings.amountOfIncrease
                  : _selectedUpdateType == 'decrease'
                      ? strings.amountOfDecrease
                      : strings.amountOfIncreaseOrDecrease,
            ),
            onContainer: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown(
            onContainer: true,
            hintText: strings.theReason,
            items: [
              ChipItem(label: 'Performance Bonus', tag: 'performance_bonus'),
              ChipItem(label: 'Cost of Living Adjustment', tag: 'cola'),
              ChipItem(label: 'Role Change', tag: 'role_change'),
              ChipItem(label: 'Promotion', tag: 'promotion'),
              ChipItem(label: 'Demotion', tag: 'demotion'),
              ChipItem(label: 'Market Adjustment', tag: 'market_adjustment'),
              ChipItem(label: 'Annual Increment', tag: 'annual_increment'),
              ChipItem(
                  label: 'Disciplinary Action', tag: 'disciplinary_action'),
            ],
            onChanged: (e) {},
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: FooterFormButton(
              onCancel: () {
                widget.onDismiss(false);
              },
              onSubmitAsync: () async {
                await Future.delayed(1.seconds);
                widget.onDismiss(true);
              },
              // localize these labels
              cancelLabel: strings.cancel,
              submitLabel: strings.confirm,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _noteController.dispose();
    _newPosController.dispose();
    _newSalaryController.dispose();
    super.dispose();
  }
}
