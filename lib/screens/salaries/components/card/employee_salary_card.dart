import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/l10n/util/app_localizationx.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:intl/intl.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeeSalaryCard extends StatelessWidget {
  const EmployeeSalaryCard({
    super.key,
    required this.employeeData,
    this.items = const [],
    this.status = 'pending',
  });

  final EmployeeData employeeData;
  final String status;
  final List<Widget> items;
  static const successTypes = ['paid', 'approved', 'success', 'completed'];
  static const errorTypes = ['rejected', 'failed', 'unpaid'];
  static const warningTypes = ['pending'];
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return InkWell(
      onTap: () {
        context.go('/salaries/details', extra: employeeData);
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        decoration: BoxDecoration(
          color: theme.colors.backgroundContainer,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colors.strokeColor,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header section
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  onTap: () {
                    // context.push('/employees/employee');
                  },
                  splashFactory: NoSplash.splashFactory,
                  child: CircleAvatar(
                    radius: 24,
                    backgroundImage: NetworkImage(employeeData?.imageUrl ?? ""),

                    // Add a placeholder or error widget if image fails to load
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                employeeData?.name ?? "",
                                style: theme.textStyles.headline4.copyWith(
                                  fontSize: 12,
                                  color: theme.colors.secondaryText,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                formatDateDmyText(DateTime.now(), context),
                                style: theme.textStyles.body3.copyWith(
                                  color: theme.colors.primaryText,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (status != null)
                        Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: successTypes.contains(status)
                                  ? theme.colors.successContainer
                                  : errorTypes.contains(status)
                                      ? theme.colors.errorContainer
                                      : theme.colors.warningContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              // TODO: handle these cases... create a status map ig.
                              strings.translatedLabel(status) ??
                                  toBeginningOfSentenceCase(status),
                              style: theme.textStyles.fieldText2.copyWith(
                                fontSize: 12,
                                color: successTypes.contains(status)
                                    ? theme.colors.success
                                    : errorTypes.contains(status)
                                        ? theme.colors.error
                                        : theme.colors.warning,
                              ),
                            )),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // InkWell(
            //   onTap: () async {
            //     await Clipboard.setData(ClipboardData(text: '024290003'));
            //     showAppSnackbar(context,
            //         title: 'Employee ID copied to clipboard', type: 'success');
            //   },
            //   splashFactory: NoSplash.splashFactory,
            //   child: Padding(
            //     padding: const EdgeInsets.fromLTRB(0, 4, 0, 8),
            //     child: Row(children: [
            //       Text(
            //         'EMP:',
            //         style: theme.textStyles.body3.copyWith(
            //           color: theme.colors.tertiaryText,
            //         ),
            //       ),
            //       const SizedBox(width: 4),
            //       Text(
            //         '024290003',
            //         style: theme.textStyles.buttonSmall.copyWith(
            //           color: theme.colors.primary,
            //         ),
            //       ),
            //       const SizedBox(width: 4),
            //       Icon(
            //         Iconsax.copy_copy,
            //         size: 12,
            //       )
            //     ]),
            //   ),
            // ),

            _buildDetailRow(
              context,
              label: strings.basicSalary,
              value: formatCurrency(1100000, context),
            ),
            const SizedBox(height: 4),
            _buildDetailRow(
              context,
              label: strings.allowances,
              value: formatCurrency(1100000, context),
            ),
            const SizedBox(height: 4),
            _buildDetailRow(
              context,
              label: strings.deductions,
              value: formatCurrency(1100000, context),
            ),
            const SizedBox(height: 4),
            _buildDetailRow(
              context,
              label: strings.netSalary,
              value: formatCurrency(1100000, context),
            ),

            ...items,
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context, {
    required String label,
    required String value,
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
          color: theme.colors.background,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colors.primaryVariant)),
      child: Row(
        children: [
          Text(
            label,
            style: theme.textStyles.body3.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            ':',
            style: theme.textStyles.body3.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              value,
              style: theme.textStyles.body3.copyWith(
                color: theme.colors.secondaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
