import 'dart:math';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:solar_icons/solar_icons.dart';

class SalaryModificationForm extends StatefulWidget {
  final EmployeeData? data;

  /// addition / deduction
  final String type;
  final List<(String, double)> initialItems;
  const SalaryModificationForm({
    super.key,
    this.data,
    required this.type,
    this.initialItems = const [],
  });

  @override
  State<SalaryModificationForm> createState() => _SalaryModificationFormState();
}

class _SalaryModificationFormState extends State<SalaryModificationForm> {
  final List<SalaryChangeData> _items = [];

  @override
  void initState() {
    super.initState();
    _initializeItems();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      // padding: EdgeInsets.only(
      //     bottom: max(MediaQuery.viewInsetsOf(context).bottom, 16)),
      child: Column(
        children: [
          SectionHeading(
            title: widget.type == 'addition'
                ? strings.manualAdditions
                : strings.manualDeductions,
            titleStyle: theme.textStyles.headline4,
            padding: const EdgeInsets.all(0),
          ),
          const SizedBox(height: 12),

          // Dynamic items list
          AnimatedSize(
            duration: 200.milliseconds,
            alignment: Alignment.topCenter,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (_items.isEmpty) ...[
                  GestureDetector(
                    onTap: _addItem,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Text(
                            strings.noData,
                            style: theme.textStyles.body.copyWith(
                              color: theme.colors.secondaryText,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 5),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                HugeIcons.strokeRoundedAdd01,
                                size: 16,
                                color: theme.colors.primary,
                              ),
                              const SizedBox(width: 5),
                              Text(
                                strings.add,
                                style: theme.textStyles.textButton,
                              ),
                              const SizedBox(width: 5),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )
                ] else ...[
                  ..._items.asMap().entries.map((entry) =>
                      _buildItemRow(entry.value, entry.key, strings, theme)),
                  AppOutlinedButton(label: strings.add, onTap: _addItem),
                ],
              ],
            ),
          ),

          const SizedBox(height: 16),
          FooterFormButton(
            onCancel: () {
              context.pop();
            },
            onSubmitAsync: () async {
              // save...
              await Future.delayed(1.seconds);
              if (context.mounted) {
                final items = _getFinalItems();
                context.pop(items);
              }
            },
          )
        ],
      ),
    );
  }

  Widget _buildItemRow(SalaryChangeData item, int index,
      AppLocalizations strings, AppTheme theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Reason field
          Expanded(
            flex: 2,
            child: SimpleTextField(
              controller: item.reasonController,
              decoration: InputDecoration(
                labelText: strings.description,
                hintText: widget.type == 'addition'
                    ? strings.performanceBonus
                    : strings.disciplinaryFine,
              ),
            ),
          ),
          const SizedBox(width: 8),

          // Amount field
          Expanded(
            flex: 1,
            child: SimpleTextField(
              controller: item.amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: strings.amount,
                hintText: '0.0',
              ),
            ),
          ),

          // Delete button
          GestureDetector(
            onTap: () => _removeItem(index),
            child: Padding(
              padding: const EdgeInsetsDirectional.only(start: 12, end: 2),
              child: Icon(SolarIconsOutline.trashBinMinimalistic,
                  color: theme.colors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _initializeItems() {
    for (final item in widget.initialItems) {
      _addItem(reason: item.$1, amount: item.$2);
    }
  }

  void _addItem({String? reason, double? amount}) {
    final item = SalaryChangeData(
      reason: reason ?? '',
      amount: amount != null
          ? (amount % 1 == 0 ? amount.toInt().toString() : amount.toString())
          : '',
    );

    setState(() {
      _items.add(item);
    });
  }

  void _removeItem(int index) {
    _items[index].dispose();
    setState(() {
      _items.removeAt(index);
    });
  }

  List<(String, double)> _getFinalItems() {
    final List<(String, double)> result = [];
    for (final item in _items) {
      final reason = item.reasonController.text.trim();
      final amountText = item.amountController.text.trim();
      if (reason.isNotEmpty && amountText.isNotEmpty) {
        final amount = double.tryParse(amountText);
        if (amount != null && amount > 0) {
          result.add((reason, amount));
        }
      }
    }
    return result;
  }

  @override
  void dispose() {
    // Dispose all items' controllers
    for (final item in _items) {
      item.dispose();
    }
    super.dispose();
  }
}

class SalaryChangeData {
  final TextEditingController reasonController = TextEditingController();
  final TextEditingController amountController = TextEditingController();

  SalaryChangeData({
    String reason = '',
    String amount = '',
  }) {
    reasonController.text = reason;
    amountController.text = amount;
  }

  void dispose() {
    reasonController.dispose();
    amountController.dispose();
  }

  Map<String, dynamic> toJson() => {
        "description": reasonController.text,
        "amount": double.tryParse(amountController.text) ?? 0.0,
      };
}
