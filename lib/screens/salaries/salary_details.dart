import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/l10n/util/app_localizationx.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/popups/common/icon_info_popup.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_brief_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_info_header.dart';
import 'package:ako_basma/screens/salaries/components/form/payslip_confirmation.dart';
import 'package:ako_basma/screens/salaries/components/form/salary_update_form.dart';
import 'package:ako_basma/screens/salaries/components/popups/salary_modification_form.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:solar_icons/solar_icons.dart';

class SalaryDetailsScreen extends StatefulWidget {
  const SalaryDetailsScreen({
    super.key,
    required this.employeeData,
  });
  final EmployeeData? employeeData;
  @override
  State<SalaryDetailsScreen> createState() => _SalaryDetailsScreenState();
}

class _SalaryDetailsScreenState extends State<SalaryDetailsScreen> {
  final _contextMenuButtonKey = GlobalKey();
  final _statsDurationDropdownButtonKey = GlobalKey();

  String _selectedTab = 'shift';
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: MyAppbar(
        title: strings.salaryDetails,
        actions: [
          ActionIconButton(
            key: _contextMenuButtonKey,
            icon: SolarIconsOutline.menuDots,
            onPressed: () async {
              final res = await showAppDialog(context, (context, sc) {
                return SalaryUpdateForm(onDismiss: (v) {
                  if (context.mounted) {
                    context.pop(v);
                  }
                });
              }, useRootNavigator: true);
              //   showLocalContextMenu(
              //       context: context,
              //       buttonKey: _contextMenuButtonKey,
              //       items: [
              //         {
              //           'icon': Iconsax.edit_2_copy,
              //           'label': 'Edit Name',
              //           'onPressed': () {},
              //         },
              //         {
              //           'icon': SolarIconsOutline.chatRoundUnread,
              //           'label': 'Performance',
              //           'onPressed': () {
              //             showAppDialog(context, (context, sc) {
              //               return EmployeeRatingForm(onDismiss: () {
              //                 if (context.mounted) {
              //                   context.pop();
              //                 }
              //               });
              //             }, useRootNavigator: true);
              //           },
              //         },
              //         {
              //           'icon': SolarIconsOutline.archive,
              //           'label': 'Archive',
              //           'onPressed': () {
              //             showAppDialog(context, (context, sc) {
              //               return IconInfoPopup(
              //                 schemeColor: theme.colors.warning,
              //                 iconData: SolarIconsOutline.archive,
              //                 iconBgColor: theme.colors.warningContainer,
              //                 title: 'Archive Employee',
              //                 description:
              //                     'If the employee is archived, their account will be deactivated',
              //                 footerText:
              //                     'ARE YOU SURE YOU WANT TO ARCHIVE THE EMPLOYEE?',
              //                 onCancelPressed: () {
              //                   context.pop();
              //                 },
              //                 onSubmitPressedAsync: () async {},
              //               );
              //             }, useRootNavigator: true);
              //           },
              //         },
              //         {
              //           'icon': SolarIconsOutline.handHeart,
              //           'label': 'Promotion',
              //           'onPressed': () {
              //             showAppDialog(context, (context, sc) {
              //               return EmployeePromotionForm(onDismiss: () {
              //                 if (context.mounted) {
              //                   context.pop();
              //                 }
              //               });
              //             }, useRootNavigator: true);
              //           },
              //         },
              //         {
              //           'icon': SolarIconsOutline.trashBinMinimalistic_2,
              //           'label': 'Delete employee',
              //           'color': theme.colors.error,
              //           'onPressed': () {
              //             showAppDialog(context, (context, sc) {
              //               return IconInfoPopup(
              //                 schemeColor: theme.colors.secondary,
              //                 iconData: SolarIconsOutline.trashBinMinimalistic_2,
              //                 iconColor: theme.colors.error,
              //                 iconBgColor: theme.colors.errorContainer,
              //                 title: 'Delete Employee',
              //                 description:
              //                     'This Action Cannot be Undone. All Employee Data and Related Information Will be Removed From The System',
              //                 footerText: 'ARE YOU SURE YOU WANT TO PROCEED?',
              //                 onCancelPressed: () {
              //                   context.pop();
              //                 },
              //                 onSubmitPressedAsync: () async {},
              //               );
              //             }, useRootNavigator: true);
              //           },
              //         },
              //       ],
              //       // maxWidth: 225,
              //       positionShift: (offset, size) =>
              //           offset.translate(0, size.height + 8),
              //       showDivider: true);
              if (mounted && res == true) {
                showAppSnackbar(context,
                    title: strings.addedSuccessfully, type: 'success');
              }
            },
          )
        ],
      ),
      body: widget.employeeData == null
          ? Center(
              child: Text(strings.sorrySomeErrorOccured),
            )
          : ListView(
              children: [
                EmployeeInfoHeader(
                  data: widget.employeeData!,
                  items: [
                    const SizedBox(height: 8),
                    CustomDropdown(
                        onContainer: true,
                        initialItem: 'pending',
                        // expandedHeaderPadding: EdgeInsets.only(top: 8),
                        listItemPadding: EdgeInsets.all(0),
                        itemsListPadding: EdgeInsets.all(0),
                        listItemBuilder:
                            (context, item, isSelected, onItemSelect) {
                          return Container(
                            decoration:
                                BoxDecoration(color: theme.colors.background),
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: Center(
                              child: Text(
                                // localize
                                strings.translatedLabel(item) ??
                                    toBeginningOfSentenceCase(item),
                                style: theme.textStyles.body.copyWith(
                                  color: item == 'pending'
                                      ? theme.colors.warning
                                      : theme.colors.error,
                                ),
                              ),
                            ),
                          );
                        },
                        excludeSelected: true,
                        headerBuilder: (context, selectedItem, enabled) {
                          return Center(
                            child: Text(
                              // localize

                              strings.translatedLabel(selectedItem) ??
                                  toBeginningOfSentenceCase(selectedItem),
                              style: theme.textStyles.body.copyWith(
                                color: selectedItem == 'pending'
                                    ? theme.colors.warning
                                    : theme.colors.error,
                              ),
                            ),
                          );
                        },
                        closedHeaderPadding:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        hideSelectedFieldWhenExpanded: true,
                        items: ['pending', 'unpaid'],
                        onChanged: (c) {}),
                    const SizedBox(height: 6),
                    AppOutlinedButton.async(
                      label: strings.payNow,
                      onPressed: _handlePayslipFlow,
                      tintColor: theme.colors.success,
                    ),
                  ],
                  showContract: false,
                  showEmpId: false,
                  showJoined: false,
                ),

                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.thisMonthsSalaryDetails,
                  titleColor: theme.colors.secondaryText,
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Expanded(
                              child: _buildStatsCard(
                            strings.basicSalary,
                            formatCurrency(1400000, context),
                          )),
                          const SizedBox(width: 8),
                          Expanded(
                              child: _buildStatsCard(strings.netSalary,
                                  formatCurrency(1200000, context))),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                              child: _buildStatsCard(
                                  strings.totalDeductions,
                                  formatCurrency(100000, context),
                                  theme.colors.error)),
                          const SizedBox(width: 8),
                          Expanded(
                              child: _buildStatsCard(
                                  strings.totalAdditions,
                                  formatCurrency(100000, context),
                                  theme.colors.success)),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.manualDeductions,
                  titleColor: theme.colors.secondaryText,
                  action: InkWell(
                    onTap: () => _handleSalaryModification('deduction'),
                    splashFactory: NoSplash.splashFactory,
                    child: SvgPicture.asset(
                      'assets/icons/edit.svg',
                      height: 24,
                      width: 24,
                      color: theme.colors.secondaryText,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                _buildDetailContainer(
                  context: context,
                  children: [
                    // Details section
                    _buildMinimalDetailRow(
                      strings.disciplinaryFine,
                      formatCurrency(100000, context),
                    ),
                    const SizedBox(height: 4),
                    _buildMinimalDetailRow(
                      strings.unionFees,
                      formatCurrency(100000, context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.manualAdditions,
                  titleColor: theme.colors.secondaryText,
                  action: InkWell(
                    onTap: () => _handleSalaryModification('addition'),
                    splashFactory: NoSplash.splashFactory,
                    child: SvgPicture.asset(
                      'assets/icons/edit.svg',
                      height: 24,
                      width: 24,
                      color: theme.colors.secondaryText,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                _buildDetailContainer(
                  context: context,
                  children: [
                    // Details section
                    _buildMinimalDetailRow(
                      strings.performanceBonus,
                      formatCurrency(100000, context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                SectionHeading(
                    title: strings.systemGeneratedDeductions,
                    titleColor: theme.colors.secondaryText,
                    action: SvgPicture.asset(
                      'assets/icons/edit.svg',
                      height: 24,
                      width: 24,
                      color: theme.colors.secondaryText,
                    )),
                const SizedBox(height: 8),
                _buildDetailContainer(
                  context: context,
                  children: [
                    // Details section
                    _buildMinimalDetailRow(
                      strings.absenceDeduction,
                      formatCurrency(100000, context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SectionHeading(
                    title: strings.systemGeneratedAdditions,
                    titleColor: theme.colors.secondaryText,
                    action: SvgPicture.asset(
                      'assets/icons/edit.svg',
                      height: 24,
                      width: 24,
                      color: theme.colors.secondaryText,
                    )),
                const SizedBox(height: 8),
                _buildDetailContainer(
                  context: context,
                  children: [
                    // Details section
                    _buildMinimalDetailRow(
                      strings.overtimePay,
                      formatCurrency(100000, context),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                SectionHeading(
                  title: strings.previousSalaries,
                  titleColor: theme.colors.secondaryText,
                ),
                const SizedBox(height: 2),

                Column(
                  key: const ValueKey('salary'),
                  children: [
                    EmployeeBriefCard(
                      date: DateTime.now(),
                      status: 'paid',
                      entries: [
                        {
                          'title': strings.month,
                          'value': strings.january,
                        },
                        {
                          'title': strings.netSalary,
                          'value': formatCurrency(100000, context),
                        },
                        {
                          'title': strings.totalAdditions,
                          'value': formatCurrency(50000, context),
                        },
                        {
                          'title': strings.totalDeductions,
                          'value': formatCurrency(1100000, context),
                        },
                      ],
                    ),
                    EmployeeBriefCard(
                      date: DateTime.now(),
                      status: 'unpaid',
                      entries: [
                        {
                          'title': strings.month,
                          'value': strings.january,
                        },
                        {
                          'title': strings.netSalary,
                          'value': formatCurrency(100000, context),
                        },
                        {
                          'title': strings.totalAdditions,
                          'value': formatCurrency(50000, context),
                        },
                        {
                          'title': strings.totalDeductions,
                          'value': formatCurrency(1100000, context),
                        },
                      ],
                    ),
                    EmployeeBriefCard(
                      date: DateTime.now(),
                      status: 'pending',
                      entries: [
                        {
                          'title': strings.month,
                          'value': strings.january,
                        },
                        {
                          'title': strings.netSalary,
                          'value': formatCurrency(100000, context),
                        },
                        {
                          'title': strings.totalAdditions,
                          'value': formatCurrency(50000, context),
                        },
                        {
                          'title': strings.totalDeductions,
                          'value': formatCurrency(1100000, context),
                        },
                      ],
                    ),
                  ],
                )
                //  list of cards depending on the selected tab
                // switches with animation/transition
              ],
            ),
    );
  }

  Widget _buildMinimalDetailRow(String title, String value) {
    final theme = AppTheme.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title,
            style: theme.textStyles.body3
                .copyWith(color: theme.colors.tertiaryText)),
        Text(':',
            style: theme.textStyles.body3
                .copyWith(color: theme.colors.tertiaryText)),
        Expanded(
            child: Text(value,
                style: theme.textStyles.body3
                    .copyWith(color: theme.colors.secondaryText),
                textAlign: TextAlign.end)),
      ],
    );
  }

  Future<void> _handlePayslipFlow() async {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    while (mounted) {
      final res = await showAppDialog(
        context,
        (context, sc) => const PayslipConfirmationForm(),
        backgroundColor: theme.colors.background,
        useRootNavigator: true,
      );

      if (res != true) {
        return;
      }

      final confirmationRes = await showAppDialog(
        context,
        (ctx, sc) => IconInfoPopup(
          title: strings.salaryPaymentConfirmation,
          svgIcon: 'assets/icons/banknote.svg',
          description: strings.payslipConfirmationWarning("User"),
          footerText: strings.proceedConfirmation,
          submitLabel: strings.send,
          iconPadding: const EdgeInsets.all(8),
          onSubmitPressedAsync: () async {
            await Future.delayed(1.seconds);
            ctx.pop(true); // Confirm
          },
          onCancelPressed: () {
            ctx.pop(false); // Cancel
          },
        ),
        useRootNavigator: true,
        backgroundColor: theme.colors.background,
      );

      if (confirmationRes == true) {
        if (mounted) {
          showAppSnackbar(
            context,
            title: strings.payslipSuccessNotification,
            type: 'success',
          );
        }
        return;
      }
    }
  }

  Widget _buildStatsCard(String title, String value, [Color? color]) {
    final theme = AppTheme.of(context);
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colors.strokeColor),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            title,
            style: theme.textStyles.body3.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          const SizedBox(height: 8),
          // gradient
          Text(
            value,
            style: theme.textStyles.headline4.copyWith(
              color: color ?? theme.colors.primaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailContainer({
    required BuildContext context,
    required List<Widget> children,
    EdgeInsetsGeometry? margin = const EdgeInsets.symmetric(horizontal: 16),
    EdgeInsetsGeometry? padding =
        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: theme.colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colors.strokeColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  void _handleSalaryModification(String type) {
    showAppDialog(context, (context, sc) {
      return SalaryModificationForm(
        type: type,
        // for dummy reasons
        initialItems: type == 'addition'
            ? [
                ('Performance Bonus', 100000),
              ]
            : [
                ('Disciplinary Fine', 100000),
                ('Union Fees', 100000),
              ],
      );
    }, useRootNavigator: true);
  }
}
