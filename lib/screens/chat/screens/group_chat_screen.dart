import 'dart:io';
import 'dart:math';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/chat/components/chat_input_field.dart';
import 'package:ako_basma/screens/chat/components/attachment_menu/attachment_menu.dart';
import 'package:ako_basma/screens/chat/components/attachment_menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/components/attachment_menu/location_sharing_modal.dart';
import 'package:ako_basma/screens/chat/components/header/group_chat_header.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';

class GroupChatScreen extends StatefulWidget {
  final String groupName;
  final String? groupImage;
  final List<EmployeeData> members;

  const GroupChatScreen({
    super.key,
    required this.groupName,
    this.groupImage,
    required this.members,
  });

  @override
  State<GroupChatScreen> createState() => _GroupChatScreenState();
}

class _GroupChatScreenState extends State<GroupChatScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: colors.background,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Group Chat Header
          GroupChatHeader(
            groupName: widget.groupName,
            groupImage: widget.groupImage,
            memberCount: widget.members.length,
            onBackPressed: () {
              context.pop();
            },
          ),

          // Messages area with initial group creation message
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Today divider
                  Center(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: colors.background,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: colors.strokeColor),
                      ),
                      child: Text(
                        strings.today,
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Group creation message
                  Text(
                    strings.nameCreatedGroup(strings.you),
                    style: textStyles.body2.copyWith(
                      color: colors.tertiaryText,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          // Chat input field at bottom
          Padding(
            padding: EdgeInsets.fromLTRB(
                16, 0, 16, max(24, MediaQuery.paddingOf(context).bottom)),
            child: ChatInputField(
              onSendMessage: (message) {
                // Handle sending message in group
                // TODO: Implement group message sending logic
                print('Group message sent: $message');
              },
              onVoicePressed: () {
                // TODO: Handle voice message in group
                // implement voice message sending logic here
              },
            ),
          ),
        ],
      ),
    );
  }
}
