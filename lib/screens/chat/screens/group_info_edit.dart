import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/chat/screens/group_chat_screen.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

class GroupInfoEditScreen extends StatefulWidget {
  final bool newGroup;
  final List<EmployeeData> selectedEmployees;

  const GroupInfoEditScreen({
    super.key,
    required this.selectedEmployees,
    this.newGroup = false,
  });

  @override
  State<GroupInfoEditScreen> createState() => _GroupInfoEditScreenState();
}

class _GroupInfoEditScreenState extends State<GroupInfoEditScreen> {
  // Text controller for group name input
  final TextEditingController _groupNameController = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  File? _selectedGroupImage;
  final FocusNode _groupNameFocusNode = FocusNode();
  final _attachmentButtonKey = GlobalKey(debugLabel: 'groupImageContainerKey');

  @override
  void dispose() {
    _groupNameController.dispose();
    _groupNameFocusNode.dispose();
    super.dispose();
  }

  /// Handle group photo selection from gallery
  Future<void> _selectGroupPhoto() async {
    final strings = AppLocalizations.of(context)!;
    try {
      final res = await showLocalPickerMenu(
        buttonKey: _attachmentButtonKey,
        context: context,
        allowedTypes: ['image'],
        allowMultiple: false,
        maxSizeInMB: 25,
      );
      setState(() {
        if (res is File) {
          _selectedGroupImage = (res);
        }
      });
    } catch (e) {
      // Handle image selection error
      if (mounted) {
        showAppSnackbar(context,
            title: strings.sorrySomeErrorOccured, type: 'error');
      }
    }
  }

  /// Handle group creation action
  void _handleNextAction() {
    final groupName = _groupNameController.text.trim();

    // validation..
    if (groupName.isEmpty) {
      return;
    }
    context.go('/chat/group-chat', extra: {
      'name': groupName,
      'members': widget.selectedEmployees,
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    final title = widget.newGroup ? strings.newGroup : strings.editGroup;

    return Scaffold(
      backgroundColor: colors.background,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: SafeArea(
          bottom: false,
          child: Container(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Back button
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Icon(
                      Directionality.of(context) == TextDirection.ltr
                          ? SolarIconsOutline.altArrowLeft
                          : SolarIconsOutline.altArrowRight,
                      color: colors.primaryText,
                      size: 32,
                    ),
                  ),

                  // Title and step indicator
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: textStyles.buttonSmall.copyWith(
                          color: colors.primaryText,
                          fontSize: 14,
                        ),
                        textScaler: TextScaler.noScaling,
                      ),
                    ],
                  ),

                  // Next button
                  InkWell(
                    splashFactory: NoSplash.splashFactory,
                    onTap: _handleNextAction,
                    child: Text(
                      widget.newGroup ? strings.create : strings.save,
                      style: textStyles.textButton.copyWith(
                        fontSize: 14,
                        color: _groupNameController.text.trim().isNotEmpty
                            ? colors.primary
                            : colors.disabled,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header

          // Group creation section with camera icon and group name input
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: colors.backgroundContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                // Camera icon for group photo selection
                GestureDetector(
                  key: _attachmentButtonKey,
                  onTap: _selectGroupPhoto,
                  child: Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _selectedGroupImage != null
                          ? Colors.transparent
                          : colors.backgroundContainer,
                    ),
                    clipBehavior: Clip.hardEdge,
                    child: _selectedGroupImage != null
                        ? Stack(
                            children: [
                              Container(
                                width: 56,
                                height: 56,
                                foregroundDecoration: BoxDecoration(
                                    color: Colors.black.withAlpha(102)),
                                child: Image.file(
                                  _selectedGroupImage!,
                                  width: 56,
                                  height: 56,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              const Center(
                                child: Icon(
                                  SolarIconsOutline.camera,
                                  color: Colors.white,
                                  size: 24,
                                ),
                              ),
                            ],
                          )
                        : Center(
                            child: Icon(
                              SolarIconsOutline.camera,
                              color: colors.secondaryText,
                              size: 24,
                            ),
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                // Group name input field with note icon
                Expanded(
                  child: Row(
                    children: [
                      // Note icon
                      Icon(
                        SolarIconsOutline.penNewSquare,
                        color: colors.tertiaryText,
                        size: 24,
                      ),
                      const SizedBox(width: 8),

                      // Group name text field
                      Expanded(
                        child: TextField(
                          controller: _groupNameController,
                          focusNode: _groupNameFocusNode,
                          style: textStyles.body2.copyWith(
                            color: colors.primaryText,
                          ),
                          decoration: InputDecoration(
                            hintText: strings.groupName,
                            hintStyle: textStyles.body2.copyWith(
                              color: colors.tertiaryText,
                            ),
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                          onChanged: (value) {
                            // Update UI when text changes to enable/disable create button
                            setState(() {});
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Divider
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Divider(
              color: colors.strokeColor,
              thickness: 1,
              height: 0,
            ),
          ),

          // Selected members list title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            // alignment: Alignment.centerLeft,
            child: Text(
              strings.membersSelectedOfTotal(
                  widget.selectedEmployees.length, dummyEmployees.length),
              style: textStyles.body3.copyWith(
                color: colors.tertiaryText,
              ),
            ),
          ),
          const SizedBox(height: 4),
          // Selected members list
          Expanded(
            child:
                // margin: const EdgeInsets.symmetric(horizontal: 16),
                ListView.separated(
              padding: const EdgeInsets.fromLTRB(0, 8, 0, 16),
              itemCount: widget.selectedEmployees.length,
              separatorBuilder: (context, index) => SizedBox(height: 12),
              itemBuilder: (context, index) {
                final employee = widget.selectedEmployees[index];

                return _buildListItem(employee);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(
    EmployeeData data,
  ) {
    final theme = AppTheme.of(context);

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 10),
      child: Row(
        children: [
          // Profile picture
          SizedBox(
            width: 32,
            height: 32,
            child: ClipOval(
              child: ImageContainer(
                url: data.imageUrl,
                width: 32,
                height: 32,
                fit: BoxFit.cover,
              ),
            ),
          ),

          const SizedBox(width: 8),
          // Name
          Expanded(
            child: Text(
              data.name,
              style: theme.textStyles.headline4,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
