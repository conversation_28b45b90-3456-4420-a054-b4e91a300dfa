import 'dart:math';

import 'package:ako_basma/screens/chat/components/header/ai_chat_header.dart';
import 'package:ako_basma/screens/chat/components/message_bubble.dart';
import 'package:ako_basma/screens/chat/components/sections/ai_chat_section.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/components/header/chat_header.dart';
import 'package:ako_basma/screens/chat/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/components/chat_input_field.dart';

class AiChatScreen extends StatefulWidget {
  const AiChatScreen({
    super.key,
  });

  @override
  State<AiChatScreen> createState() => _AiChatScreenState();
}

class _AiChatScreenState extends State<AiChatScreen> {
  static const List<Map<String, dynamic>> _messages = [
    {
      'id': '1',
      'message':
          'Awesome, thanks for letting me know! Can\'t wait for my delivery. 🎉',
      'time': '10:11',
      'isSentByMe': true,
      'isRead': true,
      'profileImage': '',
    },
    {
      'id': '2',
      'message': 'No problem at all!\nI\'ll be there in about 15 minutes.',
      'time': '10:11',
      'isSentByMe': false,
      'isRead': false,
      'profileImage': 'assets/images/person.png',
    },
    {
      'id': '3',
      'message': 'I\'ll text you when I arrive.',
      'time': '10:11',
      'isSentByMe': false,
      'isRead': false,
      'profileImage': 'assets/images/person.png',
    },
    {
      'id': '4',
      'message': 'Great! 😊',
      'time': '10:12',
      'isSentByMe': true,
      'isRead': true,
      'profileImage': '',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Scaffold(
      backgroundColor: colors.background,
      // Hide bottom navigation bar by not using shell route
      body: Column(
        children: [
          // Chat Header with back button, user info
          AIChatHeader(onBackPressed: () {
            context.pop();
          }),
          // Messages List - Expandable to take remaining space
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              children: [
                AiChatWelcomeSection(),
                ..._messages
                    .map((message) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: MessageBubble(
                            message: message['message'],
                            time: message['time'],
                            isSentByMe: message['isSentByMe'],
                            isRead: message['isRead'],
                            profileImage: message['profileImage'],
                            showAvatar: false, // No avatars needed
                          ),
                        ))
                    .toList(),
              ],
            ),
          ),

          // Input field at bottom
          Padding(
            padding: EdgeInsets.fromLTRB(
                16, 0, 16, max(24, MediaQuery.paddingOf(context).bottom)),
            child: ChatInputField(
              onSendMessage: (message) {
                // Handle sending message
                // TODO: Implement message sending logic
                print('Message sent: $message');
              },
              onVoicePressed: () {
                // Handle voice message
                print('Voice message pressed');
              },
            ),
          ),
        ],
      ),
    );
  }
}
