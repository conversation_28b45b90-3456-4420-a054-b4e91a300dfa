import 'dart:math';

import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:go_router/go_router.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/components/header/chat_header.dart';
import 'package:ako_basma/screens/chat/components/chat_messages_list.dart';
import 'package:ako_basma/screens/chat/components/chat_input_field.dart';

class IndividualChatScreen extends StatelessWidget {
  final String userName;
  final String userImage;
  final DateTime lastSeen;
  final bool isOnline;

  const IndividualChatScreen({
    super.key,
    required this.userName,
    required this.userImage,
    required this.lastSeen,
    this.isOnline = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Scaffold(
      backgroundColor: colors.background,
      // Hide bottom navigation bar by not using shell route
      body: Column(
        children: [
          // Chat Header with back button, user info
          ChatHeader(
            userName: userName,
            userImage: userImage,
            // might wanna change this later.. depending on the duration b/w today and last seen.
            lastSeen: formatTime(lastSeen, context),
            isOnline: isOnline,
            showClockOutBanner: true,
            onBackPressed: () => context.pop(),
            // showEndChat: true,
            showApprovalActions: true,
          ),

          // Messages List - Expandable to take remaining space
          Expanded(
            child:
                //  Chat(
                //   messages: [],
                //   // chatController: _chatController,

                //   user: const types.User(id: 'human'),
                //   onSendPressed: (text) {
                //     // _chatController.insertMessage(
                //     //   TextMessage(
                //     //     // Better to use UUID or similar for the ID - IDs must be unique.
                //     //     id: '${Random().nextInt(1000) + 1}',
                //     //     authorId: 'user1',
                //     //     createdAt: DateTime.now().toUtc(),
                //     //     text: text,
                //     //   ),
                //     // );
                //   },
                // ),
                ChatMessagesList(),
          ),

          // Input field at bottom
          Padding(
            padding: EdgeInsets.fromLTRB(
                16, 0, 16, max(24, MediaQuery.paddingOf(context).bottom)),
            child: ChatInputField(
              onSendMessage: (message) {
                // Handle sending message
                // TODO: Implement message sending logic
                print('Message sent: $message');
              },
              onVoicePressed: () {
                // Handle voice message
                print('Voice message pressed');
              },
            ),
          ),
        ],
      ),
    );
  }
}
