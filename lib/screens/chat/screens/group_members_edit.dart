import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';

class GroupMembersEditScreen extends StatefulWidget {
  const GroupMembersEditScreen({
    super.key,
    this.newGroup = false,
  });

  final bool newGroup;
  @override
  State<GroupMembersEditScreen> createState() => _GroupMembersEditScreenState();
}

class _GroupMembersEditScreenState extends State<GroupMembersEditScreen> {
  // List to track selected employees
  final Set<EmployeeData> _selectedEmployees = {};

  // Generated employee data - in real app this would come from API
  late final List<EmployeeData> _employees;

  @override
  void initState() {
    super.initState();
    // Generate 12 employees with different names for demo
    _employees = dummyEmployees;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
        final strings = AppLocalizations.of(context)!;

    final title = widget.newGroup ? strings.addEmployee : strings.editMembers;
    print(MediaQuery.paddingOf(context).top);
    return Scaffold(
      backgroundColor: colors.background,
      appBar: PreferredSize(
        preferredSize:const Size.fromHeight(kToolbarHeight),
        child: SafeArea(
          bottom: false,
          child: Container(
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 16),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Back button
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Icon(
                      Directionality.of(context) == TextDirection.ltr
                          ? SolarIconsOutline.altArrowLeft
                          : SolarIconsOutline.altArrowRight,
                      color: colors.primaryText,
                      size: 32,
                    ),
                  ),

                  // Title and step indicator
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: textStyles.buttonSmall.copyWith(
                          color: colors.primaryText,
                          fontSize: 14,
                        ),
                        textScaler: TextScaler.noScaling,
                      ),
                      Text(
                        '${_selectedEmployees.length.toString().padLeft(_employees.length.toString().length, '0')}/${_employees.length.toString()}',
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                          fontSize: 10,
                        ),
                        textScaler: TextScaler.noScaling,
                      ),
                    ],
                  ),

                  // Next button
                  InkWell(
                    splashFactory: NoSplash.splashFactory,
                    onTap: _handleNextAction,
                    child: Text(
                      strings.next,
                      style: textStyles.textButton.copyWith(
                        fontSize: 14,
                        color: _selectedEmployees.isNotEmpty
                            ? colors.primary
                            : colors.disabled,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header

          // Search bar
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: SearchTextField(
              hintText: strings.search,
            ),
          ),

          // Selected employees at the top (if any)

          AnimatedSize(
            duration: 300.milliseconds,
            alignment: Alignment.topCenter,
            child: (_selectedEmployees.isNotEmpty)
                ? SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 4),
                    child: Row(
                      spacing: 12,
                      children: [
                        // Show first 2 selected employees as avatars with names
                        ..._selectedEmployees.map((e) {
                          final employee = e;
                          return _buildSelectedEmployeeAvatar(employee);
                        }),
                      ],
                    ),
                  ).animate(delay: 250.milliseconds).fadeIn()
                : const SizedBox(),
          ),

          // Employee list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.only(top: 8, bottom: 24),
              itemCount: _employees.length,
              itemBuilder: (context, index) {
                final employee = _employees[index];
                // final isSelected = _selectedEmployees.contains(index);

                return _buildListItem(employee);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(
    EmployeeData data,
  ) {
    final theme = AppTheme.of(context);
    final isSelected = _selectedEmployees.contains(data);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            if (!isSelected) {
              _selectedEmployees.add(data);
            } else {
              _selectedEmployees.remove(data);
            }
          });
        },
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 10),
          child: Row(
            children: [
              // Profile picture
              SizedBox(
                width: 32,
                height: 32,
                child: ClipOval(
                  child: ImageContainer(
                    url: data.imageUrl,
                    width: 32,
                    height: 32,
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              const SizedBox(width: 8),
              // Name
              Expanded(
                child: Text(
                  data.name,
                  style: theme.textStyles.headline4,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Checkbox
              AnimatedContainer(
                duration: 200.milliseconds,
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: theme
                        .colors.primary, // Always use primary color for border
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  color: isSelected ? theme.colors.primary : Colors.transparent,
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: theme.colors.background,
                        size: 16,
                      ).animate().fadeIn()
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build selected employee avatar widget for top section
  Widget _buildSelectedEmployeeAvatar(EmployeeData employee) {
    final theme = AppTheme.of(context);
    return InkWell(
      onTap: () {
        setState(() {
          _selectedEmployees.remove(employee);
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: SizedBox(
        width: 100,
        child: Stack(
          children: [
            Column(
              children: [
                // this space for close button top -4 pos compensation...
                const SizedBox(height: 4),
                SizedBox(
                  width: 64,
                  height: 64,
                  child: ClipOval(
                    child: ImageContainer(
                      url: employee.imageUrl,
                      width: 64,
                      height: 64,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                // Employee name below avatar
                Text(
                  employee.name, // Show first name only
                  style: theme.textStyles.headline4,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            PositionedDirectional(
                top: 0,
                end: 8,
                child: Container(
                  height: 24,
                  width: 24,
                  decoration: ShapeDecoration(
                      shape: const CircleBorder(),
                      color: theme.colors.strokeColor),
                  child: Center(
                    child: Icon(
                      Icons.close,
                      color: theme.colors.secondaryText,
                      size: 16,
                      applyTextScaling: false,
                    ),
                  ),
                ))
          ],
        ),
      ),
    );
  }

  void _handleNextAction() {
    if (widget.newGroup) {
      if (_selectedEmployees.isNotEmpty) {
        context.go(
          '/chat/groupInfoEdit?create=true',
          extra: {
            'members': _selectedEmployees.toList(),
          },
        );
      }
    }
  }
}
