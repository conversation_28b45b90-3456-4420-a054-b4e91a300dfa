import 'dart:math';

import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

/// Contact info popup component displayed as bottom modal sheet
/// Shows user profile picture, name, contact actions and details
class ContactInfoPopup extends StatelessWidget {
  const ContactInfoPopup({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Profile picture - rectangular with rounded corners
        Container(
          height: 200,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: colors.primaryVariant,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: ImageContainer(
              url: 'https://randomuser.me/api/portraits/men/67.jpg',
              height: 200,
              placeholderFit: BoxFit.scaleDown,
              fit: BoxFit.cover,
            ),

            // Image.asset(
            //   userImage,
            //   errorBuilder: (context, error, stackTrace) {
            //     // Fallback to initials if image fails to load
            //     return Container(
            //       color: colors.primary.withOpacity(0.1),
            //       child: Center(
            //         child: Text(
            //           userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
            //           style: textStyles.headline.copyWith(
            //             color: colors.primary,
            //             fontSize: 48,
            //           ),
            //         ),
            //       ),
            //     );
            //   },
            // ),
          ),
        ),
        const SizedBox(height: 8),

        // User name - left aligned with primary color
        Text(
          "Jack Tamblyn",
          style: textStyles.headline3.copyWith(
            color: colors.primary,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.left,
        ),

        // Action buttons (Call, Chat, Email) - in single container with primary variant background
        Container(
            margin: const EdgeInsets.only(top: 12),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(12),
            ),
            child: _buildActionsRow(context)),
        const SizedBox(height: 12),
        // Contact details - separate containers for email and phone
        Column(
          children: [
            // Email container with border
            _buildDetailRow(
              context,
              icon: Iconsax.sms_copy,
              label: strings.email,
              value: embedLtr('<EMAIL>'),
            ),
            const SizedBox(height: 8),
            _buildDetailRow(
              context,
              icon: Iconsax.call_calling_copy,
              label: strings.phone,
              value: embedLtr('+44 ************'),
            ),
          ],
        ),
        const SizedBox(
            height:
                12), // Close button - positioned at bottom with more spacing
        Container(
          margin: EdgeInsets.only(bottom: 16),
          width: double.infinity,
          height: 48,
          child: OutlinedButton(
            onPressed: () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: colors.tertiaryText),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              strings.close,
              style: textStyles.body
                  .copyWith(color: colors.secondaryText, fontSize: 20),
              textScaler: TextScaler.noScaling,
            ),
          ),
        ),
      ],
    );
  }

  /// Helper method to build contact detail containers with border, icon, label and value
  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
          color: theme.colors.background,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colors.primaryVariant)),
      child: Row(
        children: [
          Icon(
            icon,
            size: 24,
            color: theme.colors.secondaryText,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              label,
              style: theme.textStyles.body.copyWith(
                color: theme.colors.secondaryText,
              ),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: theme.textStyles.textButton.copyWith(
              color: theme.colors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsRow(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    Widget buildButton(String type, void Function() onTap) {
      return Flexible(
        child: InkWell(
          onTap: onTap,
          splashFactory: NoSplash.splashFactory,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Container(
                    height: 56,
                    width: 56,
                    decoration: ShapeDecoration(
                      shape: const CircleBorder(),
                      color: theme.colors.primaryVariant,
                    ),
                    child: Center(
                      child: Icon(
                        switch (type) {
                          'call' => SolarIconsOutline.phone,
                          'email' => Iconsax.sms_copy,
                          'chat' => SolarIconsOutline.chatRoundLine,
                          String() => SolarIconsOutline.chatRoundLine
                        },
                        size: 32,
                        color: switch (type) {
                          'call' => theme.colors.primary,
                          'email' => theme.colors.info,
                          'chat' => theme.colors.primary,
                          String() => theme.colors.primary
                        },
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  // localize
                  switch (type) {
                    'call' => strings.call,
                    'email' => strings.email,
                    'chat' => strings.chat,
                    String() => strings.call,
                  },
                  style: theme.textStyles.body
                      .copyWith(color: theme.colors.secondaryText),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        buildButton('call', () {}),
        buildButton('chat', () {}),
        buildButton('email', () {}),
      ],
    );
  }
}
