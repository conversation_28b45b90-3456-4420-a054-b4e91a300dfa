import 'package:ako_basma/components/appbar/profile_appbar.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/components/form/tab_selector.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/chat/components/chat_list_item.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/screens/chat/components/fab_overlay_menu.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  String _selectedTab = 'all';
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: theme.colors.background,
      appBar: ProfileAppBar(),
      floatingActionButton: FabOverlayMenu(),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Custom Search Bar below greeting
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: const SearchTextField(),
          ),

          // Switch button group (dynamic labels)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colors.backgroundContainer,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colors.primaryVariant,
              ),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: TabSelector(
              items: [
                ChipItem(
                  label: strings.all,
                  tag: 'all',
                ),
                ChipItem(
                  label: strings.unread,
                  tag: 'unread',
                ),
                ChipItem(
                  label: strings.teams,
                  tag: 'teams',
                ),
              ],
              selectedItems: [_selectedTab],
              onItemTap: (tag) {
                setState(() {
                  _selectedTab = tag;
                });
              },
            ),
          ),

          // Chat list component
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
              itemCount: dummyChatData.length, // Account for dividers
              separatorBuilder: (context, index) {
                return Divider(
                  color: theme.colors.strokeColor,
                  indent: 16,
                  endIndent: 16,
                  height: 0,
                );
              },
              itemBuilder: (context, index) {
                final chatItem = dummyChatData[index];

                return ChatListItem(
                  name: chatItem['name'],
                  message: chatItem['message'],
                  time: chatItem['time'],
                  profileImage: chatItem['profileImage'],
                  unreadCount: index == 0 ? 1 : 0,
                  isOnline: index == 0, // First item shows online status
                  isTyping: index == 0, // First item shows typing indicator
                  onTap: () {
                    // Navigate to individual chat screen with user data
                    context.go(
                      '/chat/individual-chat?userName=${Uri.encodeComponent(chatItem['name'])}'
                      '&userImage=${Uri.encodeComponent(chatItem['profileImage'])}'
                      '&lastSeen=${(chatItem['time']).toString()}'
                      '&isOnline=${index == 0}',
                    );
                  },
                  status: index == 0
                      ? 'unread'
                      : index == 1
                          ? 'delivered'
                          : 'sent',
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/*
// ============================================
// COMMENTED OUT CHAT FUNCTIONALITY FOR LATER
// ============================================

// UNCOMMENT THESE IMPORTS WHEN YOU NEED TO USE THE CHAT FUNCTIONALITY:
// import 'package:ako_basma/screens/chat/constant.dart';
// import 'package:ako_basma/screens/chat/screen/menu.dart';  
// import 'package:ako_basma/screens/chat/service/chat.dart';
// import 'package:flutter_chat_ui/flutter_chat_ui.dart';
// import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
// import 'package:flutter_gemini/flutter_gemini.dart';

// UNCOMMENT THESE VARIABLES IN THE STATE CLASS WHEN YOU NEED THE CHAT FUNCTIONALITY:
// bool _processing = false;
// final _messages = <types.TextMessage>[];
// String _lastModelText = '';
// final _chatKey = GlobalKey<ChatState>();

// REPLACE THE CURRENT BUILD METHOD WITH THIS WHEN YOU WANT TO USE CHAT:
/*
@override
Widget build(BuildContext context) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Scaffold(
    body: Chat(
      key: _chatKey,
      scrollToUnreadOptions: ScrollToUnreadOptions(
        scrollOnOpen: false,
      ),
      messages: [..._messages.reversed.toList()],
      onSendPressed: (text) => _sendMessage(text.text),
      user: types.User(id: 'user'),
      typingIndicatorOptions: TypingIndicatorOptions(
          typingUsers: _processing ? [botUser] : [],
          typingMode: TypingIndicatorMode.name),
      theme: DarkChatTheme(
        backgroundColor: colors.background,
        inputMargin: const EdgeInsets.all(10),
        secondaryColor: colors.backgroundContainer,
        primaryColor: colors.primary,
        inputBackgroundColor: colors.backgroundContainer,
        inputBorderRadius: BorderRadius.circular(20),
      ),
      emptyState: Center(
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          const Text(
            '✨',
            style: TextStyle(
              fontSize: 40,
            ),
          ),
          Text(
            'It\'s quite lonely here.',
            style: textStyles.body.copyWith(
              color: colors.primaryText,
            ),
          )
        ]),
      ),
    ),
    floatingActionButtonLocation: FloatingActionButtonLocation.startTop,
    floatingActionButton: IconButton.filledTonal(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            builder: (ctx) {
              return AppMenu();
            },
          );
        },
        icon: Icon(Icons.menu)),
  );
}
*/

// ADD THESE METHODS TO THE STATE CLASS WHEN YOU NEED CHAT FUNCTIONALITY:
/*
void _sendMessage(String text) async {
  setState(() {
    _messages
        .add(types.TextMessage(id: _lastId(), author: humanUser, text: text));
    _processing = true;
  });
  final gemini = Gemini.instance;
  final result = await gemini.chat(
    _messages
        .map(
          (e) => Content(
            parts: [Parts(text: e.text)],
            role: e.author.id,
          ),
        )
        .toList(),
  );
  var reply;
  try {
    reply = result?.output ?? 'No response received';
  } catch (e) {
    reply = e.toString();
  }

  setState(() {
    _processing = false;
    _messages.add(types.TextMessage(
        id: (_messages.isNotEmpty ? ((int.parse(_messages.last.id)) + 1) : 0)
            .toString(),
        author: botUser,
        text: reply));
    _chatKey.currentState?.scrollToMessage(_lastId(1));
  });
}

String _lastId([int skip = 0]) =>
    ((_messages.isNotEmpty ? ((int.parse(_messages.last.id)) + 1) : 0) - skip)
        .toString();
*/
*/
