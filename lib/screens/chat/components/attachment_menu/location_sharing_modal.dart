import 'dart:math';

import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/chat/components/attachment_menu/location_sharing_map.dart';
import 'package:ako_basma/styles/ui.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

/// Location sharing modal component for chat attachments
/// Displays a bottom modal with map, live location option, and nearby places
class LocationSharingModal extends StatefulWidget {
  final Function(String locationType, Map<String, dynamic> locationData)
      onLocationSelected;

  const LocationSharingModal({
    super.key,
    required this.onLocationSelected,
  });

  @override
  State<LocationSharingModal> createState() => _LocationSharingModalState();
}

class _LocationSharingModalState extends State<LocationSharingModal> {
  /// Handle live location sharing
  void _handleLiveLocationShare() {
    widget.onLocationSelected('live_location', {
      'type': 'live',
      'duration': '8 hours', // Default live location duration
    });
    Navigator.pop(context);
  }

  /// Handle current location sharing
  void _handleCurrentLocationShare() {
    widget.onLocationSelected('current_location', {
      'type': 'current',
      'accuracy': '14 metres',
      'latitude': 33.8938, // Sample coordinates (Baghdad)
      'longitude': 44.3661,
    });
    Navigator.pop(context);
  }

  /// Handle close button press
  void _handleClose() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        AnimatedContainer(
          height: 255,
          duration: 300.milliseconds,
          foregroundDecoration: BoxDecoration(
            border: Border.all(
              color: colors.primaryVariant,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: LocationSharingMap(
              latitude: 47.6540235,
              longitude: -122.308572,
            ),
          ),
        ),
        // Container(
        //   height: 255,
        //   width: double.infinity,
        //   decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(12),
        //     border: Border.all(color: colors.primaryVariant),
        //   ),
        //   child: ClipRRect(
        //     borderRadius: BorderRadius.circular(12),
        //     child: Stack(
        //       children: [
        //         // Map image
        //         Container(
        //           width: double.infinity,
        //           height: double.infinity,
        //           decoration: BoxDecoration(
        //             color: colors.background,
        //           ),
        //           child: Image.asset(
        //             'assets/images/location_dark.png',
        //             fit: BoxFit.cover,
        //             errorBuilder: (context, error, stackTrace) {
        //               // Fallback if image fails to load
        //               return Container(
        //                 color: Colors.grey[800],
        //                 child: Icon(
        //                   Icons.map,
        //                   color: colors.primary,
        //                   size: 60,
        //                 ),
        //               );
        //             },
        //           ),
        //         ),

        //         // User location pin in center
        //         Center(
        //           child: Container(
        //             width: 40,
        //             height: 40,
        //             decoration: BoxDecoration(
        //               shape: BoxShape.circle,
        //               border: Border.all(
        //                 color: colors.background,
        //                 width: 2,
        //               ),
        //             ),
        //             child: ClipOval(
        //               child: Image.asset(
        //                 'assets/images/person.png',
        //                 fit: BoxFit.cover,
        //                 errorBuilder: (context, error, stackTrace) {
        //                   return Container(
        //                     color: colors.primary,
        //                     child: Icon(
        //                       Icons.person,
        //                       color: colors.background,
        //                       size: 20,
        //                     ),
        //                   );
        //                 },
        //               ),
        //             ),
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),

        // Send Your Live Location Container
        const SizedBox(height: 8),
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: _handleLiveLocationShare,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                color: colors.background,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
              ),
              child: Text(
                strings.sendYourLiveLocation,
                style: textStyles.body2.copyWith(
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Nearby Places Section
        Text(
          strings.nearbyPlaces,
          style: textStyles.headline4.copyWith(
            color: colors.secondaryText,
          ),
        ),
        const SizedBox(height: 12),

        // Send Your Current Location Container
        Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: _handleCurrentLocationShare,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: colors.background,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colors.primaryVariant),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Main text
                  Text(
                    strings.sendYourCurrentLocation,
                    style: textStyles.body2.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                  const SizedBox(height: 4),

                  Text(
                    strings.approximateToDistanceMetres(14),
                    style: textStyles.body3.copyWith(
                      color: colors.tertiaryText,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.fromLTRB(
              0, 12, 0, max(24, MediaQuery.paddingOf(context).bottom)),
          child: AppOutlinedButton(
            label: strings.close,
            onTap: () {
              context.pop();
            },
            tintColor: colors.secondaryText,
            outlineColor: colors.secondaryText,
            style: AppButtonStyle.primaryOutlinedButtonStyle(
              context,
              color: colors.tertiaryText,
              radius: 8,
              fontColor: colors.secondaryText,
              textStyle: textStyles.body
                  .copyWith(color: colors.secondaryText, fontSize: 18),
            ).copyWith(),
            padding: const EdgeInsets.symmetric(vertical: 8),
          ),
        ),
        // Close button
      ],
    );
  }
}
