import 'dart:math';

import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Contact selection modal component for chat attachments
/// Displays a full-screen bottom modal with searchable contact list
/// organized alphabetically with checkboxes for selection
class ContactSelectionModal extends StatefulWidget {
  final Function(List<Contact>) onContactsSelected;

  const ContactSelectionModal({
    super.key,
    required this.onContactsSelected,
    this.sc,
  });
  final ScrollController? sc;

  @override
  State<ContactSelectionModal> createState() => _ContactSelectionModalState();
}

class _ContactSelectionModalState extends State<ContactSelectionModal> {
  final TextEditingController _searchController = TextEditingController();
  List<Contact> _allContacts = [];
  List<Contact> _filteredContacts = [];
  Set<String> _selectedContactIds = <String>{};

  @override
  void initState() {
    super.initState();
    _initializeContacts();
    _filteredContacts = _allContacts;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Initialize sample contacts data
  /// In real app, this would fetch from contacts API or database
  void _initializeContacts() {
    _allContacts = [
      Contact(
          id: '1',
          name: 'Aillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '2',
          name: 'Billan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '3',
          name: 'Cillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '4',
          name: 'Cillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '5',
          name: 'Cillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '6',
          name: 'Eillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '7',
          name: 'Eillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
      Contact(
          id: '8',
          name: 'Eillan James',
          profileImage: 'https://randomuser.me/api/portraits/men/67.jpg'),
    ];
  }

  /// Filter contacts based on search query
  void _filterContacts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredContacts = _allContacts;
      } else {
        _filteredContacts = _allContacts
            .where((contact) =>
                contact.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  /// Toggle contact selection
  void _toggleContactSelection(Contact contact) {
    setState(() {
      if (_selectedContactIds.contains(contact.id)) {
        _selectedContactIds.remove(contact.id);
      } else {
        _selectedContactIds.add(contact.id);
      }
    });
  }

  /// Group contacts alphabetically by first letter
  Map<String, List<Contact>> _groupContactsAlphabetically() {
    final Map<String, List<Contact>> groupedContacts = {};

    for (final contact in _filteredContacts) {
      // TODO: check how this works in arabic...
      final firstLetter = contact.name[0].toUpperCase();
      if (!groupedContacts.containsKey(firstLetter)) {
        groupedContacts[firstLetter] = [];
      }
      groupedContacts[firstLetter]!.add(contact);
    }

    // Sort the keys alphabetically
    final sortedKeys = groupedContacts.keys.toList()..sort();
    final sortedMap = <String, List<Contact>>{};
    for (final key in sortedKeys) {
      sortedMap[key] = groupedContacts[key]!;
    }

    return sortedMap;
  }

  /// Handle close button press
  void _handleClose() {
    final selectedContacts = _allContacts
        .where((contact) => _selectedContactIds.contains(contact.id))
        .toList();
    widget.onContactsSelected(selectedContacts);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;
    final groupedContacts = _groupContactsAlphabetically();

    return Column(
      children: [
        // Search bar
        SearchTextField(
          controller: _searchController,
          hintText: strings.searching,
          onChanged: _filterContacts,
          onContainer: true,
        ),

        // Contacts list
        Expanded(
          child: _filteredContacts.isEmpty
              ? _buildEmptyState(colors, textStyles)
              : _buildContactsList(groupedContacts, colors, textStyles),
        ),

        // Close button
        Container(
          margin: EdgeInsets.fromLTRB(
              0, 0, 0, max(MediaQuery.paddingOf(context).bottom, 16)),
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: _handleClose,
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: colors.tertiaryText,
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              backgroundColor: Colors.transparent,
            ),
            child: Text(
              strings.close,
              style: textStyles.body.copyWith(
                color: colors.secondaryText,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build empty state when no contacts found
  Widget _buildEmptyState(AppColors colors, TextStyles textStyles) {
    final strings = AppLocalizations.of(context)!;
    return Center(
      child: Text(
        strings.noContactsFound,
        style: textStyles.body.copyWith(
          color: colors.tertiaryText,
        ),
      ),
    );
  }

  /// Build the contacts list grouped alphabetically
  Widget _buildContactsList(
    Map<String, List<Contact>> groupedContacts,
    AppColors colors,
    TextStyles textStyles,
  ) {
    return ListView.builder(
      controller: widget.sc,
      padding: const EdgeInsets.symmetric(horizontal: 0),
      itemCount: groupedContacts.length,
      itemBuilder: (context, index) {
        final letter = groupedContacts.keys.elementAt(index);
        final contacts = groupedContacts[letter]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Alphabetical section header
            Container(
              margin: EdgeInsets.only(top: index == 0 ? 16 : 8, bottom: 8),
              child: Text(
                letter,
                style: textStyles.headline4.copyWith(fontSize: 14),
              ),
            ),

            // Contacts in this section
            ...contacts.map((contact) => _buildContactItem(
                  contact,
                  colors,
                  textStyles,
                )),
          ],
        );
      },
    );
  }

  /// Build individual contact item
  Widget _buildContactItem(
    Contact contact,
    AppColors colors,
    TextStyles textStyles,
  ) {
    final isSelected = _selectedContactIds.contains(contact.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _toggleContactSelection(contact),
          child: Container(
            padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 12, 4),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                // Profile picture
                SizedBox(
                  width: 40,
                  height: 40,
                  child: ClipOval(
                    child: ImageContainer(
                      url: contact.profileImage,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
const SizedBox(width: 4),
                // Name
                Expanded(
                  child: Text(
                    contact.name,
                    style: textStyles.headline4.copyWith(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                    
                  ),
                ),

                // Checkbox
                AnimatedContainer(
                  duration: 200.milliseconds,
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color:
                          colors.primary, // Always use primary color for border
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    color: isSelected ? colors.primary : Colors.transparent,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          color: colors.background,
                          size: 16,
                        ).animate().fadeIn()
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Contact model class
class Contact {
  final String id;
  final String name;
  final String profileImage;

  Contact({
    required this.id,
    required this.name,
    required this.profileImage,
  });
}
