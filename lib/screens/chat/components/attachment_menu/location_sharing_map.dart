import 'dart:async';
import 'dart:ui' as ui;

import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/constants/map.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart' hide Theme;
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/map/marker_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class LocationSharingMap extends ConsumerStatefulWidget {
  final double latitude;
  final double longitude;

  const LocationSharingMap({
    super.key,
    required this.latitude,
    required this.longitude,
  });

  @override
  ConsumerState<LocationSharingMap> createState() => _LocationSharingMapState();
}

class _LocationSharingMapState extends ConsumerState<LocationSharingMap> {
  final Completer<GoogleMapController> _controller = Completer();
  final Set<Marker> _markers = {};
  bool _markerCreated = false;
  bool _isMapDark = false;

  late LatLng _officeLatLng;
  late CameraPosition _officeLocation;

  @override
  void initState() {
    super.initState();
    _officeLatLng = LatLng(widget.latitude, widget.longitude);
    _officeLocation = CameraPosition(
      target: _officeLatLng,
      zoom: 14.0,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_markerCreated) {
      _createCustomMarker(context);
      _markerCreated = true;
    }
    _setMapStyle();
  }

  void _setMapStyle() async {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    if (_isMapDark == isDark) return;

    final controller = await _controller.future;
    final style = isDark ? mapNightStyleString : null;
    controller.setMapStyle(style);
    setState(() {
      _isMapDark = isDark;
    });
  }

  Future<void> _createCustomMarker(BuildContext context) async {
    final BitmapDescriptor customMarker =
        await _getCustomMarker('https://i.pravatar.cc/150?img=5');
    if (mounted) {
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('user_location'),
            position: _officeLatLng,
            icon: customMarker,
            anchor: const Offset(0.5, 1),
          ),
        );
      });
    }
  }

  Future<BitmapDescriptor> _getCustomMarker(String photoUrl) async {
    final devicePixelRatio = MediaQuery.devicePixelRatioOf(context);
    const markerRatio = 63 / 51;
    const markerHeight = 63.0;
    const markerWidth = 51.0;
    // Load the image first, then use it in the ClipOval
    final ImageProvider imageProvider = NetworkImage(photoUrl);
    await precacheImage(imageProvider, context);

    return await SizedBox(
            height: markerHeight,
            width: markerWidth,
            child: Stack(
              children: [
                SvgPicture.asset(
                  'assets/icons/map_photo_marker_${ref.watch(themeProvider).name}.svg',
                  height: markerHeight,
                  width: markerWidth,
                ),
                Positioned(
                    top: 8,
                    left: 7,
                    right: 7,
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: SizedBox.square(
                        dimension: 43,
                        child: ClipOval(
                          child: Image(
                            image: imageProvider,
                            height: 43,
                            width: 43,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ))
              ],
            ))
        .toBitmapDescriptor(
            logicalSize: Size(51, 63),
            imageSize: Size(51 * devicePixelRatio, 63 * devicePixelRatio));
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    return SizedBox(
      height: 250,
      child: Stack(
        children: [
          GoogleMap(
            mapType: MapType.normal,
            initialCameraPosition: _officeLocation,
            onMapCreated: (GoogleMapController controller) {
              if (!_controller.isCompleted) {
                _controller.complete(controller);
                _setMapStyle();
              }
            },
            markers: _markers,
            zoomControlsEnabled: false,
            scrollGesturesEnabled: false,
            zoomGesturesEnabled: false,
            tiltGesturesEnabled: false,
            rotateGesturesEnabled: false,
            myLocationButtonEnabled: false,
            myLocationEnabled: true,
          ),
          // Positioned(
          //   right: 16,
          //   bottom: 16,
          //   child: Column(
          //     children: [
          //       Container(
          //         padding: const EdgeInsets.all(5),
          //         margin: const EdgeInsets.only(bottom: 10),
          //         decoration: BoxDecoration(
          //             color: theme.colors.backgroundContainer,
          //             borderRadius: BorderRadius.circular(5)),
          //         child: InkWell(
          //           onTap: () async {
          //             final GoogleMapController controller =
          //                 await _controller.future;
          //             controller.animateCamera(CameraUpdate.zoomIn());
          //           },
          //           child: SvgPicture.asset(
          //             'assets/icons/expand-outline.svg',
          //             height: 24,
          //             width: 24,
          //             color: theme.colors.secondaryText,
          //           ),
          //         ),
          //       ),
          //       Container(
          //         decoration: BoxDecoration(
          //             color: theme.colors.backgroundContainer,
          //             borderRadius: BorderRadius.circular(5)),
          //         child: Column(
          //           mainAxisSize: MainAxisSize.min,
          //           children: <Widget>[
          //             InkWell(
          //               onTap: () async {
          //                 final GoogleMapController controller =
          //                     await _controller.future;
          //                 controller.animateCamera(CameraUpdate.zoomIn());
          //               },
          //               child: Padding(
          //                 padding: const EdgeInsets.fromLTRB(5, 5, 5, 7.5),
          //                 child: Icon(
          //                   Icons.add_rounded,
          //                   size: 24,
          //                   color: theme.colors.secondaryText,
          //                 ),
          //               ),
          //             ),
          //             InkWell(
          //               onTap: () async {
          //                 final GoogleMapController controller =
          //                     await _controller.future;
          //                 controller.animateCamera(CameraUpdate.zoomOut());
          //               },
          //               child: Padding(
          //                 padding: const EdgeInsets.fromLTRB(5, 7.5, 5, 5),
          //                 child: Icon(
          //                   Icons.remove_rounded,
          //                   size: 24,
          //                   color: theme.colors.secondaryText,
          //                 ),
          //               ),
          //             ),
          //           ],
          //         ),
          //       ),
          //     ],
          //   ),
          // )
        ],
      ),
    );
  }

  Widget _buildMapControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      elevation: 4,
      child: InkWell(
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Icon(icon, size: 20, color: Colors.black54),
        ),
      ),
    );
  }
}
