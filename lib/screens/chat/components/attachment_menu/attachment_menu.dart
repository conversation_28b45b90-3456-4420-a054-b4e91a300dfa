import 'package:ako_basma/components/overlay/animated_section.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

/// Attachment menu component
class AttachmentMenu extends StatefulWidget {
  final VoidCallback? onCameraPressed;
  final VoidCallback? onRecordPressed;
  final VoidCallback? onContactPressed;
  final VoidCallback? onGalleryPressed;
  final VoidCallback? onLocationPressed;
  final VoidCallback? onDocumentPressed;
  final Size size;
  final LayerLink layerLink;
  final VoidCallback hideOverlay;
  const AttachmentMenu({
    super.key,
    this.onCameraPressed,
    this.onRecordPressed,
    this.onContactPressed,
    this.onGalleryPressed,
    this.onLocationPressed,
    this.onDocumentPressed,
    required this.size,
    required this.layerLink,
    required this.hideOverlay,
  });

  @override
  State<AttachmentMenu> createState() => _AttachmentMenuState();
}

class _AttachmentMenuState extends State<AttachmentMenu> {
  bool _displaying = true;
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    final overlayOffset = const Offset(-16, 0);
    final child = Stack(
      children: [
        Positioned(
          width: widget.size.width + 32
          // 32 cus we have 16px padding around the field.
          ,
          bottom: 0,
          child: CompositedTransformFollower(
            link: widget.layerLink,
            followerAnchor: Alignment.bottomLeft,
            showWhenUnlinked: false,
            offset: overlayOffset,
            child: AnimatedSection(
              animationDismissed: widget.hideOverlay,
              expand: _displaying,
              axisAlignment: -1.0,
              child: Container(
                margin: EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // First row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        spacing: 24,
                        children: [
                          _buildAttachmentOption(
                            context,
                            SolarIconsBold.cameraMinimalistic,
                            strings.camera,
                            colors.primary,
                            widget.onCameraPressed,
                          ),
                          _buildAttachmentOption(
                            context,
                            SolarIconsBold.microphone,
                            strings.record,
                            colors.primary,
                            widget.onRecordPressed,
                          ),
                          _buildAttachmentOption(
                            context,
                            SolarIconsBold.user,
                            strings.contact,
                            colors.primary,
                            widget.onContactPressed,
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Second row
                      Row(
                        spacing: 24,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildAttachmentOption(
                            context,
                            SolarIconsBold.gallery,
                            strings.gallery,
                            colors.primary,
                            widget.onGalleryPressed,
                          ),
                          _buildAttachmentOption(
                            context,
                            SolarIconsBold.mapPoint,
                            strings.myLocation,
                            colors.primary,
                            widget.onLocationPressed,
                          ),
                          _buildAttachmentOption(
                            context,
                            SolarIconsBold.fileText,
                            strings.document,
                            colors.primary,
                            widget.onDocumentPressed,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );

    return Stack(
      children: [
        GestureDetector(
          onTap: () => setState(() => _displaying = false),
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.transparent,
          ),
        ),
        child,
      ],
    );
  }

  /// Builds individual attachment option with icon and label
  Widget _buildAttachmentOption(
    BuildContext context,
    IconData icon,
    String label,
    Color iconColor,
    VoidCallback? onPressed,
  ) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            widget.hideOverlay();
            onPressed?.call();
          },
          splashColor: colors.primaryVariant,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon container with background
                Container(
                  width: 42,
                  height: 42,
                  decoration: BoxDecoration(
                    color: colors.primaryVariant,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      icon,
                      size: 26,
                      color: iconColor,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Label text
                Text(
                  label,
                  style: textStyles.buttonSmall.copyWith(
                    color: colors.primaryText,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
