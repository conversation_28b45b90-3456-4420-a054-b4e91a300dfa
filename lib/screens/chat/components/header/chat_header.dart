import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/screens/chat/screens/contact_info_popup.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:solar_icons/solar_icons.dart';

/// Chat header component
/// Displays profile image, name, online status, and last seen time
class ChatHeader extends StatelessWidget {
  final String userName;
  final String userImage;
  final String lastSeen;
  final bool isOnline;
  final VoidCallback onBackPressed;
  final bool showClockOutBanner;
  final bool showApprovalActions;
  final bool showEndChat;
  final Future<void> Function()? onApprove;
  final Future<void> Function()? onReject;
  final Future<void> Function()? onEnd;

  const ChatHeader({
    super.key,
    required this.userName,
    required this.userImage,
    required this.lastSeen,
    required this.isOnline,
    required this.onBackPressed,
    this.showClockOutBanner = false,
    this.showApprovalActions = false,
    this.showEndChat = false,
    this.onApprove,
    this.onReject,
    this.onEnd,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;
    // final screenWidth = MediaQuery.sizeOf(context).width;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: [
        InkWell(
          onTap: () => _showContactInfo(context),
          child: Container(
            // width: screenWidth,
            color: colors.backgroundContainer,
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: MediaQuery.of(context).padding.top +
                  8, // Blend with status bar
              bottom: 8,
            ),
            child: Row(
              children: [
                // Back button
                InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: onBackPressed,
                  child: Padding(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      Directionality.of(context) == TextDirection.ltr
                          ? SolarIconsOutline.altArrowLeft
                          : SolarIconsOutline.altArrowRight,
                      size: 32,
                      color: colors.primaryText,
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // User profile section
                Expanded(
                  child: Row(
                    children: [
                      // Profile image with online indicator
                      Stack(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: colors.primaryVariant,
                                border: Border.all(color: colors.tertiaryText)),
                            child: ClipOval(
                              child: Image.asset(
                                userImage,
                                width: 40,
                                height: 40,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // Fallback to initials if image fails to load
                                  return Container(
                                    color: colors.primary.withOpacity(0.1),
                                    child: Center(
                                      child: Text(
                                        userName.isNotEmpty
                                            ? userName[0].toUpperCase()
                                            : 'U',
                                        style: textStyles.headline4.copyWith(
                                          color: colors.primary,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                          // Online status indicator
                          if (isOnline)
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: colors.success,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colors.background,
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(width: 4),
                      // User name and status
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              userName,
                              style: textStyles.button.copyWith(
                                color: colors.secondaryText,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            // Last seen or online status
                            Text(
                              isOnline
                                  ? strings.online
                                  // TODO change this later...
                                  : strings.lastSeenOn(lastSeen),
                              style: textStyles.body3.copyWith(
                                color: isOnline
                                    ? colors.success
                                    : colors.tertiaryText,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        // the clocked out section..
        AnimatedSize(
            alignment: Alignment.topCenter,
            duration: 200.milliseconds,
            child: Column(
              children: [
                if (showClockOutBanner)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        border: Border.all(color: colors.primaryVariant)),
                    child: Row(
                      children: [
                        Icon(
                          SolarIconsOutline.clockCircle,
                          size: 20,
                          color: colors.tertiaryText,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          // TODO localize
                          strings.nameClockedOutTime(userName),
                          style: textStyles.body3
                              .copyWith(color: colors.secondaryText),
                        ),
                      ],
                    ).animate().fadeIn(),
                  ),
                if (showApprovalActions)
                  Container(
                    color: colors.backgroundContainer,
                    padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppOutlinedButton.async(
                            label: strings.reject,
                            tintColor: colors.error,
                            textStyle:
                                textStyles.body3.copyWith(color: colors.error),
                            // onTap: () {},
                            onPressed: onReject,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                            child: AppOutlinedButton.async(
                          label: strings.approve,
                          tintColor: colors.success,
                          textStyle:
                              textStyles.body3.copyWith(color: colors.success),
                          onPressed: onApprove,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        )),
                      ],
                    ),
                  ),
                if (showEndChat)
                  Container(
                    color: colors.backgroundContainer,
                    padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
                    child: Row(
                      children: [
                        Expanded(
                            child: AppOutlinedButton.async(
                          label: strings.endChat,
                          tintColor: colors.error,
                          textStyle:
                              textStyles.body3.copyWith(color: colors.error),
                          onPressed: onEnd,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        )),
                      ],
                    ),
                  ),
              ],
            ))
      ],
    );
  }

  /// Show contact info popup as bottom modal sheet
  void _showContactInfo(BuildContext context) {
    showAdaptivePopup(
      context,
      (ctx, sc) {
        return const ContactInfoPopup();
      },
      useRootNavigator: true,
    );
  }
}
