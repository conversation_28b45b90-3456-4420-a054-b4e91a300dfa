import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

/// AI Chat header component
/// Displays back arrow and "Chat with AI" title only
class AI<PERSON>hatHeader extends StatelessWidget {
  final VoidCallback onBackPressed;

  const AIChatHeader({
    super.key,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final strings = AppLocalizations.of(context)!;
    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsetsDirectional.fromSTEB(
        16, // start
        MediaQuery.of(context).padding.top + 8, // top - blend with status bar
        16, // end
        8, // bottom
      ),
      child: Row(
        children: [
          // Back button
          InkWell(
            splashFactory: NoSplash.splashFactory,
            onTap: onBackPressed,
            child: Padding(
              padding: const EdgeInsetsDirectional.all(4),
              child: Icon(
                Directionality.of(context) == TextDirection.ltr
                    ? SolarIconsOutline.altArrowLeft
                    : SolarIconsOutline.altArrowRight,
                size: 32,
                color: colors.primaryText,
              ),
            ),
          ),

          // Chat with AI title
          Expanded(
            child: Container(
              margin: const EdgeInsetsDirectional.only(start: 12),
              child: Text(
                strings.chatWithAi,
                style: textStyles.textButton.copyWith(
                  fontSize: 18,
                  color: colors.secondaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
