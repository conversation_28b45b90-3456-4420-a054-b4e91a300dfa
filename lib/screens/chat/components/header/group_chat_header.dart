import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

/// Group chat header component
/// Displays group photo, name, member count, and back button
class GroupChatHeader extends StatelessWidget {
  final String groupName;
  final String? groupImage;
  final int memberCount;
  final VoidCallback onBackPressed;

  const GroupChatHeader({
    super.key,
    required this.groupName,
    this.groupImage,
    required this.memberCount,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    return GestureDetector(
      // onTap: () => _showContactInfo(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            // width: screenWidth,
            color: colors.backgroundContainer,
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              top: MediaQuery.of(context).padding.top +
                  8, // Blend with status bar
              bottom: 8,
            ),
            child: Row(
              children: [
                // Back button
                InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: onBackPressed,
                  child: Padding(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      Directionality.of(context) == TextDirection.ltr
                          ? SolarIconsOutline.altArrowLeft
                          : SolarIconsOutline.altArrowRight,
                      size: 32,
                      color: colors.primaryText,
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // User profile section
                Expanded(
                  child: Row(
                    children: [
                      // Profile image with online indicator
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: colors.primaryVariant,
                        ),
                        child: ClipOval(
                          child: ImageContainer(
                            url: groupImage,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // User name and status
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // User name - made clickable with GestureDetector
                            Text(
                              groupName,
                              style: textStyles.button.copyWith(
                                color: colors.secondaryText,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            // Last seen or online status
                            Text(
                              // localization
                              strings.groupMembersCount(memberCount),
                              style: textStyles.body3.copyWith(
                                color: colors.tertiaryText,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // AnimatedSize(
          //   alignment: Alignment.topCenter,
          //   duration: 200.milliseconds,
          //   child: showClockOutBanner
          //       ? Container(
          //           padding: const EdgeInsets.all(8),
          //           decoration: BoxDecoration(
          //               color: colors.backgroundContainer,
          //               border: Border.all(color: colors.primaryVariant)),
          //           child: Row(
          //             children: [
          //               Icon(
          //                 SolarIconsOutline.clockCircle,
          //                 size: 20,
          //                 color: colors.tertiaryText,
          //               ),
          //               const SizedBox(width: 8),
          //               Text(
          //                 'Ethaar Clocked Out One Hour Ago',
          //                 style: textStyles.body3
          //                     .copyWith(color: colors.secondaryText),
          //               ),
          //             ],
          //           ).animate().fadeIn(),
          //         )
          //       : const SizedBox(height: 0, width: 0),
          // )
        ],
      ),
    );

    // return Container(
    //   width: screenWidth,
    //   color: colors.backgroundContainer,
    //   padding: EdgeInsets.only(
    //     left: 28,
    //     right: 16,
    //     top: MediaQuery.of(context).padding.top + 8, // Blend with status bar
    //     bottom: 12,
    //   ),
    //   child: Row(
    //     children: [
    //       // Back button
    //       Material(
    //         color: Colors.transparent,
    //         child: InkWell(
    //           borderRadius: BorderRadius.circular(20),
    //           onTap: onBackPressed,
    //           child: Container(
    //             padding: const EdgeInsets.all(8),
    //             child: Icon(
    //               Icons.arrow_back_ios,
    //               size: 20,
    //               color: colors.primaryText,
    //             ),
    //           ),
    //         ),
    //       ),
    //       const SizedBox(width: 4),
    //       // Group profile section
    //       Expanded(
    //         child: Row(
    //           children: [
    //             // Group image
    //             Container(
    //               width: 40,
    //               height: 40,
    //               decoration: BoxDecoration(
    //                 shape: BoxShape.circle,
    //                 color: colors.primaryVariant,
    //               ),
    //               child: ClipOval(
    //                 child: groupImage != null
    //                     ? ImageContainer(
    //                         url: groupImage,
    //                         width: 40,
    //                         height: 40,
    //                         fit: BoxFit.cover,
    //                       )
    //                     : Container(
    //                         color: colors.primaryVariant,
    //                         child: Center(
    //                           child: Text(
    //                             groupName.isNotEmpty
    //                                 ? groupName[0].toUpperCase()
    //                                 : 'G',
    //                             style: textStyles.headline4.copyWith(
    //                               color: colors.primary,
    //                             ),
    //                             textScaler: TextScaler.noScaling,
    //                           ),
    //                         ),
    //                       ),
    //               ),
    //             ),

    //             // Group name and member count
    //             Expanded(
    //               child: Container(
    //                 margin: const EdgeInsets.only(left: 12),
    //                 child: Column(
    //                   crossAxisAlignment: CrossAxisAlignment.start,
    //                   mainAxisAlignment: MainAxisAlignment.center,
    //                   children: [
    //                     // Group name
    //                     Text(
    //                       groupName,
    //                       style: textStyles.headline4.copyWith(
    //                         color: colors.primaryText,
    //                       ),
    //                       overflow: TextOverflow.ellipsis,
    //                     ),

    //                     // Member count
    //                     Container(
    //                       margin: const EdgeInsets.only(top: 4),
    //                       child: Text(
    //                         '$memberCount Members',
    //                         style: textStyles.body3.copyWith(
    //                           color: colors.tertiaryText,
    //                         ),
    //                       ),
    //                     ),
    //                   ],
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //       ),
    //     ],
    //   ),
    // );
  }
}
