import 'dart:math';
import 'dart:ui';

import 'package:ako_basma/components/text/gradient_mask.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';

class AiChatWelcomeSection extends StatelessWidget {
  const AiChatWelcomeSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    // final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 27.4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Ako Basma Logo
          Image.asset(
            'assets/images/ako_basma_logo.png',
            width: 45,
            height: 45,
            fit: BoxFit.contain,
            // errorBuilder: (context, error, stackTrace) {
            //   // Fallback if logo fails to load
            //   return Container(
            //     width: 80,
            //     height: 80,
            //     decoration: BoxDecoration(
            //       shape: BoxShape.circle,
            //       gradient: colors.primaryGradient,
            //     ),
            //     child: Center(
            //       child: Text(
            //         'AI',
            //         style: textStyles.headline.copyWith(
            //           color: Colors.white,
            //           fontWeight: FontWeight.bold,
            //         ),
            //       ),
            //     ),
            //   );
            // },
          ),
          const SizedBox(height: 10.73),

          // Gradient greeting text
          // Gradient text using Text widget with hardcoded blue to red gradient
          GradientMask(
            gradient: const LinearGradient(
              colors: [
                Color(0xFF0090BC), // Blue
                Color(0xFFD45969),
                Color(0xFF65C9E8), // Red
              ],
            ),
            child: Text(
              strings.aiChatGreeting,
              style: theme.textStyles.headline4.copyWith(
                fontSize: 13.41,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 11.4),
          LayoutBuilder(builder: (context, constraints) {
            final width = min(constraints.maxWidth, 244.0);
            return Container(
              width: width,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.73),
                  color: Color.fromRGBO(109, 109, 109, 0.2)),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.73),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                      sigmaX: 20, sigmaY: 20), // Background blur effect
                  child: Container(
                    padding: const EdgeInsetsDirectional.symmetric(
                        vertical: 10.73, horizontal: 5.36),
                    decoration: BoxDecoration(
                      // Blue gradient background matching Figma design with transparency for blur effect
                      gradient: const LinearGradient(
                        stops: [0.3, 1],
                        colors: [
                          Color.fromRGBO(109, 109, 109,
                              0.1), // Top color - dark gray with transparency
                          Color.fromRGBO(166, 224, 242,
                              0.2), // Bottom color - light blue with transparency
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius: BorderRadius.circular(10.73),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Welcome message
                        Text(
                          strings.aiGreetingText,
                          style: theme.textStyles.button.copyWith(
                            fontSize: 10.73,
                            color: theme.colors
                                .primaryText, // Use primary text color insteads
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 5.36),

                        // AI capabilities list
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            _buildCapabilityText(
                              context,
                              strings.aiCapablityLine1,
                            ),
                            _buildCapabilityText(
                              context,
                              strings.aiCapablityLine2,
                            ),
                            _buildCapabilityText(
                              context,
                              strings.aiCapablityLine3,
                            ),
                            _buildCapabilityText(
                              context,
                              strings.aiCapablityLine4,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
          // const SizedBox(height: 27.47),
        ],
      ),
    );
  }

  Widget _buildCapabilityText(BuildContext context, String text) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Text(
      text,
      style: theme.textStyles.body3.copyWith(
        color: theme.colors.secondaryText,
        fontSize: 8.04,
      ),
      textAlign: TextAlign.center,
    );
  }
}
