import 'package:ako_basma/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:solar_icons/solar_icons.dart';

/// Message bubble component for individual chat messages
/// Different styling for sent vs received messages
class MessageBubble extends StatelessWidget {
  final String message;
  final String time;
  final bool isSentByMe;
  final bool isRead;
  final String profileImage;
  final bool showAvatar;

  const MessageBubble({
    super.key,
    required this.message,
    required this.time,
    required this.isSentByMe,
    required this.isRead,
    required this.profileImage,
    this.showAvatar = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      // margin: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment:
            isSentByMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Message bubble
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth:
                    screenWidth * 0.8, // Limit bubble width to 75% of screen
              ),
              decoration: BoxDecoration(
                gradient: isSentByMe ? DesignColors.primaryGradient : null,
                color: isSentByMe ? null : colors.backgroundContainer,
                borderRadius: BorderRadiusDirectional.only(
                  topStart: const Radius.circular(16),
                  topEnd: const Radius.circular(16),
                  bottomStart: Radius.circular(isSentByMe ? 16 : 0),
                  bottomEnd: Radius.circular(isSentByMe ? 0 : 16),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: isSentByMe
                    ? CrossAxisAlignment.end
                    : CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Message text
                  Text(
                    message,
                    style: textStyles.body.copyWith(
                      color: isSentByMe ? Colors.white : colors.primaryText,
                    ),
                  ),

                  // Time and read status
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Time
                        Text(
                          time,
                          style: textStyles.buttonSmall.copyWith(
                            color: isSentByMe
                                ? Theme.of(context).brightness ==
                                        Brightness.light
                                    ? colors.disabled
                                    : colors.tertiaryText
                                : colors.disabled,
                          ),
                        ),
                        if (isSentByMe) const SizedBox(width: 8),
                        // Read status (double checkmarks for sent messages)
                        if (isSentByMe)
                          Icon(SolarIconsOutline.chatRead,
                              size: 14,
                              color: Theme.of(context).brightness ==
                                      Brightness.light
                                  ? colors.disabled
                                  : colors.tertiaryText
                              // isRead

                              //     ? colors.tertiaryText
                              //     : colors.secondaryText,
                              ),
                        //   'sent' => Icon(
                        //     SolarIconsOutline.unread,
                        //     size: 24,
                        //     color: colors.tertiaryText,
                        //   ),
                        // 'delivered' => Icon(
                        //     SolarIconsOutline.chatRead,
                        //     size: 24,
                        //     color: colors.tertiaryText,
                        //   ),
                        // 'seen' => Icon(
                        //     SolarIconsOutline.chatRead,
                        //     size: 24,
                        //     color: colors.primary,
                        //   ),

                        // 'error' => Icon(
                        //     SolarIconsOutline.dangerCircle,
                        //     size: 24,
                        //     color: colors.warning,
                        //   ),
                        // // TODO: Handle this case.
                        // String() => Icon(
                        //     SolarIconsOutline.unread,
                        //     size: 24,
                        //     color: colors.tertiaryText,
                        //   ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // No spacing needed since we removed avatars
        ],
      ),
    );
  }
}
