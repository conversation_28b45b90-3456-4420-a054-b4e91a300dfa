import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:solar_icons/solar_icons.dart';

class ChatListItem extends StatelessWidget {
  final String name;
  final String message;
  final DateTime time;
  final String profileImage;
  // sent, delivered, seen, unread, error
  final String status;
  final int unreadCount;
  final bool isOnline;
  final bool isTyping;
  final VoidCallback onTap;

  const ChatListItem({
    super.key,
    required this.name,
    required this.message,
    required this.time,
    required this.profileImage,
    this.isOnline = false,
    this.isTyping = false,
    required this.onTap,
    required this.status,
    this.unreadCount = 1,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
          child: Row(
            children: [
              // Profile Image
              Stack(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primaryVariant,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        profileImage,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback to initials if image fails to load
                          // TODO , see this later..
                          return Container(
                            color: colors.primary.withOpacity(0.1),
                            child: Center(
                              child: Text(
                                name.isNotEmpty ? name[0].toUpperCase() : 'U',
                                style: textStyles.headline4.copyWith(
                                  color: colors.primary,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  // Online indicator
                  if (isOnline)
                    Positioned(
                      bottom: 1,
                      right: 1,
                      child: Container(
                        width: 14,
                        height: 14,
                        decoration: BoxDecoration(
                          color: colors.success,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: colors.background,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                ],
              ),

              // Chat content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Name and time row
                      Text(
                        name,
                        style: textStyles.headline4,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(height: 2),
                      // Message
                      Text(
                        isTyping ? strings.typing : message,
                        style: textStyles.body2.copyWith(
                          color: isTyping
                              ? colors.success
                              : status == 'unread'
                                  ? colors.primaryText
                                  : colors.secondaryText,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Name and time row
                  Padding(
                    padding: const EdgeInsetsDirectional.only(end: 5),
                    child: Text(
                      formatTime(time, context, padHours: false),
                      style: textStyles.body3.copyWith(
                        color: const Color(0xffA9ABAD),
                      ),
                    ),
                  ),

                  SizedBox.square(
                    dimension: 24,
                    child: AnimatedSwitcher(
                      duration: 200.milliseconds,
                      child: switch (status) {
                        'sent' => Icon(
                            SolarIconsOutline.unread,
                            size: 24,
                            color: colors.tertiaryText,
                          ),
                        'delivered' => Icon(
                            SolarIconsOutline.chatRead,
                            size: 24,
                            color: colors.tertiaryText,
                          ),
                        'seen' => Icon(
                            SolarIconsOutline.chatRead,
                            size: 24,
                            color: colors.primary,
                          ),
                        'unread' => Align(
                            alignment: AlignmentDirectional.bottomEnd,
                            child: Container(
                              height: 16,
                              width: 16,
                              decoration: ShapeDecoration(
                                  shape: CircleBorder(),
                                  color: Color(0xffD34141)),
                              child: Center(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    unreadCount.toString(),
                                    style: textStyles.headline4.copyWith(
                                      fontSize: 14,
                                      color: Colors.white,
                                    ),
                                    textScaler: TextScaler.noScaling,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        'error' => Icon(
                            SolarIconsOutline.dangerCircle,
                            size: 24,
                            color: colors.warning,
                          ),
                        // TODO: Handle this case.
                        String() => Icon(
                            SolarIconsOutline.unread,
                            size: 24,
                            color: colors.tertiaryText,
                          ),
                      },
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
