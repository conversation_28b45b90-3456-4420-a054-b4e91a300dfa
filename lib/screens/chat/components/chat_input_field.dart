import 'dart:io';
import 'dart:math';

import 'package:ako_basma/components/overlay/overlay_builder.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/chat/components/attachment_menu/contact_selection_modal.dart';
import 'package:ako_basma/screens/chat/components/attachment_menu/location_sharing_modal.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:solar_icons/solar_icons.dart';
import 'attachment_menu/attachment_menu.dart';

/// Chat input field component for sending messages
/// Includes text input, attachment, and voice message buttons
class ChatInputField extends StatefulWidget {
  final Function(String) onSendMessage;
  final VoidCallback onVoicePressed;

  const ChatInputField({
    super.key,
    required this.onSendMessage,
    required this.onVoicePressed,
  });

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends State<ChatInputField> {
  final TextEditingController _messageController = TextEditingController();
  final LayerLink _layerLink = LayerLink();
  final GlobalKey _inputFieldKey = GlobalKey();
  OverlayEntry? _attachmentOverlay;
  bool _hasText = false;
  double _inputFieldWidth = 0;

  @override
  void initState() {
    super.initState();
    _messageController.addListener(() {
      setState(() {
        _hasText = _messageController.text.trim().isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    // _removeAttachmentOverlay();
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty) {
      widget.onSendMessage(message);
      _messageController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    // final screenWidth = MediaQuery.of(context).size.width;

    return OverlayBuilder(
// overlayPortalController:,
      overlay: (size, hideCallback) => AttachmentMenu(
        size: size,
        layerLink: _layerLink,
        hideOverlay: hideCallback,
        onCameraPressed: _handleCameraAttachment,
        onRecordPressed: () {},
        onContactPressed: _handleContactAttachment,
        onGalleryPressed: _handleGalleryAttachments,
        onLocationPressed: _handleLocationAttachment,
        onDocumentPressed: _handleDocumentAttachment,
      ),
      child: (showCallback) => CompositedTransformTarget(
        link: _layerLink,
        child: Container(
          key: _inputFieldKey,
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              InkWell(
                splashFactory: NoSplash.splashFactory,
                onTap: showCallback,
                // onTap: _toggleAttachmentOverlay,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    SolarIconsOutline.paperclip,
                    size: 24,
                    color: colors.tertiaryText,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _messageController,
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                  decoration: InputDecoration(
                    hintText: strings.writeAMessage,
                    hintStyle: textStyles.body2.copyWith(
                      color: colors.tertiaryText,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 2),
                    isDense: true,
                  ),
                  maxLines: 1,
                  minLines: 1,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              InkWell(
                splashFactory: NoSplash.splashFactory,
                onTap: _hasText ? _sendMessage : widget.onVoicePressed,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: _hasText
                      ? Icon(
                          Iconsax.send_1_copy,
                          size: 24,
                          color: colors.primary,
                        )
                      : Icon(
                          Iconsax.microphone_2_copy,
                          size: 24,
                          color: colors.primary,
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleCameraAttachment() async {
    final picker = ImagePicker();
    List<File> pickedFiles = [];

    final XFile? media;
    media = await picker.pickImage(
      source: ImageSource.camera,
      maxHeight: 1024,
      maxWidth: 1024,
      imageQuality: 65,
    );

    if (media != null) {
      pickedFiles.add(File(media.path));
    }
  }

  void _handleGalleryAttachments() async {
    final picker = ImagePicker();
    List<File> pickedFiles = [];

    final res = await picker.pickMultipleMedia(
      // source: ImageSource.camera,

      maxHeight: 1024,
      maxWidth: 1024,
      imageQuality: 65,
    );
    if (res.isNotEmpty) {
      for (final file in res) {
        pickedFiles.add(File(file.path));
      }
    }
  }

  void _handleLocationAttachment() async {
    showAdaptivePopup(context, (ctx, sc) {
      return LocationSharingModal(
          onLocationSelected: (locationType, locationData) {});
    }, useRootNavigator: true);
  }

  void _handleContactAttachment() async {
    showAdaptivePopup(
      context,
      (ctx, sc) {
        return ContactSelectionModal(
          onContactsSelected: (selectedContacts) {
            // Handle selected contacts
            if (selectedContacts.isNotEmpty) {
              print(
                  'Selected contacts: ${selectedContacts.map((c) => c.name).join(', ')}');
              // TODO: Implement contact sharing logic
            }
          },
          sc: sc,
        );
      },
      scrollable: true,
      maxChildSize: 1,
      minChildSize: 0.5,
      useRootNavigator: true,
    );
  }

  void _handleDocumentAttachment() async {
    FileType type = FileType.any;
    List<String>? extensions;
    List<File> pickedFiles = [];
    final result = await FilePicker.platform.pickFiles(
      type: type,
      allowedExtensions: extensions,
      allowMultiple: true,
    );
    if (result != null) {
      pickedFiles
          .addAll(result.paths.where((p) => p != null).map((p) => File(p!)));
    }
  }
}
