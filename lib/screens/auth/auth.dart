import 'package:ako_basma/components/form/password_text_field.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/providers/auth/auth.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';

import '../../components/button/primary_button.dart';
import '../../components/form/simple_text_field.dart';
import '../../constants/assets.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key, this.initialMode});

  final String? initialMode;

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();

  final _identifierController = TextEditingController();
  final _passwordController = TextEditingController();

  final _passFocus = FocusNode();

  bool _rememberSession = false;

  // Regex for email and phone number (Keeping these if needed for validation, but logic is in _determineInputType)
  final _emailRegex = RegExp(
      r'^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+$'); // More standard email regex
  final _phoneRegex = RegExp(r'^\d+$'); // Just digits regex

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _passFocus.addListener(() {
        setState(() {});
      });
    });
  }

  @override
  void dispose() {
    _identifierController.dispose();
    _passwordController.dispose();
    _passFocus.dispose();
    super.dispose();
  }

  // Helper function to determine input type based on content
  String? _determineInputType(String input) {
    if (_phoneRegex.hasMatch(input.trim())) {
      return 'phone';
    }
    // Check for any letter or @ or .
    if (input.contains(RegExp(r'[a-zA-Z@.]'))) {
      return 'email';
    }
    return null; // Unknown type
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    // Determine input type directly in build
    final inputType = _determineInputType(_identifierController.text);

    final form = Column(
      children: [
        Container(
          margin: const EdgeInsets.only(top: 20),
        ),
        Row(
          children: [
            // Container commented out for brevity
            Expanded(
              child: SimpleTextField(
                controller: _identifierController,
                validator: (value) {
                  // Add validation logic based on _determineInputType if needed
                  // For example, if _determineInputType returns 'phone' but it's not a valid phone number format
                  return null; // Placeholder
                },
                onChanged: (_) {
                  // Keep onChanged if needed for button enablement or other live feedback
                  setState(() {}); // Keep setState for button enablement
                },

                // focusNode: _phoneFocus,
                autocorrect: false,
                decoration: InputDecoration(
                    // errorText: 'Invalid',
                    labelText: inputType == 'email'
                        ? strings.emailAddress
                        : inputType == 'phone'
                            ? strings.phoneNumber
                            : strings
                                .phoneNumberOrEmailAddress, // Use result of function
                    prefixIcon: Icon(
                      inputType == 'email'
                          ? SolarIconsOutline.letter
                          : inputType == 'phone'
                              ? SolarIconsOutline.phone
                              : SolarIconsOutline
                                  .phone, // Use result of function, default to phone icon
                      size: 24,
                    )),
                forceLtr: true,
                // keyboardType: inputType == 'email' ? TextInputType.emailAddress : TextInputType.phone, // Dynamic keyboard type commented out
                thick: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        PasswordTextField(
          controller: _passwordController,
          // validator: (value) {
          //   return Validators.phone('$_countryCode$value');
          // },
          label: strings.password,
          focusNode: _passFocus,
          onChanged: (s) {
            setState(() {});
          },
          keyboardType: TextInputType.text,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  _rememberSession = !_rememberSession;
                });
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    SizedBox.square(
                      dimension: 22,
                      child: Checkbox(
                        value: _rememberSession,
                        onChanged: (c) {
                          setState(() {
                            _rememberSession = c ?? false;
                          });
                        },
                        side: BorderSide(color: colors.primary),

                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadiusGeometry.circular(4)),

                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        // style: ButtonStyle(
                        //     padding: WidgetStatePropertyAll(EdgeInsets.all(1))),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      strings.rememberMe,
                      style: textStyles.body3.copyWith(color: colors.primary),
                    ),
                  ],
                ),
              ),
            ),
            Flexible(
              child: GestureDetector(
                onTap: () {
                  context.go('/login/forgot');
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Text(
                    strings.forgotYourPassword,
                    style:
                        textStyles.body2.copyWith(color: colors.secondaryText),
                  ),
                ),
              ),
            )
          ],
        )
      ],
    );

    return Scaffold(
      backgroundColor: colors.background,
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 80),
              child: Column(
                children: [
                  // ResizeImage(imageProvider)
                  Image.asset(
                    Assets.logoHighRes,
                    height: 100,
                    width: 100,
                    cacheHeight: 100.cacheSize(context) + 100,
                    cacheWidth: 100.cacheSize(context) + 100,
                  ),
                  // const SizedBox(height: 14),
                  Text(
                    strings.akoBasma,
                    style: textStyles.headline.copyWith(
                      color: const Color(0xFF066A96),
                      // fontWeight: FontWeight.bold,
                      fontSize: 36,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 60),
            Form(
              key: _formKey,
              child: form,
            ),
            const SizedBox(height: 32),
            PrimaryButton.async(
                // to localize
                label: strings.logIn,
                expand: true,
                padding: const EdgeInsetsDirectional.symmetric(
                    vertical: 8, horizontal: 24),
                onPressed: _identifierController.text.trim().isNotEmpty
                    ? () async {
                        await _handleSubmit();
                        // await Future.delayed(10.seconds);
                      }
                    : null),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    // await Future.delayed(Duration(seconds: 1));
    final identifier = _identifierController.text.trim();
    final pass = _passwordController.text.trim();
// after validation.. this logic is ok.

    final bool isEmail = identifier.contains('@');
    await ref.read(authStateProvider.notifier).loginWithCredentials(
          email: isEmail ? identifier : null,
          phone: !isEmail ? identifier : null,
          password: pass,
          onLoginVerification: null,
          storeToken: _rememberSession,
        );
  }
}
