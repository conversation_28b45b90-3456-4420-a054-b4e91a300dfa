import 'dart:math';

import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/form/password_text_field.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/constants/assets.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/auth/otp_inputs.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:solar_icons/solar_icons.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  // states are 1.'phone', 2.'otp', 3.'password'
  String _state = 'phone';

  String _countryCode = '+964';
  final _phoneController = TextEditingController();
  final _otpController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Add FocusNodes for the password fields
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Initialize FocusNodes
    _passwordFocusNode.addListener(() {
      setState(() {});
    });

    _confirmPasswordFocusNode.addListener(() {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    return Scaffold(
      appBar: MyAppbar(title: strings.forgotYourPassword),
      body: Column(
        children: [
          Expanded(
              child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                if (['phone', 'password'].contains(_state)) _buildLogo(),
                _state == 'phone'
                    ? IntrinsicHeight(
                        child: Row(
                          textDirection: TextDirection.ltr,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: colors.strokeColor,
                                ),
                                borderRadius: BorderRadius.circular(8),
                                color: colors
                                    .backgroundContainer, // Added background color
                              ),
                              child: InkWell(
                                onTap: () {
                                  // Country picker code here
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        // TODO change later
                                        _countryCode,
                                        style: textStyles.body2.copyWith(
                                            color: colors.tertiaryText),
                                      ),
                                      Container(
                                        width: 5,
                                      ),
                                      Icon(
                                        HugeIcons.strokeRoundedArrowDown01,
                                        size: 22,
                                        color: colors.primary,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Directionality(
                                textDirection: Directionality.of(context),
                                child: SimpleTextField(
                                  controller: _phoneController,
                                  validator: (value) {
                                    // return Validators.phone('$_countryCode$value');
                                  },
                                  onChanged: (_) {
                                    setState(() {});
                                  },
                                  // focusNode: _phoneFocus,
                                  autocorrect: false,
                                  decoration: InputDecoration(
                                    labelText: strings.phoneNumber,
                                    // prefixIcon: Icon(
                                    //   SolarIconsOutline.phone,
                                    //   size: 24,
                                    // ),
                                  ),
                                  keyboardType: TextInputType.phone,
                                  thick: true,
                                  forceLtr: true,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    // step 2, otp verification
                    : _state == 'otp'
                        ? Column(
                            children: [
                              Container(
                                padding:
                                    const EdgeInsets.only(top: 40, bottom: 8),
                                child: Text(
                                  strings.checkYourMessages,
                                  style: textStyles.button
                                      .copyWith(color: colors.primaryText),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                child: Text(
                                  strings.verificationCode,
                                  style: textStyles.body2.copyWith(
                                    color: colors.secondaryText,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Text(
                                embedLtr(
                                    '$_countryCode${_phoneController.text.trim()}'),
                                style: textStyles.body2.copyWith(
                                  color: colors.secondaryText,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              Padding(
                                padding: const EdgeInsetsGeometry.fromLTRB(
                                    8, 32, 8, 12),
                                // width: 50 * 6,
                                child: Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: OtpInputs(
                                    controller: _otpController,
                                    onChanged: (v) {
                                      setState(() {});
                                    },
                                  ),
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  // Implement resend functionality
                                },
                                child: Text(
                                  strings.resendCode,
                                  style: textStyles.textButton.copyWith(
                                    color: colors.primary,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Column(
                            children: [
                              PasswordTextField(
                                controller: _passwordController,
                                focusNode:
                                    _passwordFocusNode, // Assign the new password focus node
                                // validator: (value) {
                                //   return Validators.phone('$_countryCode$value');
                                // },
                                label: strings.newPassword,
                                onChanged: (s) {
                                  setState(() {});
                                },
                              ),
                              const SizedBox(height: 16),
                              PasswordTextField(
                                controller: _confirmPasswordController,
                                focusNode:
                                    _confirmPasswordFocusNode, // Assign the confirm password focus node
                                // validator: (value) {
                                //   return Validators.phone('$_countryCode$value');
                                // },
                                label: strings.confirmPassword,
                                onChanged: (s) {
                                  setState(() {});
                                },
                              ),
                              const SizedBox(height: 8),
                            ],
                          ),
                // const SizedBox(height: 32),
              ],
            ),
          )),
          Padding(
            padding: EdgeInsets.fromLTRB(
                16,
                8,
                16,
                MediaQuery.viewInsetsOf(context).bottom > 0
                    ? 16
                    : max(MediaQuery.paddingOf(context).bottom, 24)),
            child: PrimaryButton.async(
                label: strings.next,
                expand: true,
                padding: const EdgeInsetsDirectional.symmetric(
                    vertical: 8, horizontal: 24),
                onPressed: _state == 'phone'
                    ? _phoneController.text.trim().isNotEmpty
                        ? _handleSubmitPhone
                        : null
                    : _state == 'otp'
                        ? _otpController.text.trim().length == 6
                            ? _handleSubmitOtp
                            : null
                        : _state == 'password'
                            ? _passwordController.text.trim().isNotEmpty &&
                                    _confirmPasswordController.text
                                        .trim()
                                        .isNotEmpty
                                ? _handleSubmitPassword
                                : null
                            : null),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSubmitPhone() async {
    await Future.delayed(Duration(milliseconds: 1000));
    setState(() {
      _state = 'otp';
    });
  }

  Future<void> _handleSubmitOtp() async {
    await Future.delayed(Duration(milliseconds: 1000));
    setState(() {
      _state = 'password';
    });
  }

  Future<void> _handleSubmitPassword() async {
    await Future.delayed(Duration(milliseconds: 1000));
  }

  Widget _buildLogo() {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final strings = AppLocalizations.of(context)!;
    final textStyles = theme.extension<TextStyles>()!;
    return Padding(
      padding: const EdgeInsets.only(top: 30, bottom: 50),
      child: Column(
        children: [
          Image.asset(
             Assets.logoHighRes,
                    height: 100,
                    width: 100,
                    cacheHeight: 100.cacheSize(context) + 100,
                    cacheWidth: 100.cacheSize(context) + 100,
          ),
          Text(
            strings.akoBasma,
            style: textStyles.headline.copyWith(
              color: const Color(0xFF066A96),
              // fontWeight: FontWeight.bold,
              fontSize: 36,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    // Dispose of the FocusNodes
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }
}
