import 'dart:math';

import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/filter_button.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_detail_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_expense_request_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_shift_change_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_timeoff_card.dart';
import 'package:ako_basma/screens/employees/components/filters/employee_filters_sheet.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:ako_basma/screens/employees/components/map/employee_location_map.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeesScreen extends StatefulWidget {
  const EmployeesScreen({super.key, this.initialTab});

  final String? initialTab;

  @override
  State<EmployeesScreen> createState() => _EmployeesScreenState();
}

class _EmployeesScreenState extends State<EmployeesScreen> {
  String _selectedTab = 'manage';
  final _searchController = TextEditingController();
  int _filterCount = 0;
  Map<String, Set<String>> _selectedFilters = {};

  @override
  void initState() {
    if (widget.initialTab != null) {
      _selectedTab = widget.initialTab!;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
        appBar: PreferredSize(
            preferredSize: const Size.fromHeight(kToolbarHeight),
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              strings.employees,
                              style: theme.textStyles.headline4
                                  .copyWith(color: theme.colors.primaryText),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              strings.manageYourEmployees,
                              style: theme.textStyles.body3
                                  .copyWith(color: theme.colors.tertiaryText),
                            ),
                          ],
                        ),
                      ),
                    ),

                    ActionIconButton(
                      icon: SolarIconsOutline.archive,
                      onPressed: () {
                        context.go('/employees/archive');
                      },
                    ),
                    const SizedBox(width: 8),
                    ActionIconButton(
                      icon: Iconsax.export_copy,
                      onPressed: () {},
                    ),
                    const SizedBox(width: 8),
                    ActionIconButton(
                      icon: Iconsax.add_copy,
                      iconColor: theme.colors.primary,
                      onPressed: () {
                        context.push('/employees/add', extra: () {
                          showAppSnackbar(context,
                              title: strings.employeeAddedSuccessfully,
                              type: 'success');
                        });
                      },
                    ),
                    // Container()
                  ],
                ),
              ),
            )),
        floatingActionButton: Transform.translate(
          offset: Offset(
            Directionality.of(context) == TextDirection.rtl ? 7.5 : -7.5,
            0,
          ),
          child: Transform.rotate(
            angle: -pi / 4,
            child: InkWell(
              splashFactory: NoSplash.splashFactory,
              onTap: () {
                context.push('/home/<USER>');
              },
              borderRadius: BorderRadius.circular(4),
              child: Container(
                  width: 40,
                  height: 40,
                  padding: const EdgeInsets.all(4),
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    gradient: DesignColors.primaryGradient,
                    /* Brand-Primary */
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4)),
                  ),
                  child: Transform.rotate(
                    angle: pi / 4,
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        strings.ai,
                        style: theme.textStyles.headline.copyWith(
                          fontSize: 20,
                          color: Colors.white,
                        ),
                        textScaler: TextScaler.noScaling,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )),
            ),
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ChipSelector(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 8, 8),
              items: [
                ChipItem(
                  label: strings.manageEmployees,
                  icon: Iconsax.user_edit_copy,
                  tag: 'manage',
                ),
                ChipItem(
                  label: strings.employeeTracking,
                  icon: Iconsax.map_copy,
                  tag: 'track',
                ),
                ChipItem(
                  label: strings.shiftEditRequests,
                  icon: Iconsax.user_edit_copy,
                  tag: 'shift_request',
                ),
                ChipItem(
                  label: strings.timeOffRequests,
                  icon: Iconsax.user_remove_copy,
                  tag: 'timeoff_request',
                ),
                ChipItem(
                  label: strings.expenseRequests,
                  icon: Iconsax.coin_copy,
                  tag: 'expense_request',
                ),
              ],
              onItemTap: (tag) {
                setState(() {
                  _selectedTab = tag;
                });
              },
              selectedItems: [_selectedTab],
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Expanded(
                        child: SearchTextField(
                      controller: _searchController,
                      onChanged: (v) {
                        setState(() {});
                      },
                    )),
                    const SizedBox(width: 8),
                    FilterButton(
                      filterCount: _filterCount,
                      onPress: () {
                        showAdaptivePopup(
                          context,
                          (ctx, sc) {
                            return EmployeeFiltersSheet(
                              sc: sc,
                              initialFilters: _selectedFilters,
                              onFilterUpdate: (p0) {
                                setState(() {
                                  _selectedFilters = p0;
                                  _filterCount =
                                      _selectedFilters.values.fold<int>(
                                    0,
                                    (sum, filterSet) => sum + filterSet.length,
                                  );
                                });
                              },
                            );
                          },
                          useRootNavigator: true,
                          scrollable: true,
                          initialChildSize: 0.6,
                          minChildSize: 0.6,
                        );
                      },
                      onClear: () {
                        setState(() {
                          _selectedFilters = {};
                          _filterCount = 0;
                        });
                      },
                    )
                  ],
                ),
              ),
            ),

            Expanded(
              child: _selectedTab == 'manage'
                  ?
                  // Manage tab content
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.fromLTRB(16, 8, 16, 4),
                          child: Text(
                            '${strings.employees}: 32',
                            style: theme.textStyles.body3
                                .copyWith(color: theme.colors.tertiaryText),
                          ),
                        ),
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.fromLTRB(16, 4, 16, 16),
                            itemBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: GestureDetector(
                                  onTap: () {
                                    context.push('/employees/employee',
                                        extra: dummyEmployees[
                                            index % dummyEmployees.length]);
                                  },
                                  child: EmployeeDetailCard(
                                    data: dummyEmployees[
                                        index % dummyEmployees.length],
                                  ),
                                ),
                              );
                            },
                            itemCount: 10,
                          ),
                        ),
                      ],
                    )
                  : _selectedTab == 'track'
                      ?
                      // Requests tab content
                      Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Expanded(
                                child: Container(
                              clipBehavior: Clip.hardEdge,
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: theme.colors.strokeColor,
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              margin: EdgeInsets.fromLTRB(16, 8, 16, 16),
                              child: Hero(
                                tag: 'employee-map',
                                child: EmployeeLocationMap(
                                    type: 'track',
                                    onMarkerTap: (employee) {
                                      context.go('/employees/activity',
                                          extra: employee);
                                    },
                                    employees: [
                                      Employee(
                                        id: 'EMP001',
                                        name: 'Rajesh Kumar',
                                        photoUrl:
                                            'https://i.pravatar.cc/150?img=1',
                                        location: LatLng(28.6139,
                                            77.2090), // Connaught Place
                                      ),
                                      Employee(
                                        id: 'EMP002',
                                        name: 'Priya Sharma',
                                        photoUrl:
                                            'https://i.pravatar.cc/150?img=2',
                                        location:
                                            LatLng(28.5355, 77.3910), // Gurgaon
                                      ),
                                      Employee(
                                        id: 'EMP003',
                                        name: 'Amit Patel',
                                        photoUrl:
                                            'https://i.pravatar.cc/150?img=13',
                                        location: LatLng(
                                            28.7041, 77.1025), // North Delhi
                                      ),
                                      Employee(
                                        id: 'EMP004',
                                        name: 'Neha Gupta',
                                        photoUrl:
                                            'https://i.pravatar.cc/150?img=4',
                                        location: LatLng(
                                            28.4595, 77.0266), // South Delhi
                                      ),
                                    ]),
                              ),
                            ))
                          ],
                        )
                      : _selectedTab == 'shift_request'
                          ? ListView.builder(
                              padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: EmployeeShiftChangeCard(
                                    data: dummyEmployees[
                                        index % dummyEmployees.length],
                                    shiftDate: DateTime.now(),
                                    currentShiftStart: DateTime.now()
                                        .add(const Duration(hours: 9)),
                                    currentShiftEnd: DateTime.now()
                                        .add(const Duration(hours: 17)),
                                    requestedShiftStart: DateTime.now()
                                        .add(const Duration(hours: 10)),
                                    requestedShiftEnd: DateTime.now()
                                        .add(const Duration(hours: 18)),
                                    location: 'Main Office - Floor 3',
                                    onApprove: () {},
                                    onReject: () {},
                                    statusType: index % 3 == 0
                                        ? 'pending'
                                        : 'confirmed',
                                  ),
                                );
                              },
                              itemCount: 10,
                            )
                          : _selectedTab == 'timeoff_request'
                              ?
                              // Schedule tab content
                              ListView.builder(
                                  padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
                                  itemBuilder: (context, index) {
                                    return Padding(
                                      padding: const EdgeInsets.only(bottom: 8),
                                      child: EmployeeTimeoffCard(
                                        data: dummyEmployees[
                                            index % dummyEmployees.length],
                                        leaveStart: DateTime.now()
                                            .add(const Duration(hours: 9)),
                                        leaveEnd: DateTime.now()
                                            .add(const Duration(hours: 17)),
                                        onApprove: () {},
                                        onReject: () {},
                                        statusType: index % 3 == 0
                                            ? 'pending'
                                            : 'confirmed',
                                      ),
                                    );
                                  },
                                  itemCount: 10,
                                )
                              : _selectedTab == 'expense_request'
                                  ?
                                  // Schedule tab content
                                  ListView.builder(
                                      padding:
                                          EdgeInsets.fromLTRB(16, 0, 16, 16),
                                      itemBuilder: (context, index) {
                                        return Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 8),
                                          child: EmployeeExpenseRequestCard(
                                            data: dummyEmployees[
                                                index % dummyEmployees.length],
                                            date: DateTime.now()
                                                .add(const Duration(hours: 9)),
                                            amount: 15000,
                                            onApprove: () {},
                                            onReject: () {},
                                            statusType: index % 3 == 0
                                                ? 'pending'
                                                : 'confirmed',
                                          ),
                                        );
                                      },
                                      itemCount: 10,
                                    )
                                  : Container(),
            ) // Default empty container
          ],
        ));
  }
}
