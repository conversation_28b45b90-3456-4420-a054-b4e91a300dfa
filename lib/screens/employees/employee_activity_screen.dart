import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/screens/employees/components/map/employee_location_map.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class EmployeeActivityScreen extends StatefulWidget {
  const EmployeeActivityScreen({
    super.key,
    required this.data,
  });

  final Employee? data;

  @override
  State<EmployeeActivityScreen> createState() => _EmployeeActivityScreenState();
}

class _EmployeeActivityScreenState extends State<EmployeeActivityScreen> {
  DateTime? _selectedDate = null;
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: MyAppbar(
        title: widget.data?.name ?? strings.error,
        actions: [
          AnimatedSize(
            duration: 300.milliseconds,
            child: _selectedDate != null
                ? Container(
                    // padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.67),
                      border:
                          Border.all(width: 0.83, color: theme.colors.primary),
                    ),
                    child: Row(
                      children: [
                        Theme(
                          data: ThemeData(useMaterial3: false),
                          child: InkWell(
                            onTap: _handleFilterPress,
                            splashFactory: NoSplash.splashFactory,
                            child: Padding(
                              padding: const EdgeInsetsDirectional.fromSTEB(
                                  10, 8, 4, 8),
                              child: Icon(
                                Iconsax.filter_search_copy,
                                size: 20,
                                color: theme.colors.primary,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () async {
                            setState(() {
                              _selectedDate = null;
                            });
                          },
                          splashFactory: NoSplash.splashFactory,
                          child: Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                4, 8, 10, 8),
                            child: Icon(
                              Icons.close_rounded,
                              size: 16,
                              color: theme.colors.secondaryText,
                            ),
                          ),
                        ),
                      ],
                    ))
                : ActionIconButton(
                    icon: Iconsax.filter_search_copy,
                    onPressed: _handleFilterPress,
                    // iconSize: 20,
                    backgroundColor: Colors.transparent,
                    borderColor: theme.colors.primaryVariant,
                  ),
          ),
        ],
      ),
      body: SlidingUpPanel(
        body: Hero(
          tag: 'employee-map',
          child: EmployeeLocationMap(
            type: 'activity',
            padding: const EdgeInsets.only(bottom: 170),
            employeeActivity: {
              '2024-03-20T09:00:00': Employee(
                id: 'EMP001',
                name: 'Rajesh Kumar',
                photoUrl: 'https://i.pravatar.cc/150?img=1',
                location: LatLng(28.5355, 77.3910), // Connaught Place
              ),
              '2024-03-20T10:00:00': Employee(
                id: 'EMP001',
                name: 'Rajesh Kumar',
                photoUrl:'https://i.pravatar.cc/150?img=1',
                location: LatLng(28.6139, 77.2090),
                // Gurgaon
              ),
              '2024-03-20T11:00:00': Employee(
                id: 'EMP001',
                name: 'Rajesh Kumar',
                photoUrl: 'https://i.pravatar.cc/150?img=1',
                location: LatLng(28.7041, 77.1025), // North Delhi
              ),
              '2024-03-20T12:00:00': Employee(
                id: 'EMP001',
                name: 'Rajesh Kumar',
                photoUrl: 'https://i.pravatar.cc/150?img=1',
                location: LatLng(28.4595, 77.0266), // South Delhi
              ),
            },
          ),
        ),
        panelBuilder: (sc) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Align(
                alignment: Alignment.center,
                child: Padding(
                  padding: const EdgeInsetsGeometry.symmetric(vertical: 8),
                  child: Container(
                    height: 5,
                    width: 136,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.5),
                      color: theme.colors.strokeColor,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                child: Text(
                  strings.activity,
                  style: theme.textStyles.body2
                      .copyWith(color: theme.colors.secondaryText),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemBuilder: (context, index) {
                    return _buildActivityTile(index == 0);
                  },
                  controller: sc,
                ),
              ),
            ],
          );
        },
        boxShadow: [],
        color: theme.colors.backgroundContainer,
        border: Border.all(color: theme.colors.strokeColor),
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        minHeight: 70,
      ),
    );
  }

  Widget _buildActivityTile(bool selected) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: selected ? theme.colors.primaryVariant : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        selected: selected,
        title: Text(
          strings.restaurant,
          style: theme.textStyles.textButton
              .copyWith(color: theme.colors.primaryText),
        ),
        subtitle: Text(
          'Baghdad, Yarmouk, Four Streets',
          style: theme.textStyles.body3
              .copyWith(color: theme.colors.secondaryText),
        ),
        trailing: Text(
          // TODO look at this...
          formatLongDateTime(DateTime.now(), context),
          // '${} ${} ${formatDate(DateTime.now(), context, format: 'yyyy')} ${}',
// textDirection: TextDi,
          // formatDate(DateTime.now(), context, format: 'd MMMM yyyy hh:mm a'),
          style: theme.textStyles.body3
              .copyWith(color: theme.colors.secondaryText, fontSize: 10),
        ),
        visualDensity: VisualDensity.compact,
        dense: true,
      ),
    );
  }

  Future<void> _handleFilterPress() async {
    final result = await showDatePickerDialog(
      context: context,
      minDate: DateTime.utc(2024),
      maxDate: DateTime.now(),
      selectedDate: _selectedDate,
      initialDate: DateTime.now(),
    );
    if (result != null) {
      setState(() {
        _selectedDate = result;
      });
    }
  }
}
