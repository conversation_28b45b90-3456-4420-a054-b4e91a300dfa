import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/attachment/attachment_tile.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_info_header.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/components/form/attachment/attachment_grid_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class EmployeeResignationReqScreen extends StatelessWidget {
  const EmployeeResignationReqScreen({
    super.key,
    required this.data,
  });
  final EmployeeData? data;
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: MyAppbar(title: strings.resignationRequest),
      body: data == null
          ? Center(
              child: Text(strings.sorrySomeErrorOccured),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.only(top: 16, bottom: 24),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        Expanded(
                          child: AppOutlinedButton.async(
                            label: strings.reject,
                            tintColor: theme.colors.error,
                            textStyle: theme.textStyles.body3
                                .copyWith(color: theme.colors.error),
                            // onTap: () {},
                            onPressed: () async {
                              await Future.delayed(1.seconds);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                            child: AppOutlinedButton(
                          label: strings.approve,
                          tintColor: theme.colors.success,
                          textStyle: theme.textStyles.body3
                              .copyWith(color: theme.colors.success),
                          onTap: () {},
                        )),
                      ],
                    ),
                  ),
                  EmployeeInfoHeader(data: data!),
                  const SizedBox(height: 16),
                  SectionHeading(
                    title: strings.resignation,
                    titleStyle: theme.textStyles.headline3,
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          'Better Career Opportunity',
                          style: theme.textStyles.headline3.copyWith(
                            color: theme.colors.secondaryText,
                          ),
                        ),
                        const SizedBox(height: 5),
                        Text(
                          'I have received an offer that aligns more closely with my long-term career goals and offers greater opportunities for growth and skill development',
                          style: theme.textStyles.body.copyWith(
                            color: theme.colors.secondaryText,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SectionHeading(
                    title: strings.additionalNotes,
                    titleStyle: theme.textStyles.headline3,
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          'The employee expressed gratitude for the learning experience and positive work environment but feels the new role offers more strategic responsibilities',
                          style: theme.textStyles.body.copyWith(
                            color: theme.colors.secondaryText,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SectionHeading(
                    title: strings.lastWorkingDay,
                    titleStyle: theme.textStyles.headline3,
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    decoration: BoxDecoration(
                      color: theme.colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          formatDateDmy(DateTime.now(), context),
                          style: theme.textStyles.body.copyWith(
                            color: theme.colors.secondaryText,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SectionHeading(
                    title: strings.documents,
                    titleStyle: theme.textStyles.headline3,
                  ),
                  SizedBox(
                    height: 167 + 16,
                    child: ListView.separated(
                        padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          return SizedBox(
                            width: 135,
                            child: AttachmentPlaceholderCard(
                              url:
                                  'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
                              // previewHeight: 114,
                              previewWidth: 100,
                            ),
                          );
                        },
                        separatorBuilder: (__, _) => const SizedBox(width: 8),
                        itemCount: 2),
                  ),
                ],
              ),
            ),
    );
  }
}
