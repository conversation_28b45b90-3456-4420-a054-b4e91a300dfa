import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/filter_button.dart';
import 'package:ako_basma/components/form/search_text_field.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_detail_card.dart';
import 'package:ako_basma/screens/employees/components/filters/employee_filters_sheet.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';

class EmployeeArchiveScreen extends StatefulWidget {
  const EmployeeArchiveScreen({super.key});

  @override
  State<EmployeeArchiveScreen> createState() => _EmployeeArchiveScreenState();
}

class _EmployeeArchiveScreenState extends State<EmployeeArchiveScreen> {
  final TextEditingController _searchController = TextEditingController();
  int _filterCount = 0; // State for filter count
  Map<String, Set<String>> _selectedFilters = {};

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: MyAppbar(
        title: strings.archives,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Expanded(
                    child: SearchTextField(
                      controller: _searchController,
                      onChanged: (v) {
                        setState(
                            () {}); // Trigger rebuild on search input change
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  FilterButton(
                    filterCount: _filterCount,
                    onPress: () {
                      showAdaptivePopup(
                        context,
                        (ctx, sc) {
                          return EmployeeFiltersSheet(
                            sc: sc,
                            initialFilters: _selectedFilters,
                            onFilterUpdate: (p0) {
                              setState(() {
                                _selectedFilters = p0;
                                _filterCount =
                                    _selectedFilters.values.fold<int>(
                                  0,
                                  (sum, filterSet) => sum + filterSet.length,
                                );
                              });
                            },
                          );
                        },
                        useRootNavigator: true,
                        scrollable: true,
                        initialChildSize: 0.6,
                        minChildSize: 0.6,
                      );
                    },
                    onClear: () {
                      setState(() {
                        _selectedFilters = {};
                        _filterCount = 0;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.fromLTRB(16, 4, 16, 16),
              itemCount: 10, // Dummy item count
              itemBuilder: (context, index) {
                // Dummy EmployeeDetailCard
                final data = dummyEmployees[index % dummyEmployees.length];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: InkWell(
                    onTap: () {
                      context.push('/employees/employee', extra: data);
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: EmployeeDetailCard(
                      data: data,
                      showDates: true,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
