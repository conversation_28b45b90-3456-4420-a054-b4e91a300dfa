import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/attachment/attachment_tile.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/text/gradient_mask.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/popups/common/icon_info_popup.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_brief_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_info_header.dart';
import 'package:ako_basma/screens/employees/components/forms/employee_feedback_form.dart';
import 'package:ako_basma/screens/employees/components/forms/employee_promotion_form.dart';
import 'package:ako_basma/screens/employees/components/forms/employee_rating_form.dart';
import 'package:ako_basma/screens/employees/components/popups/employee_edit_address.dart';
import 'package:ako_basma/screens/employees/components/popups/employee_edit_salary.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeeProfileScreen extends StatefulWidget {
  const EmployeeProfileScreen({
    super.key,
    required this.data,
  });
  final EmployeeData? data;
  @override
  State<EmployeeProfileScreen> createState() => _EmployeeProfileScreenState();
}

class _EmployeeProfileScreenState extends State<EmployeeProfileScreen> {
  final _contextMenuButtonKey = GlobalKey();
  final _statsDurationDropdownButtonKey = GlobalKey();

  String _selectedTab = 'shift';
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: MyAppbar(
        title: widget.data?.name ?? "",
        actions: [
          ActionIconButton(
            key: _contextMenuButtonKey,
            icon: SolarIconsOutline.menuDots,
            onPressed: () {
              showLocalContextMenu(
                  context: context,
                  buttonKey: _contextMenuButtonKey,
                  items: [
                    {
                      'icon': Iconsax.edit_2_copy,
                      'label': strings.editName,
                      'onPressed': () {},
                    },
                    {
                      'icon': SolarIconsOutline.chatRoundUnread,
                      'label': strings.performance,
                      'onPressed': () {
                        showAppDialog(context, (context, sc) {
                          return EmployeeRatingForm(onDismiss: () {
                            if (context.mounted) {
                              context.pop();
                            }
                          });
                        }, useRootNavigator: true);
                      },
                    },
                    {
                      'icon': SolarIconsOutline.archive,
                      'label': strings.archive,
                      'onPressed': () {
                        showAppDialog(context, (context, sc) {
                          return IconInfoPopup(
                            schemeColor: theme.colors.warning,
                            iconData: SolarIconsOutline.archive,
                            iconBgColor: theme.colors.warningContainer,
                            title: strings.archiveEmployee,
                            description: strings.archiveEmployeeWarning,
                            footerText: strings.archiveEmployeeWarningSubtitle,
                            onCancelPressed: () {
                              context.pop();
                            },
                            onSubmitPressedAsync: () async {},
                          );
                        }, useRootNavigator: true);
                      },
                    },
                    {
                      'icon': SolarIconsOutline.handHeart,
                      'label': strings.promotion,
                      'onPressed': () {
                        showAppDialog(context, (context, sc) {
                          return EmployeePromotionForm(onDismiss: () {
                            if (context.mounted) {
                              context.pop();
                            }
                          });
                        }, useRootNavigator: true);
                      },
                    },
                    {
                      'icon': SolarIconsOutline.trashBinMinimalistic_2,
                      'label': strings.deleteEmployee,
                      'color': theme.colors.error,
                      'iconColor': theme.colors.error,
                      'onPressed': () {
                        showAppDialog(context, (context, sc) {
                          return IconInfoPopup(
                            schemeColor: theme.colors.secondary,
                            iconData: SolarIconsOutline.trashBinMinimalistic_2,
                            iconColor: theme.colors.error,
                            iconBgColor: theme.colors.errorContainer,
                            title: strings.deleteEmployee,
                            description: strings.deleteEmployeeWarning,
                            footerText: strings.proceedConfirmation,
                            onCancelPressed: () {
                              context.pop();
                            },
                            onSubmitPressedAsync: () async {},
                          );
                        }, useRootNavigator: true);
                      },
                    },
                  ],
                  // maxWidth: 225,

                  positionShift: (offset, size) =>
                      offset.translate(0, size.height + 8),
                  showDivider: true);
            },
          )
        ],
      ),
      body: widget.data == null
          ? Center(
              child: Text(strings.sorrySomeErrorOccured),
            )
          : ListView(
              children: [
                EmployeeInfoHeader(
                  data: widget.data!,
                  items: [
                    const SizedBox(height: 8),
                    AppOutlinedButton(
                      label: strings.editInfo,
                      onTap: () {
                        context.push('/employees/edit', extra: {
                          'data': widget.data,
                          'onSuccess': () {
                            if (context.mounted) {
                              // context.pop();
                            }
                          },
                          // 'tab': 'details',
                        });
                      },
                      outlineColor: theme.colors.strokeColor,
                    ),
                    const SizedBox(height: 8),
                    AppOutlinedButton(
                      label: strings.viewActivity,
                      onTap: () {
                        context.push(
                          '/employees/activity',
                          extra: Employee(
                            id: '',
                            // id: widget.data?.id ?? "",
                            name: widget.data?.name ?? "",
                            photoUrl: widget.data?.imageUrl ?? "",
                            location: LatLng(0, 0),
                            // location:
                            //     widget.data?.location ?? const LatLng(0, 0),
                          ),
                        );
                      },
                      tintColor: theme.colors.primary,
                    ),
                    const SizedBox(height: 8),
                    AppOutlinedButton(
                      label: strings.pendingRequests,
                      onTap: () {
                        context.push('/employees/pending-requests',
                            extra: widget.data);
                      },
                      tintColor: theme.colors.warning,
                    ),
                    const SizedBox(height: 8),
                    AppOutlinedButton(
                      label: strings.resignationRequest,
                      onTap: () {
                        context.push('/employees/resignation-request',
                            extra: widget.data);
                      },
                      tintColor: theme.colors.error,
                    ),
                    const SizedBox(height: 8),
                    PrimaryButton(
                      label: strings.writeFeedback,
                      onTap: () {
                        showAppDialog(context, (context, sc) {
                          return EmployeeFeedbackForm(onDismiss: () {
                            if (context.mounted) {
                              context.pop();
                            }
                          });
                        }, useRootNavigator: true);
                      },
                      isCompact: true,
                      // tintColor: theme.colors.warning,
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.overview,
                  titleColor: theme.colors.secondaryText,
                  action: InkWell(
                    key: _statsDurationDropdownButtonKey,
                    onTap: () {
                      showLocalContextMenu(
                        context: context,
                        buttonKey: _statsDurationDropdownButtonKey,
                        showDivider: true,
                        items: [
                          {
                            'label': strings.daily,
                            'onPressed': () {},
                          },
                          {
                            'label': strings.weekly,
                            'onPressed': () {},
                          },
                          {
                            'label': strings.monthly,
                            'onPressed': () {},
                          },
                          {
                            'label': strings.yearly,
                            'onPressed': () {},
                          },
                        ],
                        maxWidth: 150,
                        positionShift: (offset, size) =>
                            offset.translate(0, size.height + 8),
                      );
                    },
                    splashFactory: NoSplash.splashFactory,
                    child: Container(
                      decoration: BoxDecoration(
                          border: Border.all(
                              width: 1, color: theme.colors.primaryVariant),
                          borderRadius: BorderRadius.circular(4),
                          color: theme.colors.background),
                      padding: const EdgeInsets.all(4),
                      child: Row(
                        children: [
                          Icon(
                            HugeIcons.strokeRoundedArrowDown01,
                            size: 12,
                            color: theme.colors.primary,
                          ),
                          const SizedBox(width: 8),
                          // localize
                          Text(
                            strings.allTime,
                            style: theme.textStyles.body3
                                .copyWith(color: theme.colors.secondaryText),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          Expanded(
                              child: _buildStatsCard(
                            strings.attendance,
                            Directionality.of(context) == TextDirection.rtl
                                ? '28/16'
                                : '16/28',
                          )),
                          const SizedBox(width: 8),
                          Expanded(
                              child: _buildStatsCard(strings.workingHours,
                                  strings.hoursLetter(234.3))),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                              child:
                                  _buildStatsCard(strings.leavesTaken, '12')),
                          const SizedBox(width: 8),
                          Expanded(
                              child: _buildStatsCard(
                                  strings.remainingLeaves, '18')),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.addressInformation,
                  titleColor: theme.colors.secondaryText,
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: theme.colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: theme.colors.strokeColor),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Details section
                      _buildMinimalDetailRow(
                        strings.country,
                        widget.data?.country ?? strings.iraq,
                      ),
                      if (widget.data?.city != null &&
                          widget.data!.city!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        _buildMinimalDetailRow(
                          strings.city,
                          widget.data!.city!,
                        ),
                      ],
                      if (widget.data?.address != null &&
                          widget.data!.address!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        _buildMinimalDetailRow(
                          strings.address,
                          widget.data!.address!,
                        ),
                      ],
                      if (widget.data?.zipCode != null &&
                          widget.data!.zipCode!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        _buildMinimalDetailRow(
                          strings.zipCode,
                          widget.data!.zipCode!,
                        ),
                      ],
                      const SizedBox(height: 8),

                      AppOutlinedButton(
                        label: strings.editAddress,
                        onTap: _handleEditAddress,
                        outlineColor: theme.colors.strokeColor,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.salary,
                  titleColor: theme.colors.secondaryText,
                ),
                const SizedBox(height: 8),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  decoration: BoxDecoration(
                    color: theme.colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: theme.colors.strokeColor),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Details section
                      _buildMinimalDetailRow(
                        strings.gross,
                        formatCurrency(1100000, context),
                      ),

                      const SizedBox(height: 4),
                      _buildMinimalDetailRow(
                        strings.taxes,
                        formatCurrency(10000, context),
                      ),

                      const SizedBox(height: 4),
                      _buildMinimalDetailRow(
                        strings.netSalary,
                        formatCurrency(1100000, context),
                      ),

                      const SizedBox(height: 8),

                      AppOutlinedButton(
                        label: strings.editSalary,
                        onTap: _handleEditSalary,
                        outlineColor: theme.colors.strokeColor,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                SectionHeading(
                  title: strings.documents,
                  titleColor: theme.colors.secondaryText,
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      AttachmentTile(
                        fileName: 'CV.pdf',
                        fileSize: strings.sizeKB(200),
                        state: AttachmentState.viewOnly,
                      ),
                      const SizedBox(height: 8),
                      AttachmentTile(
                        fileName: 'National ID.pdf',
                        fileSize: strings.sizeKB(200),
                        state: AttachmentState.viewOnly,
                      ),
                      const SizedBox(height: 8),
                      AttachmentTile(
                        fileName: 'Contract.pdf',
                        fileSize: strings.sizeKB(200),
                        state: AttachmentState.viewOnly,
                      ),
                      const SizedBox(height: 8),
                      AttachmentTile(
                        fileName: 'Bank Details.pdf',
                        fileSize: strings.sizeKB(200),
                        state: AttachmentState.viewOnly,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                ChipSelector(
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 8, 8),
                  items: [
                    ChipItem(
                      label: strings.shiftHistory,
                      icon: Iconsax.user_copy,
                      tag: 'shift',
                    ),
                    ChipItem(
                      label: strings.salaryHistory,
                      icon: Iconsax.coin_copy,
                      tag: 'salary',
                    ),
                    ChipItem(
                      label: strings.tasks,
                      icon: Iconsax.menu_board_copy,
                      tag: 'task',
                    ),
                    ChipItem(
                      label: strings.leavesHistory,
                      icon: Iconsax.user_remove_copy,
                      tag: 'leave',
                    ),
                  ],
                  onItemTap: (tag) {
                    setState(() {
                      _selectedTab = tag;
                    });
                  },
                  selectedItems: [_selectedTab],
                ),
                //  list of cards depending on the selected tab
                // switches with animation/transition
                SizedBox(
                  height: MediaQuery.sizeOf(context).height -
                      (kToolbarHeight +
                          MediaQuery.paddingOf(context).top +
                          MediaQuery.paddingOf(context).bottom +
                          52),
                  child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 400),
                      switchInCurve: Curves.easeOut,
                      switchOutCurve: Curves.easeIn,
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0.1, 0.0),
                            end: Offset.zero,
                          ).animate(animation),
                          child: FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        );
                      },
                      child: _selectedTab == 'shift'
                          ? Column(
                              key: const ValueKey('shift'),
                              children: [
                                EmployeeBriefCard(
                                  date: DateTime.now(),
                                  entries: [
                                    {
                                      'title': strings.workDayYesNo,
                                      'value': strings.yes,
                                      'valueColor': theme.colors.success
                                    },
                                    {
                                      'title': strings.checkInTime,
                                      'value':
                                          formatTime(DateTime.now(), context)
                                    },
                                    {
                                      'title': strings.checkOutTime,
                                      'value': formatTime(
                                        DateTime(2025, 1, 1, 8),
                                        context,
                                      )
                                    },
                                  ],
                                  footerEntries: [
                                    {
                                      'title': strings.workingHours,
                                      'value': '8 ${strings.hours}',
                                      'icon': SolarIconsOutline.clockCircle
                                    },
                                    {
                                      'title': strings.notes,
                                      'value': strings.noIssues,
                                      'icon': SolarIconsOutline.document1
                                    },
                                  ],
                                ),
                                EmployeeBriefCard(
                                    date: DateTime.now(),
                                    entries: [
                                      {
                                        'title': strings.workDayYesNo,
                                        'value': strings.no,
                                        'valueColor': theme.colors.error
                                      },
                                    ]),
                                EmployeeBriefCard(
                                  date: DateTime.now(),
                                  entries: [
                                    {
                                      'title': strings.workDayYesNo,
                                      'value': strings.yes,
                                      'valueColor': theme.colors.success
                                    },
                                    {
                                      'title': strings.checkInTime,
                                      'value':
                                          formatTime(DateTime.now(), context)
                                    },
                                    {
                                      'title': strings.checkOutTime,
                                      'value': formatTime(
                                        DateTime(2025, 1, 1, 8),
                                        context,
                                      )
                                    },
                                  ],
                                  footerEntries: [
                                    {
                                      'title': strings.workingHours,
                                      'value': '8 ${strings.hours}',
                                      'icon': SolarIconsOutline.clockCircle
                                    },
                                    {
                                      'title': strings.notes,
                                      'value': strings.noIssues,
                                      'icon': SolarIconsOutline.document1
                                    },
                                  ],
                                ),
                              ],
                            )
                          : _selectedTab == 'salary'
                              ? Column(
                                  key: const ValueKey('salary'),
                                  children: [
                                    EmployeeBriefCard(
                                      date: DateTime.now(),
                                      status: 'paid',
                                      entries: [
                                        {
                                          'title': strings.month,
                                          'value': getMonths(context)[0],
                                        },
                                        {
                                          'title': strings.netSalary,
                                          'value':
                                              formatCurrency(100000, context),
                                        },
                                        {
                                          'title': strings.totalAdditions,
                                          'value':
                                              formatCurrency(50000, context),
                                        },
                                        {
                                          'title': strings.totalDeductions,
                                          'value':
                                              formatCurrency(110000, context),
                                        },
                                      ],
                                    ),
                                    EmployeeBriefCard(
                                      date: DateTime.now(),
                                      status: 'unpaid',
                                      entries: [
                                        {
                                          'title': strings.month,
                                          'value': strings.january,
                                        },
                                        {
                                          'title': strings.netSalary,
                                          'value':
                                              formatCurrency(100000, context),
                                        },
                                        {
                                          'title': strings.totalAdditions,
                                          'value':
                                              formatCurrency(50000, context),
                                        },
                                        {
                                          'title': strings.totalDeductions,
                                          'value':
                                              formatCurrency(110000, context),
                                        },
                                      ],
                                    ),
                                    EmployeeBriefCard(
                                      date: DateTime.now(),
                                      status: 'pending',
                                      entries: [
                                        {
                                          'title': strings.month,
                                          'value': strings.january,
                                        },
                                        {
                                          'title': strings.netSalary,
                                          'value':
                                              formatCurrency(100000, context),
                                        },
                                        {
                                          'title': strings.totalAdditions,
                                          'value':
                                              formatCurrency(50000, context),
                                        },
                                        {
                                          'title': strings.totalDeductions,
                                          'value':
                                              formatCurrency(110000, context),
                                        },
                                      ],
                                    ),
                                  ],
                                )
                              : _selectedTab == 'task'
                                  ? Column(
                                      key: const ValueKey('task'),
                                      children: [
                                        EmployeeBriefCard(
                                          date: DateTime.now(),
                                          status: 'pending',
                                          entries: [
                                            {
                                              'title': strings.taskDescription,
                                              'value': 'Design new UI',
                                            },
                                            {
                                              'title': strings.dueDate,
                                              'value': formatDateDmy(
                                                  DateTime(2025, 1, 13),
                                                  context)
                                            },
                                            {
                                              'title': strings.notes,
                                              'value': 'Task Delivered'
                                            },
                                          ],
                                        ),
                                        EmployeeBriefCard(
                                          date: DateTime.now(),
                                          status: 'completed',
                                          entries: [
                                            {
                                              'title': strings.taskDescription,
                                              'value': 'Design new UI',
                                            },
                                            {
                                              'title': strings.dueDate,
                                              'value': formatDateDmy(
                                                  DateTime(2025, 1, 13),
                                                  context)
                                            },
                                            {
                                              'title': strings.notes,
                                              'value': 'Task Delivered'
                                            },
                                          ],
                                        ),
                                        EmployeeBriefCard(
                                          date: DateTime.now(),
                                          status: 'pending',
                                          entries: [
                                            {
                                              'title': strings.taskDescription,
                                              'value': 'Design new UI',
                                            },
                                            {
                                              'title': strings.dueDate,
                                              'value': formatDateDmy(
                                                  DateTime(2025, 1, 13),
                                                  context)
                                            },
                                            {
                                              'title': strings.notes,
                                              'value': 'Task Delivered'
                                            },
                                          ],
                                        ),
                                      ],
                                    )
                                  : Column(
                                      key: const ValueKey('leave'),
                                      children: [
                                        EmployeeBriefCard(
                                          date: DateTime.now(),
                                          status: 'pending',
                                          entries: [
                                            {
                                              'title': strings.duration,
                                              'value': '7 ${strings.days}',
                                            },
                                            {
                                              'title': strings.leaveType,
                                              'value': 'Sick Leave'
                                            },
                                            {
                                              'title': strings.approvedBy,
                                              'value': 'HR Department'
                                            },
                                            {
                                              'title': strings.notes,
                                              'value': 'End of year break',
                                            },
                                          ],
                                        ),
                                        EmployeeBriefCard(
                                          date: DateTime.now(),
                                          status: 'approved',
                                          entries: [
                                            {
                                              'title': strings.duration,
                                              'value': '7 ${strings.days}',
                                            },
                                            {
                                              'title': strings.leaveType,
                                              'value': 'Sick Leave'
                                            },
                                            {
                                              'title': strings.approvedBy,
                                              'value': 'HR Department'
                                            },
                                            {
                                              'title': strings.notes,
                                              'value': 'End of year break',
                                            },
                                          ],
                                        ),
                                        EmployeeBriefCard(
                                          date: DateTime.now(),
                                          status: 'rejected',
                                          entries: [
                                            {
                                              'title': strings.duration,
                                              'value': '7 ${strings.days}',
                                            },
                                            {
                                              'title': strings.leaveType,
                                              'value': 'Sick Leave'
                                            },
                                            {
                                              'title': strings.approvedBy,
                                              'value': 'HR Department'
                                            },
                                            {
                                              'title': strings.notes,
                                              'value': 'End of year break',
                                            },
                                          ],
                                        ),
                                      ],
                                    )),
                ),
              ],
            ),
    );
  }

  Widget _buildMinimalDetailRow(String title, String value) {
    final theme = AppTheme.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title,
            style: theme.textStyles.body3
                .copyWith(color: theme.colors.tertiaryText)),
        Text(':',
            style: theme.textStyles.body3
                .copyWith(color: theme.colors.tertiaryText)),
        Expanded(
            child: Text(value,
                style: theme.textStyles.body3
                    .copyWith(color: theme.colors.secondaryText),
                textAlign: TextAlign.end)),
      ],
    );
  }

  Widget _buildStatsCard(String title, String value) {
    final theme = AppTheme.of(context);
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colors.strokeColor),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            title,
            style: theme.textStyles.body2.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          const SizedBox(height: 8),
          // gradient
          GradientMask(
            gradient: DesignColors.primaryGradient,
            child: Text(
              value,
              style: theme.textStyles.headline2.copyWith(
                color: theme.colors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleEditAddress() async {
    await showAdaptivePopup(context, (ctx, sc) {
      return EmployeeEditAddressPopup(
        data: widget.data,
      );
    });
  }

  void _handleEditSalary() async {
    await showAdaptivePopup(context, (ctx, sc) {
      return EmployeeEditSalaryPopup(
        data: widget.data,
      );
    });
  }
}
