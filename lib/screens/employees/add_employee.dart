import 'dart:math';

import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/date_picker/date/show_date_picker_dialog.dart';
import 'package:ako_basma/components/form/attachment/attachment_placeholder_card.dart';
import 'package:ako_basma/components/form/attachment/attachment_tile.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/color_selector.dart';
import 'package:ako_basma/components/form/radio_button.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/icon/ako_custom_icons_icons.dart';
import 'package:ako_basma/components/stepper/stepper.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_info_header.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/components/form/attachment/attachment_grid_card.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'dart:io';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/components/picker/time_picker.dart' as timePicker;

class AddEmployeeScreen extends StatefulWidget {
  final VoidCallback? onSuccess;
  final EmployeeData? initialData;
  final String? initialTab;
  const AddEmployeeScreen({
    super.key,
    this.onSuccess,
    this.initialData,
    this.initialTab,
  });

  @override
  State<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends State<AddEmployeeScreen> {
  // Personal Information Controllers
  final _firstNameController = TextEditingController();
  final _middleNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _dateOfBirthController = TextEditingController();

  // Job Information Controllers
  final _jobTitleController = TextEditingController();
  final _departmentController = TextEditingController();
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  final _contractController = TextEditingController();
  final _timingsController = TextEditingController();

  // Contact Information Controllers
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _countryController = SingleSelectController<String?>(null);
  final _cityController = SingleSelectController<String?>(null);
  final _zipCodeController = TextEditingController();

  final _hourlyRateController = TextEditingController();

  // State Variables
  DateTime? _dateOfBirth;
  DateTime? _startDate;
  String? _selectedEmploymentType;
  String? _selectedScheduleType;
  String _selectedSalaryType = 'hourly';
  TimeOfDay? _shiftStartTime;
  TimeOfDay? _shiftEndTime;
  String _selectedColor = '';

  // Document attachments
  File? _nationalIdAttachment;
  File? _contractAttachment;
  List<File> _otherAttachments = [];

  List<Map<String, String>> _employmentTypes = const [
    {'id': 'part_time_on_site', 'label_en': 'Part-Time - On-site'},
    {'id': 'full_time_on_site', 'label_en': 'Full-time - On-site'},
    {'id': 'freelance', 'label_en': 'Freelance'},
    {'id': 'full_time_remote', 'label_en': 'Full-time - Remote'},
    {'id': 'full_time_hybrid', 'label_en': 'Full-time - Hybrid'},
  ];

  final _steps = [
    'details',
    'contact',
    'schedule',
    'summary',
  ];
  String _activeStep = 'details';

  @override
  void initState() {
    super.initState();
    if (widget.initialData != null) {
      _activeStep = widget.initialTab ?? 'details';
    }

    if (widget.initialData != null) {
      final data = widget.initialData!;
      // Name fields (if you have separate controllers for first/middle/last, try to split)
      if (data.name.isNotEmpty) {
        final nameParts = data.name.split(' ');
        _firstNameController.text = nameParts.isNotEmpty ? nameParts[0] : '';
        _middleNameController.text = nameParts.length > 2
            ? nameParts.sublist(1, nameParts.length - 1).join(' ')
            : '';
        _lastNameController.text = nameParts.length > 1 ? nameParts.last : '';
      }
      _jobTitleController.text = data.jobTitle;
      _departmentController.text = data.department;
      _contractController.text = data.contract;
      _phoneController.text = data.phone ?? '';
      _emailController.text = data.email ?? '';
      _addressController.text = data.address ?? '';
      _zipCodeController.text = data.zipCode ?? '';
      _selectedColor = data.departmentColor?.toString() ?? '';
      // If you have a controller for imageUrl, set it here (not shown in your code)
      if (data.country != null) {
        _countryController.value = data.country;
      }
      if (data.city != null) {
        _cityController.value = data.city;
      }
      // Date fields
      _startDate = data.dateOfJoining;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // _dateOfBirthController.text = formatDateDmy(d, context);
        _startDateController.text = formatDateDmy(data.dateOfJoining, context);
      });
      if (data.country != null) {
        _countryController.value = data.country;
      }
      if (data.city != null) {
        _cityController.value = data.city;
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isDateOfBirth) async {
    final initialDate = isDateOfBirth
        ? _dateOfBirth ??
            DateTime.now().subtract(const Duration(days: 365 * 18))
        : _startDate ?? DateTime.now();
    final DateTime? picked = await showDatePickerDialog(
        context: context,
        initialDate: initialDate,
        selectedDate: isDateOfBirth ? _dateOfBirth : _startDate,
        minDate: isDateOfBirth ? DateTime(1900) : DateTime(2000),
        maxDate: isDateOfBirth ? DateTime.now() : DateTime(2100),
        onClearTap: isDateOfBirth
            ? () {
                setState(() {
                  _dateOfBirth = null;
                  _dateOfBirthController.clear();
                });
              }
            : () {
                setState(() {
                  _startDate = null;
                  _startDateController.clear();
                });
              }
        // firstDate: isDateOfBirth ? DateTime(1900) : DateTime.now(),
        // lastDate: isDateOfBirth ? DateTime.now() : DateTime(2100),
        );
    if (picked != null) {
      setState(() {
        if (isDateOfBirth) {
          _dateOfBirth = picked;
          _dateOfBirthController.text = formatDateDmy(picked, context);
        } else {
          _startDate = picked;
          _startDateController.text = formatDateDmy(picked, context);
        }
      });
    }
  }

  Future<void> _pickFiles(String type, Offset? offset) async {
    try {
      final res = await showLocalPickerMenu(
        pointOffset: offset,
        context: context,
        allowedTypes: ['any'],
        allowMultiple: type == 'other',
        maxSizeInMB: 25,
      );
      setState(() {
        if (type == 'other') {
          if (res is File) {
            _otherAttachments.add(res);
          }
          if (res is List<File>) {
            _otherAttachments.addAll(res);
          }
        } else {
          final file = res;
          if (type == 'national_id') {
            _nationalIdAttachment = file;
          } else if (type == 'contract') {
            _contractAttachment = file;
          }
        }
      });
    } catch (e) {
      debugPrint('Error picking files: $e');
    }
  }

  // Function to remove a document attachment
  void _removeDocumentAttachment(String type, {int? index}) {
    setState(() {
      switch (type) {
        case 'national_id':
          _nationalIdAttachment = null;
          break;
        case 'contract':
          _contractAttachment = null;
          break;
        case 'other':
          if (index != null && index < _otherAttachments.length) {
            _otherAttachments.removeAt(index);
          }
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final contents = switch (_activeStep) {
      'details' => Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SectionHeading(
              title: strings.personalInformation,
              titleStyle: theme.textStyles.headline4,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SimpleTextField(
                    controller: _firstNameController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    decoration: InputDecoration(labelText: strings.firstName),
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    controller: _middleNameController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    decoration: InputDecoration(labelText: strings.middleName),
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    controller: _lastNameController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    decoration: InputDecoration(labelText: strings.lastName),
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    readOnly: true,
                    controller: _dateOfBirthController,
                    onTap: () => _selectDate(context, true),
                    decoration: InputDecoration(
                      labelText: strings.dateOfBirth,
                    ),
                  ),
                  const SizedBox(height: 12),
                  CustomDropdown(
                    onChanged: (p0) {},
                    items: [
                      ChipItem(label: strings.male, tag: 'male'),
                      ChipItem(label: strings.female, tag: 'female'),
                    ],
                    hintText: strings.gender,

                    // controller: _departmentController,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    controller: _nationalIdController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    decoration: InputDecoration(labelText: strings.nationalId),
                    keyboardType: TextInputType.number,
                  ),
                ],
              ),
            ),
            SectionHeading(
              title: strings.jobInformation,
              titleStyle: theme.textStyles.headline4,
            ),
            const SizedBox(height: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SimpleTextField(
                    controller: _jobTitleController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    decoration: InputDecoration(labelText: strings.jobTitle),
                  ),
                ),
                const SizedBox(height: 12),
                ColorSelector(
                  padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 8, 0),
                  colors: dummyColors,
                  selectedKey: _selectedColor,
                  onColorSelected: (key) {
                    setState(() {
                      _selectedColor = key;
                    });
                  },
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SimpleTextField(
                        readOnly: true,
                        controller: _startDateController,
                        onTap: () => _selectDate(context, false),
                        decoration: InputDecoration(
                          labelText: strings.startDate,
                        ),
                      ),
                      const SizedBox(height: 12),
                      CustomDropdown(
                        onChanged: (p0) {},
                        items: ['IT', 'HR', 'Finance', 'Marketing'],
                        hintText: strings.department,

                        // controller: _departmentController,
                      ),
                      const SizedBox(height: 12),
                      CustomDropdown(
                        onChanged: (p0) {},
                        items: ['Team A', 'Team B', 'Team C', 'Team D'],
                        hintText: strings.team,
                        // controller: _teamController,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SectionHeading(
              title: strings.uploadDocuments,
              titleStyle: theme.textStyles.headline4,
            ),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // National ID Document
                  AttachmentPlaceholderCard(
                    preset: 'national_id',
                    filePath: _nationalIdAttachment?.path,

                    onDelete: () =>
                        // _nationalIdAttachment!=null?

                        _removeDocumentAttachment('national_id'),
                    // onTap: () => _pickFiles('national_id'),
                    onTapPos: (pos) => _pickFiles('national_id', pos),
                  ),
                  const SizedBox(height: 12),
                  // Contract Document
                  AttachmentPlaceholderCard(
                    preset: 'contract',
                    filePath: _contractAttachment?.path,
                    onDelete: () => _removeDocumentAttachment('contract'),
                    onTapPos: (pos) => _pickFiles('contract', pos),
                  ),
                  const SizedBox(height: 12),
                  // Additional Documents List
                  if (_otherAttachments.isNotEmpty)
                    ..._otherAttachments.indexed.map((e) => Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: SizedBox(
                            // height: 130,
                            child: AttachmentPlaceholderCard(
                              preset: 'other',
                              filePath: e.$2.path,
                              onDelete: () => _removeDocumentAttachment(
                                'other',
                                index: e.$1,
                              ),
                            ),
                          ),
                        )),
                  // Add new document card
                  AttachmentPlaceholderCard(
                    preset: 'other',
                    onTapPos: (pos) => _pickFiles('other', pos),
                  ),
                ],
              ),
            )

            //  AttachmentGridCard(
            //     url:
            //         'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
            //   ),
          ],
        ),
      'contact' => Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SectionHeading(
              title: strings.contact,
              titleStyle: theme.textStyles.headline4,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SimpleTextField(
                    controller: _phoneController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    // textAlign: TextAlign.end,
                    decoration: InputDecoration(
                      labelText: strings.phoneNumber,
                      prefixIcon: const Icon(
                        SolarIconsOutline.phone,
                        size: 24,
                      ),
                    ),

                    keyboardType: TextInputType.phone,
                    forceLtr: true,
                  ),
                  const SizedBox(height: 12),
                  SimpleTextField(
                    controller: _emailController,
                    onChanged: (_) {
                      setState(() {});
                    },
                    decoration: InputDecoration(
                      labelText: strings.emailAddress,
                      // Use result of function

                      prefixIcon: const Icon(
                        SolarIconsOutline.letter,
                        size: 24,
                      ),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    forceLtr: true,
                  ),
                ],
              ),
            ),
            SectionHeading(
              title: strings.addressInformation,
              titleStyle: theme.textStyles.headline4,
            ),
            const SizedBox(height: 12),
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SimpleTextField(
                      onChanged: (_) {
                        setState(() {});
                      },
                      controller: _addressController,
                      decoration: InputDecoration(labelText: strings.address),
                    ),
                    const SizedBox(height: 12),
                    CustomDropdown(
                      onChanged: (p0) {},
                      items: [
                        'Baghdad',
                        'New York',
                        'Los Angeles',
                        'Chicago',
                        'Houston'
                      ],
                      hintText: strings.city,
                      controller: _cityController,
                    ),
                    const SizedBox(height: 12),
                    SimpleTextField(
                      onChanged: (_) {
                        setState(() {});
                      },
                      controller: _zipCodeController,
                      decoration: InputDecoration(
                          labelText: strings.zipCode, counter: SizedBox()),
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                    ),
                    const SizedBox(height: 12),
                    CustomDropdown(
                      onChanged: (p0) {},
                      items: ['Iraq', 'USA', 'Canada', 'UK', 'Australia'],
                      hintText: strings.country,
                      controller: _countryController,
                    ),
                  ],
                ))
          ],
        ),
      'schedule' => Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SectionHeading(
              title: strings.employementType,
              titleStyle: theme.textStyles.headline4,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
              child: Builder(
                builder: (context) {
                  final items = _employmentTypes.indexed
                      .map((e) => Padding(
                            padding: EdgeInsetsGeometry.only(
                                bottom: e.$1 == _employmentTypes.length - 1
                                    ? 0
                                    : 12),
                            child: AppRadioButton.tile(
                              value: e.$2['id'] ?? "",
                              groupValue: _selectedEmploymentType ?? "",
                              onChanged: (String? value) {
                                setState(() {
                                  _selectedEmploymentType = value;
                                });
                              },
                              // localize
                              label: e.$2['label_en'],
                            ),
                          ))
                      .toList();
                  return Column(children: items);
                },
              ),
            ),
            SectionHeading(
              title: strings.workingSchedule,
              titleStyle: theme.textStyles.headline4,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
              child: SimpleTextField(
                controller: _timingsController,
                readOnly: true,
                onTap: _handleScheduleTimePick,
                onContainer: true,
                onChanged: (_) {
                  setState(() {});
                },
                decoration: InputDecoration(
                  labelText: strings.shiftWorkingTime,
                  prefixIcon: const Icon(
                    SolarIconsOutline.clockCircle,
                    size: 24,
                  ),
                ),
                keyboardType: TextInputType.phone,
              ),
            ),
            ChipSelector(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              items: [
                ChipItem(
                    label: strings.sundayToFriday, tag: 'sunday_to_friday'),
                ChipItem(label: strings.availableOnWeekends, tag: 'weekends'),
                ChipItem(label: strings.dailyShifts, tag: 'daily'),
              ],
              selectedChipColor: theme.colors.primaryVariant,
              unselectedChipColor: theme.colors.primaryVariant,
              selectedBorderColor: theme.colors.primaryVariantDark,
              selectedTextStyle: theme.textStyles.body2
                  .copyWith(color: theme.colors.primaryVariantDark),
              unselectedTextStyle: theme.textStyles.body2
                  .copyWith(color: theme.colors.secondaryText),
              selectedItems: [_selectedScheduleType ?? ""],
              chipPadding:
                  const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              borderRadius: BorderRadius.circular(8),
              onItemTap: (id) {
                setState(() {
                  _selectedScheduleType = id;
                });
              },
            ),
            const SizedBox(height: 4),
            SectionHeading(
              title: strings.salary,
              titleStyle: theme.textStyles.headline4,
            ),
            Builder(builder: (context) {
              final data = {
                // localize
                'hourly': strings.hourly,
                'monthly': strings.monthly,
                'yearly': strings.yearly,
                'fixed': strings.fixed,
              };
              Widget buildCard(String id) {
                final selected = _selectedSalaryType == id;
                return InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: _selectedSalaryType != id
                      ? () {
                          setState(() {
                            _selectedSalaryType = id;
                          });
                        }
                      : null,
                  child: Stack(
                    children: [
                      AnimatedContainer(
                        duration: 300.milliseconds,
                        padding: const EdgeInsets.all(20),
                        width: double.infinity,
                        decoration: BoxDecoration(
                            border: Border.all(
                              color: selected
                                  ? theme.colors.primary
                                  : theme.colors.strokeColor,
                            ),
                            borderRadius: BorderRadius.circular(8)),
                        child: Column(
                          children: [
                            Opacity(
                              opacity: selected ? 1 : 0.6,
                              child: SvgPicture.asset(
                                'assets/icons/cash-blue.svg',
                                height: 40,
                                width: 40,
                                colorFilter: ColorFilter.mode(
                                  selected
                                      ? theme.colors.primary
                                      : theme.colors.secondaryText,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              data[id] ?? "",
                              style: theme.textStyles.body.copyWith(
                                  color: selected
                                      ? theme.colors.primary
                                      : theme.colors.secondaryText),
                            )
                          ],
                        ),
                      ),
                      PositionedDirectional(
                          top: 8,
                          start: 8,
                          child: AppRadioButton(
                              value: id,
                              groupValue: _selectedSalaryType,
                              onChanged: (v) {
                                setState(() {
                                  _selectedSalaryType = id;
                                });
                              }))
                    ],
                  ),
                );
              }

              return Padding(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SimpleTextField(
                      controller: _hourlyRateController,
                      onChanged: (_) {
                        setState(() {});
                      },
                      onContainer: true,
                      decoration: InputDecoration(
                        labelText:
                            // localize with selected type
                            strings.hourlyRate,
                        prefixIcon: const Icon(
                          AkoCustomIcons.subtract,
                          size: 16,
                        ),
                        // FittedBox(
                        //   fit: BoxFit.scaleDown,
                        //   child: SvgPicture.asset(
                        //     'assets/icons/cash-outline.svg',
                        //     height: 24,
                        //     width: 24,
                        //   ),
                        // ),
                      ),
                      textInputAction: TextInputAction.done,
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                    ),
                    const SizedBox(height: 12),
                    Column(
                      children: [
                        Row(
                          children: [
                            Expanded(child: buildCard('hourly')),
                            const SizedBox(width: 12),
                            Expanded(child: buildCard('monthly')),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(child: buildCard('yearly')),
                            const SizedBox(width: 12),
                            Expanded(child: buildCard('fixed')),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      'summary' => Column(
          children: [
            EmployeeInfoHeader(
              data: EmployeeData(
                id: 1,
                imageUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
                name: (() {
                  final names = [
                    _firstNameController.text.trim(),
                    if (_middleNameController.text.trim().isNotEmpty)
                      _middleNameController.text.trim(),
                    if (_lastNameController.text.trim().isNotEmpty)
                      _lastNameController.text.trim(),
                  ].where((name) => name.isNotEmpty).toList();
                  if (names.isEmpty) {
                    return 'John Doe';
                  }
                  return names.join(' ');
                })(),
                contract: _contractController.text.trim().isEmpty
                    ? 'Full-Time - On-site'
                    : _contractController.text,
                jobTitle: _jobTitleController.text.trim().isEmpty
                    ? strings.hrManager
                    : _jobTitleController.text,
                department: _departmentController.text.trim().isEmpty
                    ? 'Team A'
                    : _departmentController.text,
                departmentColor: _selectedColor,
                email: _emailController.text.trim().isEmpty
                    ? '<EMAIL>'
                    : _emailController.text,
                phone: _phoneController.text.trim().isEmpty
                    ? embedLtr('****** 123 4567')
                    : _phoneController.text,
                dateOfJoining: DateTime.now(),
              ),
              items: [
                const SizedBox(height: 8),
                AppOutlinedButton(
                  label: strings.editInfo,
                  onTap: () {
                    setState(() {
                      _activeStep = _steps[0];
                    });
                  },
                  outlineColor: theme.colors.strokeColor,
                ),
              ],
            ),

            const SizedBox(height: 16),
            // dont need these ig.?
            // SectionHeading(
            //   title: 'Overview',
            //   titleColor: theme.colors.secondaryText,
            //   action: InkWell(
            //     key: _statsDurationDropdownButtonKey,
            //     onTap: () {
            //       showLocalContextMenu(
            //         context: context,
            //         buttonKey: _statsDurationDropdownButtonKey,
            //         showDivider: true,
            //         items: [
            //           {
            //             'label': 'Daily',
            //             'onPressed': () {},
            //           },
            //           {
            //             'label': 'Weekly',
            //             'onPressed': () {},
            //           },
            //           {
            //             'label': 'Monthly',
            //             'onPressed': () {},
            //           },
            //           {
            //             'label': 'Yearly',
            //             'onPressed': () {},
            //           },
            //         ],
            //         maxWidth: 150,
            //         positionShift: (offset, size) =>
            //             offset.translate(0, size.height + 8),
            //       );
            //     },
            //     splashFactory: NoSplash.splashFactory,
            //     child: Container(
            //       decoration: BoxDecoration(
            //           border: Border.all(
            //               width: 1, color: theme.colors.primaryVariant),
            //           borderRadius: BorderRadius.circular(4),
            //           color: theme.colors.background),
            //       padding: const EdgeInsets.all(4),
            //       child: Row(
            //         children: [
            //           Icon(
            //             HugeIcons.strokeRoundedArrowDown01,
            //             size: 12,
            //             color: theme.colors.primary,
            //           ),
            //           const SizedBox(width: 8),
            //           // localize
            //           Text(
            //             'All Time',
            //             style: theme.textStyles.body3
            //                 .copyWith(color: theme.colors.secondaryText),
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),
            // ),
            // const SizedBox(height: 8),
            // Padding(
            //   padding: const EdgeInsets.symmetric(horizontal: 16),
            //   child: Column(
            //     crossAxisAlignment: CrossAxisAlignment.stretch,
            //     children: [
            //       Row(
            //         children: [
            //           Expanded(child: _buildStatsCard('Attendance', '16/28')),
            //           const SizedBox(width: 8),
            //           Expanded(
            //               child: _buildStatsCard('Working Hours', '243.5 H')),
            //         ],
            //       ),
            //       const SizedBox(height: 8),
            //       Row(
            //         children: [
            //           Expanded(child: _buildStatsCard('Leaves Taken', '12')),
            //           const SizedBox(width: 8),
            //           Expanded(
            //               child: _buildStatsCard('Remaining Leaves', '18')),
            //         ],
            //       ),
            //     ],
            //   ),
            // ),
            // const SizedBox(height: 16),
            SectionHeading(
              title: strings.addressInformation,
              titleColor: theme.colors.secondaryText,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colors.strokeColor),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Details section
                  _buildMinimalDetailRow(
                    strings.country,
                    _countryController.value ?? 'Iraq',
                  ),
                  const SizedBox(height: 4),
                  _buildMinimalDetailRow(
                    strings.city,
                    _cityController.value ?? 'Baghdad',
                  ),

                  const SizedBox(height: 4),
                  _buildMinimalDetailRow(
                    strings.address,
                    _addressController.text.isEmpty
                        ? 'Al-Mansour District, Street 12'
                        : _addressController.text,
                  ),

                  const SizedBox(height: 4),
                  _buildMinimalDetailRow(
                    strings.zipCode,
                    _zipCodeController.text.isEmpty
                        ? '62001'
                        : _zipCodeController.text,
                  ),
                  AppOutlinedButton(
                    label: strings.editAddress,
                    onTap: () {
                      setState(() {
                        _activeStep = _steps[1];
                      });
                    },
                    outlineColor: theme.colors.strokeColor,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            SectionHeading(
              title: strings.salary,
              titleColor: theme.colors.secondaryText,
            ),
            const SizedBox(height: 8),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              decoration: BoxDecoration(
                color: theme.colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colors.strokeColor),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Details section
                  _buildMinimalDetailRow(
                    strings.gross,
                    formatCurrency(1100000, context),
                  ),

                  const SizedBox(height: 4),
                  _buildMinimalDetailRow(
                    strings.taxes,
                    formatCurrency(10000, context),
                  ),

                  const SizedBox(height: 4),
                  _buildMinimalDetailRow(
                    'Net salary',
                    formatCurrency(1100000, context),
                  ),

                  const SizedBox(height: 8),

                  AppOutlinedButton(
                    label: strings.editSalary,
                    onTap: () {
                      setState(() {
                        _activeStep = _steps[2];
                      });
                    },
                    outlineColor: theme.colors.strokeColor,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            Builder(builder: (context) {
              final attachments = [
                _nationalIdAttachment,
                _contractAttachment,
                ..._otherAttachments
              ].nonNulls;

              if (attachments.isEmpty) return const SizedBox();
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SectionHeading(
                    title: strings.documents,
                    titleColor: theme.colors.secondaryText,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: attachments.indexed.map(
                        (e) {
                          return Padding(
                            padding: EdgeInsets.only(
                                bottom: e.$1 ==
                                        [
                                              _contractAttachment,
                                              _nationalIdAttachment,
                                              ..._otherAttachments
                                            ].nonNulls.length -
                                            1
                                    ? 0
                                    : 8),
                            child: AttachmentTile(
                              fileName: e.$2.path.split('/').last,
                              fileSize:
                                  '${(e.$2.lengthSync() / 1024).toStringAsFixed(1)} KB',
                              state: AttachmentState.viewOnly,
                            ),
                          );
                        },
                      ).toList(),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              );
            }),
          ],
        ),
      // TODO: Handle this case.
      String() => const SizedBox(),
    };

    return Scaffold(
      appBar: MyAppbar(title: strings.addEmployee),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: AnimatedStepper(
                currentStep: _steps.indexOf(_activeStep) + 1,
                totalSteps: _steps.length),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.fromLTRB(0, 8, 0, 24),
              child: Form(
                child: contents,
              ),
            ),
          ),
          Padding(
              padding: EdgeInsetsGeometry.fromLTRB(
                  16, 8, 16, max(MediaQuery.paddingOf(context).bottom, 16)),
              child: IntrinsicHeight(
                child: Row(
                  children: [
                    AnimatedSize(
                      duration: 300.milliseconds,
                      curve: Curves.easeIn,
                      child: (_steps.indexOf(_activeStep) != 0)
                          ? Padding(
                              padding: const EdgeInsetsDirectional.only(end: 8),
                              child: AppOutlinedButton(
                                label: strings.back,
                                onTap: _handleBack,
                                expand: false,
                                tintColor: theme.colors.primary,
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                    color: theme.colors.primary,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  padding: EdgeInsets.zero,
                                  visualDensity: VisualDensity.compact,
                                ),
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 14),
                                textStyle: theme.textStyles.body.copyWith(
                                    fontSize: 20, color: theme.colors.primary),
                              ),
                            ).animate(delay: 250.milliseconds).fadeIn()
                          : const SizedBox(),
                    ),
                    Expanded(
                      child: PrimaryButton.async(
                        label: _activeStep == 'summary'
                            ? strings.save
                            : strings.next,
                        onPressed: _handleNext,
                        expand: true,
                      ),
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }

  Widget _buildMinimalDetailRow(String title, String value) {
    final theme = AppTheme.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title,
            style: theme.textStyles.body3
                .copyWith(color: theme.colors.tertiaryText)),
        Text(':',
            style: theme.textStyles.body3
                .copyWith(color: theme.colors.tertiaryText)),
        Expanded(
            child: Text(value,
                style: theme.textStyles.body3
                    .copyWith(color: theme.colors.secondaryText),
                textAlign: TextAlign.end)),
      ],
    );
  }

  Future<void> _handleNext() async {
    unfocus();

    // validate
    final currentIndex = _steps.indexOf(_activeStep);
    if (currentIndex != _steps.length - 1) {
      setState(() {
        _activeStep = _steps[currentIndex + 1];
      });
    } else {
      context.pop();
      if (widget.onSuccess != null) widget.onSuccess!();
    }
  }

  Future<void> _handleBack() async {
    unfocus();
    final strings = AppLocalizations.of(context)!;

    // validate
    final currentIndex = _steps.indexOf(_activeStep);
    if (currentIndex > 0) {
      setState(() {
        _activeStep = _steps[currentIndex - 1];
      });
    } else {
      context.pop();
      await Future.delayed(1.seconds);
      showAppSnackbar(context,
          title: strings.employeeAddedSuccessfully, type: 'success');
    }
  }

  void _handleScheduleTimePick() async {
    // final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    TimeOfDay? startTime = await timePicker.showTimePicker(
        context: context,
        confirmText: strings.ok,
        cancelText: strings.cancel,
        initialTime: _shiftStartTime ?? TimeOfDay.now(),
        helpText: strings.selectTimeFrom);

    if (startTime != null && mounted) {
      final startDateTime =
          DateTime(2025, 1, 1, startTime.hour, startTime.minute);

      TimeOfDay? endTime = await timePicker.showTimePicker(
        context: context,
        initialTime: _shiftEndTime ??
            TimeOfDay.fromDateTime(startDateTime.add(const Duration(hours: 8))),
        helpText: strings.selectTimeTo,
      );

      if (endTime != null) {
        final endDateTime = DateTime(2025, 1, 1, endTime.hour, endTime.minute);

        if (!endDateTime.isAfter(startDateTime)) {
          // Show error if end time is before or equal to start time
          if (mounted) {
            showAppSnackbar(context,
                title: strings.timingsError, type: 'error');
          }
          return;
        }

        setState(() {
          _timingsController.text =

              // localize
              '${formatTime(startDateTime, context)} - ${formatTime(endDateTime, context)}';

          _shiftStartTime = startTime;
          _shiftEndTime = endTime;
        });
      }
    }
  }

  @override
  void dispose() {
    // Personal Information Controllers
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _nationalIdController.dispose();
    _dateOfBirthController.dispose();

    // Job Information Controllers
    _jobTitleController.dispose();
    _departmentController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _contractController.dispose();
    _timingsController.dispose();

    // Contact Information Controllers
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _countryController.dispose();
    _cityController.dispose();
    _zipCodeController.dispose();

    _hourlyRateController.dispose();

    super.dispose();
  }
}
