import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeeRatingForm extends StatefulWidget {
  const EmployeeRatingForm({
    super.key,
    required this.onDismiss,
  });

  final void Function() onDismiss;

  @override
  State<EmployeeRatingForm> createState() => _EmployeeRatingFormState();
}

class _EmployeeRatingFormState extends State<EmployeeRatingForm> {
  final _noteController = TextEditingController();
  String _selectedRating = 'good';

  // initial note?
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              height: 64,
              width: 64,
              decoration: ShapeDecoration(
                shape: const CircleBorder(),
                color: theme.colors.primaryVariant,
              ),
              margin: const EdgeInsets.only(bottom: 16),
              child: Icon(
                SolarIconsOutline.chatRoundUnread,
                color: theme.colors.primary,
                size: 40,
                applyTextScaling: false,
              )),
          Text(
            strings.employeeRatingQuestionTitle,
            style: theme.textStyles.textButton.copyWith(
              color: theme.colors.primaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildRatingItem('good'),
              const SizedBox(width: 16),
              _buildRatingItem('medium'),
              const SizedBox(width: 16),
              _buildRatingItem('poor'),
            ],
          ),
          const SizedBox(height: 16),
          SimpleTextField(
            controller: _noteController,
            onChanged: (value) {
              setState(() {});
            },
            decoration:  InputDecoration(
              hintText: strings.writeANoteForTheEmployee,
            ),
            onContainer: true,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: FooterFormButton(
              onCancel: () {
                widget.onDismiss();
              },
              onSubmitAsync: () async {
                await Future.delayed(1.seconds);
                widget.onDismiss();
              },
              // localize these labels
              submitLabel: strings.saveSend,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingItem(String id) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    final bgColor = switch (id) {
      'good' => theme.colors.successContainer,
      'medium' => theme.colors.warningContainer,
      'poor' => theme.colors.errorContainer,
      String() => theme.colors.errorContainer,
    };
    final fgColor = switch (id) {
      'good' => theme.colors.success,
      'medium' => theme.colors.warning,
      'poor' => theme.colors.error,
      String() => theme.colors.error,
    };

    final labels = switch (id) {
      'good' => strings.good,
      'medium' => strings.medium,
      'poor' => strings.poor,
      String() => strings.poor,
    };

    return Expanded(
        child: InkWell(
      onTap: () {
        setState(() {
          _selectedRating = id;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: AnimatedContainer(
        duration: 300.milliseconds,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: bgColor,
          border: Border.all(
            color: id == _selectedRating ? fgColor : Colors.transparent,
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        child: Text(
          labels,
          style: theme.textStyles.body.copyWith(color: fgColor),
          textAlign: TextAlign.center,
        ),
      ),
    ));
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }
}
