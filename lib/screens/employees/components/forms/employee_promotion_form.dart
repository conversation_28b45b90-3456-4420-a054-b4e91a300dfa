import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeePromotionForm extends StatefulWidget {
  const EmployeePromotionForm({
    super.key,
    required this.onDismiss,
  });

  final void Function() onDismiss;

  @override
  State<EmployeePromotionForm> createState() => _EmployeePromotionFormState();
}

class _EmployeePromotionFormState extends State<EmployeePromotionForm> {
  final _newPosController = TextEditingController();
  final _newSalaryController = TextEditingController();
  final _noteController = TextEditingController();

  // initial note?
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              height: 64,
              width: 64,
              decoration: ShapeDecoration(
                shape: const CircleBorder(),
                color: theme.colors.infoContainer,
              ),
              margin: const EdgeInsets.only(bottom: 16),
              child: Icon(
                SolarIconsOutline.handHeart,
                color: theme.colors.info,
                size: 40,
                applyTextScaling: false,
              )),
          Text(
            strings.promoteConfirmationTitle,
            style: theme.textStyles.textButton.copyWith(
              color: theme.colors.primaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SimpleTextField(
            controller: _newPosController,
            onChanged: (value) {
              setState(() {});
            },
            decoration:  InputDecoration(
              hintText: strings.theNewPosition,
            ),
            onContainer: true,
          ),
          const SizedBox(height: 16),
          SimpleTextField(
            controller: _newSalaryController,
            onChanged: (value) {
              setState(() {});
            },
            decoration:  InputDecoration(
              hintText: strings.theNewSalary,
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            onContainer: true,
          ),
          const SizedBox(height: 16),
          SimpleTextField(
            controller: _noteController,
            onChanged: (value) {
              setState(() {});
            },
            decoration:  InputDecoration(
              hintText: strings.optionalNote,
            ),
            onContainer: true,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: FooterFormButton(
              onCancel: () {
                widget.onDismiss();
              },
              onSubmitAsync: () async {
                await Future.delayed(1.seconds);
                widget.onDismiss();
              },
              // localize these labels
              submitLabel: strings.confirm,
            ),
          ),
        ],
      ),
    );
  }
  @override
  void dispose() {
    _noteController.dispose();
    _newPosController.dispose();
    _newSalaryController.dispose();
    super.dispose();
  }
}
