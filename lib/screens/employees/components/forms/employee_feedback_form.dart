import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeeFeedbackForm extends StatefulWidget {
  const EmployeeFeedbackForm({
    super.key,
    required this.onDismiss,
  });

  final void Function() onDismiss;

  @override
  State<EmployeeFeedbackForm> createState() => _EmployeeFeedbackFormState();
}

class _EmployeeFeedbackFormState extends State<EmployeeFeedbackForm> {
  final _titleController = TextEditingController();
  final _descController = TextEditingController();

  // initial note?
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              height: 64,
              width: 64,
              decoration: ShapeDecoration(
                shape: const CircleBorder(),
                color: theme.colors.primaryVariant,
              ),
              margin: const EdgeInsets.only(bottom: 16),
              child: Icon(
                SolarIconsOutline.user,
                color: theme.colors.primary,
                size: 40,
                applyTextScaling: false,
              )),
          Text(
            strings.writeFeedback,
            style: theme.textStyles.textButton.copyWith(
              color: theme.colors.primaryText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          SimpleTextField(
            controller: _titleController,
            onChanged: (value) {
              setState(() {});
            },
            decoration:  InputDecoration(
              hintText: strings.title,
            ),
            onContainer: true,
          ),
          const SizedBox(height: 8),
          SimpleTextField(
            controller: _descController,
            onChanged: (value) {
              setState(() {});
            },
            decoration:  InputDecoration(
              hintText: strings.description,
            ),
            onContainer: true,
            minLines: 5,
            maxLines: 5,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: FooterFormButton(
              onCancel: () {
                unfocus();
                widget.onDismiss();
              },
              onSubmitAsync: () async {
                unfocus();
                
                await Future.delayed(1.seconds);
                widget.onDismiss();
              },
              // localize these labels
              submitLabel: strings.send,
            ),
          ),
        ],
      ),
    );
  }
  @override
  void dispose() {
    _descController.dispose();
    _titleController.dispose();
    super.dispose();
  }
}
