import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/l10n/util/app_localizationx.dart';
import 'package:ako_basma/providers/locale/locale_provider.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

class EmployeeBriefCard extends ConsumerWidget {
  const EmployeeBriefCard({
    super.key,
    required this.date,
    this.status,
    this.entries,
    this.footerEntries,
    this.hasFooterButtons = false,
    this.onApprove,
    this.onReject,
  });

  final DateTime date;
  final String? status;

  /// [entries] is a list of maps with the following keys:
  /// - title: String
  /// - value: String
  /// - valueColor: Color
  /// - icon: IconData
  final List<Map<String, dynamic>>? entries;
  final List<Map<String, dynamic>>? footerEntries;
  final bool hasFooterButtons;
  final Future<void> Function()? onApprove;
  final Future<void> Function()? onReject;

  static const successTypes = ['paid', 'approved', 'success', 'completed'];
  static const errorTypes = ['rejected', 'failed', 'unpaid'];
  static const warningTypes = ['pending'];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final strings = AppLocalizations.of(context)!;
    final theme = AppTheme.of(context);
    // print(
    //   formatDate(
    //     date,
    //     context,
    //     format: 'dd MMM yyyy',
    //   ),
    // );
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colors.backgroundContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colors.strokeColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(
              context,
              formatDateDmyText(date, context),
              // formatDate(
              //   date,
              //   context,
              //   format: 'dd MMM yyyy',
              // ),
              status),
          const SizedBox(height: 4),
          if (entries != null)
            ...entries!
                .asMap()
                .entries
                .map((entry) => _buildEntry(context, entry.key, entry.value)),
          if (footerEntries != null) ...[
            const SizedBox(height: 16),
            Divider(height: 0, color: theme.colors.strokeColor)
          ],
          if (footerEntries != null)
            ...footerEntries!.map((entry) => _buildEntry(context, 2, entry)),
          if (hasFooterButtons)
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                children: [
                  Expanded(
                    child: AppOutlinedButton.async(
                      label: strings.reject,
                      tintColor: theme.colors.error,
                      textStyle: theme.textStyles.body3
                          .copyWith(color: theme.colors.error),
                      // onTap: () {},
                      onPressed: onReject,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                      child: AppOutlinedButton.async(
                    label: strings.approve,
                    tintColor: theme.colors.success,
                    textStyle: theme.textStyles.body3
                        .copyWith(color: theme.colors.success),
                    onPressed: onApprove,
                  )),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEntry(
      BuildContext context, int index, Map<String, dynamic> entry) {
    final theme = AppTheme.of(context);

    final contents = Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: entry['title'] ?? '',
            style: theme.textStyles.body3.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          TextSpan(
            text: ' : ',
            style: theme.textStyles.body3.copyWith(
              color: theme.colors.tertiaryText,
            ),
          ),
          WidgetSpan(child: SizedBox(width: 6)),
          TextSpan(
            text: entry['value'] ?? '',
            style: theme.textStyles.body3.copyWith(
              fontSize: 12,
              height: index == 0 ? 1.5 : 2,
              color: (entry['valueColor'] is Color)
                  ? entry['valueColor'] as Color
                  : theme.colors.secondaryText,
            ),
          ),
        ],
      ),
    );
    if (entry['icon'] != null && entry['icon'] is IconData) {
      return Row(
        children: [
          Icon(entry['icon'] as IconData,
              size: 16, color: theme.colors.secondaryText),
          const SizedBox(width: 8),
          contents,
        ],
      );
    }
    return contents;
  }

  Widget _buildHeader(BuildContext context, String title, [String? status]) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    ;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: theme.textStyles.fieldLabel.copyWith(
            fontSize: 12,
            color: theme.colors.secondaryText,
          ),
        )),
        if (status != null)
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: successTypes.contains(status)
                    ? theme.colors.successContainer
                    : errorTypes.contains(status)
                        ? theme.colors.errorContainer
                        : theme.colors.warningContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                strings.translatedLabel(status) ??
                    toBeginningOfSentenceCase(status),
                style: theme.textStyles.fieldText2.copyWith(
                  fontSize: 12,
                  color: successTypes.contains(status)
                      ? theme.colors.success
                      : errorTypes.contains(status)
                          ? theme.colors.error
                          : theme.colors.warning,
                ),
              )),
      ],
    );
  }
}
