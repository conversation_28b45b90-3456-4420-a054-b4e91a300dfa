import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:intl/intl.dart' as intl; // Required for date/time formatting
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/models/employee_data.dart';

class EmployeeExpenseRequestCard extends StatelessWidget {
  const EmployeeExpenseRequestCard({
    super.key,
    required this.data,
    required this.amount,
    required this.date,
    required this.onApprove,
    required this.onReject,
    this.statusType = 'pending',
  });

  final EmployeeData data;
  final double amount;
  final DateTime date;
  final VoidCallback onApprove;
  final VoidCallback onReject;
  final String statusType;

  // Helper to get status text
  String _getStatusText(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    switch (statusType) {
      case 'pending':
        return strings.pending;
      case 'confirmed':
        return strings.confirmed;
      case 'rejected':
        return strings.rejected;
      default:
        return ''; // Handle unexpected values
    }
  }

  // Helper to get status color
  Color _getStatusColor(BuildContext context, {required bool isBackground}) {
    final colors = Theme.of(context).extension<AppColors>()!;
    switch (statusType) {
      case 'pending':
        return isBackground ? colors.warningContainer : colors.warning;
      case 'confirmed':
        // Replace with actual confirmed colors from your theme
        return isBackground
            ? colors.successContainer
            : colors.success; // Example colors, replace as needed
      case 'rejected':
        // Replace with actual rejected colors from your theme
        return isBackground
            ? colors.errorContainer
            : colors.error; // Example colors, replace as needed
      default:
        return Colors.transparent; // Handle unexpected values
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colors.strokeColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header section
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage: NetworkImage(data.imageUrl),
                // Add a placeholder or error widget if image fails to load
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(data.name,
                        style: textStyles.textButton
                            .copyWith(color: colors.primaryText)),
                    const SizedBox(height: 8),
                    Text(
                      // reason of expense ig?
                      // 'Expense Reimbursement Request',
                      strings.expenseRequests,
                      // data.contract,
                      style: textStyles.body3.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Leave details section

          // const SizedBox(height: 8),
          _buildInfoRow(
            context,
            icon: Iconsax.coin_copy,
            label: '${strings.amount} :',
            value: formatCurrency(amount, context),
          ),

          Row(
            children: [
              Expanded(
                child: _buildInfoRow(
                  context,
                  icon: SolarIconsOutline.calendarMinimalistic,
                  label: strings.date,
                  value: formatDateDmyText(date, context),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(context, isBackground: true),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getStatusText(context),
                  style: textStyles.body3.copyWith(
                    fontSize: 10,
                    color: _getStatusColor(context, isBackground: false),
                  ),
                ),
              ),
            ],
          ),
          // Action buttons
          if (statusType == 'pending')
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                children: [
                  Expanded(
                    child: AppOutlinedButton.async(
                      label: strings.reject,
                      tintColor: colors.error,
                      textStyle: textStyles.body3.copyWith(color: colors.error),
                      // onTap: () {},
                      onPressed: () async {
                        await Future.delayed(1.seconds);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                      child: AppOutlinedButton(
                    label: strings.approve,
                    tintColor: colors.success,
                    textStyle: textStyles.body3.copyWith(color: colors.success),
                    onTap: () {},
                  )),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String? label,
    String? value,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 16,
            color: colors.secondaryText,
          ),
          const SizedBox(width: 8),
          if (label != null)
            Text(
              label,
              style: textStyles.body3.copyWith(
                color: colors.secondaryText,
              ),
            ),
          if (label != null && value != null)
            const SizedBox(width: 8), // Reduced space here
          if (value != null)
            Text(
              value,
              style: textStyles.body3.copyWith(
                color: colors.primaryText,
              ),
            ),
        ],
      ),
    );
  }
}
