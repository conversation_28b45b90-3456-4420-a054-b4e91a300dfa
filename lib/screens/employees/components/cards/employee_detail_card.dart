import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/popups/common/icon_info_popup.dart';
import 'package:ako_basma/screens/employees/components/forms/employee_promotion_form.dart';
import 'package:ako_basma/screens/employees/components/forms/employee_rating_form.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/models/employee_data.dart';

class EmployeeDetailCard extends StatelessWidget {
  final GlobalKey _contextMenuButtonKey = GlobalKey();

  EmployeeDetailCard({
    super.key,
    required this.data,
    this.showDates = false,
  });

  final EmployeeData data;
  final bool showDates;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        color: theme.colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colors.strokeColor,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header section
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                splashFactory: NoSplash.splashFactory,
                child: CircleAvatar(
                  radius: 24,
                  backgroundImage: NetworkImage(data.imageUrl),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              data.name,
                              style: theme.textStyles.headline4.copyWith(
                                fontSize: 12,
                                color: theme.colors.secondaryText,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              data.contract,
                              style: theme.textStyles.body3.copyWith(
                                color: theme.colors.primaryText,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      key: _contextMenuButtonKey,
                      onTap: () {
                        showLocalContextMenu(
                            context: context,
                            buttonKey: _contextMenuButtonKey,
                            items: [
                              {
                                'icon': SolarIconsOutline.chatRoundUnread,
                                'label': strings.performance,
                                'onPressed': () {
                                  showAppDialog(context, (context, sc) {
                                    return EmployeeRatingForm(onDismiss: () {
                                      if (context.mounted) {
                                        context.pop();
                                      }
                                    });
                                  }, useRootNavigator: true);
                                },
                              },
                              {
                                'icon': SolarIconsOutline.handHeart,
                                'label': strings.promotion,
                                'onPressed': () {
                                  showAppDialog(context, (context, sc) {
                                    return EmployeePromotionForm(onDismiss: () {
                                      if (context.mounted) {
                                        context.pop();
                                      }
                                    });
                                  }, useRootNavigator: true);
                                },
                              },
                              {
                                'icon': SolarIconsOutline.archive,
                                'label': strings.archive,
                                'onPressed': () {
                                  showAppDialog(context, (context, sc) {
                                    return IconInfoPopup(
                                      schemeColor: theme.colors.warning,
                                      iconData: SolarIconsOutline.archive,
                                      iconBgColor:
                                          theme.colors.warningContainer,
                                      title: strings.archiveEmployee,
                                      description:
                                          strings.archiveEmployeeWarning,
                                      footerText: strings
                                          .archiveEmployeeWarningSubtitle,
                                      onCancelPressed: () {
                                        context.pop();
                                      },
                                      onSubmitPressedAsync: () async {},
                                    );
                                  }, useRootNavigator: true);
                                },
                              },
                              {
                                'icon':
                                    SolarIconsOutline.trashBinMinimalistic_2,
                                'label': strings.deleteEmployee,
                                'color': theme.colors.error,
                                'iconColor': theme.colors.error,
                                'onPressed': () {
                                  showAppDialog(context, (context, sc) {
                                    return IconInfoPopup(
                                      schemeColor: theme.colors.secondary,
                                      iconData: SolarIconsOutline
                                          .trashBinMinimalistic_2,
                                      iconColor: theme.colors.error,
                                      iconBgColor: theme.colors.errorContainer,
                                      title: strings.deleteEmployee,
                                      description:
                                          strings.deleteEmployeeWarning,
                                      footerText: strings.proceedConfirmation,
                                      onCancelPressed: () {
                                        context.pop();
                                      },
                                      onSubmitPressedAsync: () async {},
                                    );
                                  }, useRootNavigator: true);
                                },
                              },
                            ],
                            positionShift: (offset, size) =>
                                offset.translate(0, size.height + 8),
                            showDivider: true);
                      },
                      child: Icon(
                        Iconsax.more_copy,
                        color: theme.colors.secondaryText,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Details section
          _buildDetailRow(
            context,
            icon: SolarIconsOutline.user,
            label: strings.jobTitle,
            value: data.jobTitle,
          ),
          const SizedBox(height: 4),
          _buildDetailRow(
            context,
            icon: SolarIconsOutline.usersGroupTwoRounded,
            label: strings.department,
            value: data.department,
          ),
          // Conditionally show phone detail
          if (data.phone != null && data.phone!.isNotEmpty) ...[
            const SizedBox(height: 4),
            _buildDetailRow(
              context,
              icon: SolarIconsOutline.phone,
              label: strings.phone,
              value: embedLtr(data.phone!),
            ),
          ],
          if (showDates) ...[
            if (data.dateOfJoining != null) ...[
              const SizedBox(height: 4),
              _buildDetailRow(context,
                  icon: SolarIconsOutline.calendarMinimalistic,
                  label: strings.dateOfJoining,
                  value: formatDateDmyText(data.dateOfJoining, context)),
            ],
            if (data.dateOfResignation != null) ...[
              const SizedBox(height: 4),
              _buildDetailRow(context,
                  icon: SolarIconsOutline.calendarMinimalistic,
                  label: strings.dateOfResignation,
                  value: formatDateDmyText(data.dateOfResignation, context)),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
          color: colors.background,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: colors.primaryVariant)),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: colors.secondaryText,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              label,
              style: textStyles.body3.copyWith(
                fontSize: 10,
                color: colors.secondaryText,
              ),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: textStyles.buttonSmall.copyWith(
              fontSize: 10,
              color: colors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }
}
