import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart'; // Required for date/time formatting
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/models/employee_data.dart';

class EmployeeShiftChangeCard extends StatelessWidget {
  const EmployeeShiftChangeCard({
    super.key,
    required this.data,
    required this.shiftDate,
    required this.currentShiftStart,
    required this.currentShiftEnd,
    required this.requestedShiftStart,
    required this.requestedShiftEnd,
    required this.location,
    required this.onApprove,
    required this.onReject,
    this.statusType = 'pending',
  });

  final EmployeeData data;
  final DateTime shiftDate;
  final DateTime currentShiftStart;
  final DateTime currentShiftEnd;
  final DateTime requestedShiftStart;
  final DateTime requestedShiftEnd;
  final String location;
  final VoidCallback onApprove;
  final VoidCallback onReject;
  final String statusType;

  // Helper to format time
  String _formatTime(DateTime time) {
    return DateFormat('hh:mm a').format(time);
  }

  // Helper to get status text
  String _getStatusText(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    switch (statusType) {
      case 'pending':
        return strings.pending;
      case 'confirmed':
        return strings.confirmed;
      case 'rejected':
        return strings.rejected;
      default:
        return ''; // Handle unexpected values
    }
  }

  // Helper to get status color
  Color _getStatusColor(BuildContext context, {required bool isBackground}) {
    final colors = Theme.of(context).extension<AppColors>()!;
    switch (statusType) {
      case 'pending':
        return isBackground ? colors.warningContainer : colors.warning;
      case 'confirmed':
        return isBackground ? colors.successContainer : colors.success;
      case 'rejected':
        return isBackground ? colors.errorContainer : colors.error;
      default:
        return Colors.transparent; // Handle unexpected values
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final strings = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colors.strokeColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header section
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage: NetworkImage(data.imageUrl),
                // Add a placeholder or error widget if image fails to load
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        data.name,
                        style: textStyles.headline4.copyWith(
                          fontSize: 12,
                          color: colors.secondaryText,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        data.contract,
                        style: textStyles.body3.copyWith(
                          color: colors.primaryText,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Shift details section
          _buildInfoRow(
            context,
            icon: SolarIconsOutline.calendarMinimalistic,
            label: null,
            value: formatDateDmyText(shiftDate, context),
          ),
          // const SizedBox(height: 8),
          _buildInfoRow(
            context,
            icon: SolarIconsOutline.clockCircle,
            label: '${strings.currentShift} :',
            value: formatTimeRange(currentShiftStart, currentShiftEnd, context),
          ),
          // const SizedBox(height: 8),
          _buildInfoRow(
            context,
            icon: SolarIconsOutline.clockCircle,
            label: '${strings.requestedEdit} :',
            value: formatTimeRange(
                requestedShiftStart, requestedShiftEnd, context),
          ),
          Row(
            children: [
              Expanded(
                child: _buildInfoRow(
                  context,
                  icon: SolarIconsOutline.mapPoint,
                  label: location,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(context, isBackground: true),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getStatusText(context),
                  style: textStyles.body3.copyWith(
                    fontSize: 10,
                    color: _getStatusColor(context, isBackground: false),
                  ),
                ),
              ),
            ],
          ),
          // Action buttons
          if (statusType == 'pending')
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                children: [
                  Expanded(
                    child: AppOutlinedButton.async(
                      label: strings.reject,
                      tintColor: colors.error,
                      textStyle: textStyles.body3.copyWith(color: colors.error),
                      // onTap: () {},
                      onPressed: () async {
                        await Future.delayed(1.seconds);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                      child: AppOutlinedButton(
                    label: strings.approve,
                    tintColor: colors.success,
                    textStyle: textStyles.body3.copyWith(color: colors.success),
                    onTap: () {},
                  )),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String? label,
    String? value,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 16,
            color: colors.secondaryText,
          ),
          const SizedBox(width: 8),
          if (label != null)
            Text(
              label,
              style: textStyles.body3.copyWith(
                color: colors.secondaryText,
              ),
            ),
          if (label != null && value != null)
            const SizedBox(width: 8), // Reduced space here
          if (value != null)
            Text(
              value,
              style: textStyles.body3.copyWith(
                color: colors.primaryText,
              ),
            ),
        ],
      ),
    );
  }
}
