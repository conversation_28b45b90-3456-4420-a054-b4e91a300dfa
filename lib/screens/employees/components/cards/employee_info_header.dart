import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeeInfoHeader extends StatelessWidget {
  const EmployeeInfoHeader(
      {super.key,
      required this.data,
      this.items = const [],
      this.showContract = true,
      this.showJoined = true,
      this.showEmpId = true});

  final EmployeeData data;
  final List<Widget> items;
  final bool showContract;
  final bool showJoined;
  final bool showEmpId;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final theme = AppTheme.of(context);
    return _buildDetailContainer(
      context: context,
      children: [
        // Header section
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                // context.push('/employees/employee');
              },
              splashFactory: NoSplash.splashFactory,
              child: CircleAvatar(
                radius: 24,
                backgroundImage: NetworkImage(data?.imageUrl ?? ""),

                // Add a placeholder or error widget if image fails to load
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            data?.name ?? "",
                            style: theme.textStyles.headline4.copyWith(
                              fontSize: 12,
                              color: theme.colors.secondaryText,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            data?.jobTitle ?? "",
                            style: theme.textStyles.body3.copyWith(
                              color: theme.colors.primaryText,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: data.departmentColor is Color
                            ? data.departmentColor
                            : data.departmentColor is String
                                ? (dummyColors[data.departmentColor] ??
                                    theme.colors.background)
                                : theme.colors.background,
                        border: Border.all(color: theme.colors.strokeColor)),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    child: Text(
                      data?.department ?? "",
                      style: theme.textStyles.body3.copyWith(
                        fontSize: 10,
                        color: theme.colors.primaryText,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (showEmpId)
          InkWell(
            onTap: () async {
              await Clipboard.setData(ClipboardData(text: '024290003'));
              showAppSnackbar(context,
                  title: strings.employeeIdCopySuccessMsg, type: 'success');
            },
            splashFactory: NoSplash.splashFactory,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 4, 0, 8),
              child: Row(children: [
                Text(
                  '${strings.emp}:',
                  style: theme.textStyles.body3.copyWith(
                    color: theme.colors.tertiaryText,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '024290003',
                  style: theme.textStyles.buttonSmall.copyWith(
                    color: theme.colors.primary,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Iconsax.copy_copy,
                  size: 12,
                )
              ]),
            ),
          ),

        // Details section
        if (showContract)
          _buildDetailRow(
            context,
            icon: SolarIconsOutline.file,
            label: strings.contractType,
            value: data?.contract ?? "",
          ),
        if (data.email != null && data.email!.isNotEmpty) ...[
          const SizedBox(height: 4),
          _buildDetailRow(
            context,
            icon: Iconsax.sms_copy,
            label: strings.email,
            value: embedLtr(data.email),
          ),
        ],
        // Conditionally show phone detail
        if (data.phone != null && data.phone!.isNotEmpty) ...[
          const SizedBox(height: 4),
          _buildDetailRow(
            context,
            icon: SolarIconsOutline.phone,
            label: strings.phone,
            value: embedLtr(data.phone!),
          ),
        ],

        if (showJoined) ...[
          const SizedBox(height: 4),
          _buildDetailRow(
            context,
            icon: SolarIconsOutline.calendarMinimalistic,
            label: strings.joined,
            value: formatDateDmy(data.dateOfJoining ?? DateTime.now(), context),
          ),
        ],
        if (data.dateOfResignation != null) ...[
          const SizedBox(height: 4),
          _buildDetailRow(
            context,
            icon: SolarIconsOutline.calendarMinimalistic,
            label: strings.resigned,
            value: formatDateDmy(data.dateOfResignation, context),
          ),
        ],
        ...items,
      ],
    );
  }

  Widget _buildDetailContainer({
    required BuildContext context,
    required List<Widget> children,
    EdgeInsetsGeometry? margin = const EdgeInsets.fromLTRB(16, 16, 16, 0),
    EdgeInsetsGeometry? padding =
        const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: theme.colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colors.strokeColor,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    final theme = AppTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
          color: theme.colors.background,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.colors.primaryVariant)),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colors.secondaryText,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              label,
              style: theme.textStyles.body3.copyWith(
                fontSize: 10,
                color: theme.colors.secondaryText,
              ),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: theme.textStyles.buttonSmall.copyWith(
              fontSize: 10,
              color: theme.colors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }
}
