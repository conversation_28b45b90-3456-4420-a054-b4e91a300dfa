import 'dart:math';

import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/chip_selector.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/components/icon/ako_custom_icons_icons.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

class EmployeeEditSalaryPopup extends StatefulWidget {
  final EmployeeData? data;
  const EmployeeEditSalaryPopup({super.key, this.data});

  @override
  State<EmployeeEditSalaryPopup> createState() =>
      _EmployeeEditSalaryPopupState();
}

class _EmployeeEditSalaryPopupState extends State<EmployeeEditSalaryPopup> {
  final _rateController = TextEditingController();
  final _grossController = TextEditingController();
  final _taxesController = TextEditingController();
  final _netController = TextEditingController();
  String? _selectedRateType = 'hourly';

  @override
  void initState() {
    if (widget.data != null) {
      _rateController.text = '50';
      _grossController.text = '1100000';
      _taxesController.text = '10000';
      _netController.text = '1100000';
      _selectedRateType = 'hourly';
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final rateTypes = {
      // localize
      'hourly': strings.hourly,
      'monthly': strings.monthly,
      'yearly': strings.yearly,
      'fixed': strings.fixed,
    };
    return SingleChildScrollView(
      padding: EdgeInsets.only(
          bottom: max(MediaQuery.viewInsetsOf(context).bottom, 16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SectionHeading(
            title: strings.salaryDetails,
            titleStyle: theme.textStyles.headline4,
            padding: const EdgeInsets.all(0),
          ),
          const SizedBox(height: 12),
          SimpleTextField(
            controller: _rateController,
            onChanged: (_) {
              setState(() {});
            },
            decoration: InputDecoration(
              labelText:
                  // localize with selected type
                  strings.hourlyRate,
            ),
            textInputAction: TextInputAction.done,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
          ),
          const SizedBox(height: 8),
          ChipSelector.wrap(
              items: rateTypes.entries
                  .map((e) => ChipItem(tag: e.key, label: e.value))
                  .toList(),
              selectedItems: [_selectedRateType ?? "hourly"],
              onItemTap: (e) {
                setState(() {
                  _selectedRateType = e;
                });
              }),
          const SizedBox(height: 20),
          SimpleTextField(
            onChanged: (_) {
              setState(() {});
            },
            controller: _grossController,
            decoration: InputDecoration(
              labelText: strings.grossSalary,
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 12),
          SimpleTextField(
            onChanged: (_) {
              setState(() {});
            },
            controller: _taxesController,
            decoration: InputDecoration(
              labelText: strings.taxes,
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 12),
          SimpleTextField(
            onChanged: (_) {
              setState(() {});
            },
            controller: _netController,
            decoration: InputDecoration(
              labelText: strings.netSalary,
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          FooterFormButton(
            onCancel: () {
              context.pop();
            },
            onSubmitAsync: () async {
              await Future.delayed(1.seconds);
              context.pop(true);
            },
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    _rateController.dispose();
    _grossController.dispose();
    _taxesController.dispose();
    _netController.dispose();
    super.dispose();
  }
}
