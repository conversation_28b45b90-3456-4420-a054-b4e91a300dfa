import 'dart:math';

import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/l10n/util/app_localizationx.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/styles/util.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeeLeaveDetailsPopup extends StatelessWidget {
  final EmployeeData data;
  final DateTime leaveStart;
  final DateTime leaveEnd;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;
  final String statusType;

  /// in case of halfday, allday
  final String? dayType;
  const EmployeeLeaveDetailsPopup(
      {super.key,
      required this.data,
      required this.leaveStart,
      required this.leaveEnd,
      this.onApprove,
      this.onReject,
      this.dayType,
      required this.statusType});

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Header section
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(data.imageUrl),
              // Add a placeholder or error widget if image fails to load
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(data.name,
                      style: theme.textStyles.textButton
                          .copyWith(color: theme.colors.primaryText)),
                  const SizedBox(height: 8),
                  Text(
                    data.contract,
                    style: theme.textStyles.body3.copyWith(
                      color: theme.colors.tertiaryText,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Leave details section

        // const SizedBox(height: 8),
        _buildInfoRow(
          context,
          icon: Iconsax.user_remove_copy,
          label: strings.leaveType,
          value: 'Sick Leave',
        ),
        if (leaveStart.isSameDayAs(leaveEnd))
          Row(
            children: [
              _buildInfoRow(
                context,
                icon: SolarIconsOutline.calendarMinimalistic,
                label: strings.leaveFrom,
                value: formatDateDmyText(leaveStart, context),
              ),
              Padding(
                padding: const EdgeInsetsDirectional.only(start: 8),
                child: Text(
                  '(${dayType == 'halfday' ? strings.halfDay : strings.allDay})',
                  style: theme.textStyles.body2.copyWith(
                    color: theme.colors.primaryText,
                  ),
                ),
              ),
            ],
          )
        else ...[
          _buildInfoRow(
            context,
            icon: SolarIconsOutline.calendarMinimalistic,
            label: strings.leaveFrom,
            value: formatDateDmyText(leaveStart, context),
          ),
          _buildInfoRow(
            context,
            icon: SolarIconsOutline.calendarMinimalistic,
            label: strings.leaveTo,
            value: formatDateDmyText(leaveEnd, context),
          ),
        ],
        Row(
          children: [
            Expanded(
              child: _buildInfoRow(
                context,
                icon: SolarIconsOutline.calendarMinimalistic,
                label: '${strings.days}:',
                value:
                    max((leaveEnd.difference(leaveStart)).inDays, 1).toString(),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theme.colors.statusBackground(statusType),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                strings.translatedLabel(statusType) ?? statusType,
                style: theme.textStyles.body2.copyWith(
                  color: theme.colors.statusForeground(statusType),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Action buttons
        if (statusType == 'pending')
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Row(
              children: [
                Expanded(
                  child: AppOutlinedButton.async(
                    label: strings.reject,
                    tintColor: theme.colors.error,
                    // onTap: () {},
                    onPressed: () async {
                      await Future.delayed(1.seconds);
                      if (context.mounted) {
                        context.pop();
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                    child: AppOutlinedButton.async(
                  label: strings.approve,
                  tintColor: theme.colors.success,
                  onPressed: () async {
                    await Future.delayed(1.seconds);
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                )),
              ],
            ),
          ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String? label,
    String? value,
  }) {
    final theme = AppTheme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colors.secondaryText,
          ),
          const SizedBox(width: 8),
          if (label != null)
            Text(
              label,
              style: theme.textStyles.body2.copyWith(
                color: theme.colors.secondaryText,
              ),
            ),
          if (label != null && value != null)
            const SizedBox(width: 8), // Reduced space here
          if (value != null)
            Text(
              value,
              style: theme.textStyles.body2.copyWith(
                color: theme.colors.primaryText,
              ),
            ),
        ],
      ),
    );
  }
}
