import 'dart:math';

import 'package:ako_basma/components/animated_dropdown/custom_dropdown.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/components/form/simple_text_field.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

class EmployeeEditAddressPopup extends StatefulWidget {
  final EmployeeData? data;
  const EmployeeEditAddressPopup({super.key, this.data});

  @override
  State<EmployeeEditAddressPopup> createState() =>
      _EmployeeEditAddressPopupState();
}

class _EmployeeEditAddressPopupState extends State<EmployeeEditAddressPopup> {
  final _addressController = TextEditingController();
  final _cityController = SingleSelectController<String>(null);
  final _zipCodeController = TextEditingController();
  final _countryController = SingleSelectController<String>(null);

  @override
  void initState() {
    if (widget.data != null) {
      final data = widget.data!;
      _addressController.text = data.address ?? '';
      _cityController.value = data.city ?? '';
      _zipCodeController.text = data.zipCode ?? '';
      _countryController.value = data.country ?? '';
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return SingleChildScrollView(
      padding: EdgeInsets.only(
          bottom: max(MediaQuery.viewInsetsOf(context).bottom, 16)),
      child: Column(
        children: [
          SectionHeading(
            title: strings.addressInformation,
            titleStyle: theme.textStyles.headline4,
            padding: const EdgeInsets.all(0),
          ),
          const SizedBox(height: 12),
          SimpleTextField(
            onChanged: (_) {
              setState(() {});
            },
            controller: _addressController,
            decoration: InputDecoration(labelText: strings.address),
          ),
          const SizedBox(height: 12),
          CustomDropdown(
            onChanged: (p0) {},
            items: ['Baghdad', 'New York', 'Los Angeles', 'Chicago', 'Houston'],
            hintText: strings.city,
            controller: _cityController,
          ),
          const SizedBox(height: 12),
          SimpleTextField(
            onChanged: (_) {
              setState(() {});
            },
            controller: _zipCodeController,
            decoration: InputDecoration(
                labelText: strings.zipCode, counter: SizedBox()),
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
          const SizedBox(height: 12),
          CustomDropdown(
            onChanged: (p0) {},
            items: ['Iraq', 'USA', 'Canada', 'UK', 'Australia'],
            hintText: strings.country,
            controller: _countryController,
          ),
          const SizedBox(height: 16),
          FooterFormButton(
            onCancel: () {
              context.pop();
            },
            onSubmitAsync: () async {
              await Future.delayed(1.seconds);
              context.pop(true);
            },
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    _addressController.dispose();
    _cityController.dispose();
    _zipCodeController.dispose();
    _countryController.dispose();
    super.dispose();
  }
}
