import 'package:ako_basma/components/button/app_outlined_button.dart';
import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

import '../../../../components/form/custom_checkbox_list_tile.dart';

// Dummy data function based on the provided structure
Map<String, dynamic> _getFiltersData() {
  return {
    'contract': {
      'label_en': 'Contract Type',
      'options': [
        {'id': 'part_time_on_site', 'label_en': 'Part-Time - On-site'},
        {'id': 'full_time_on_site', 'label_en': 'Full-time - On-site'},
        {'id': 'freelance', 'label_en': 'Freelance'},
        {'id': 'full_time_remote', 'label_en': 'Full-time – Remote'},
        {'id': 'full_time_hybrid', 'label_en': 'Full-time - Hybrid'},
      ]
    },
    'department': {
      'label_en': 'Department',
      'options': [
        {'id': 'development', 'label_en': 'Development'},
        {'id': 'human_resources', 'label_en': 'Human Resources'},
        {'id': 'finance', 'label_en': 'Finance'},
        {'id': 'marketing', 'label_en': 'Marketing'},
      ]
    },
    'title': {
      'label_en': 'Job Title',
      'options': [
        {'id': 'software_engineer', 'label_en': 'Software Engineer'},
        {'id': 'project_manager', 'label_en': 'Project Manager'},
        {'id': 'ux_designer', 'label_en': 'UX Designer'},
        {'id': 'data_analyst', 'label_en': 'Data Analyst'},
      ],
    },
  };
}

class EmployeeFiltersSheet extends StatefulWidget {
  const EmployeeFiltersSheet({
    super.key,
    this.sc,
    this.onFilterUpdate,
    this.initialFilters,
  });

  final ScrollController? sc;
  final void Function(Map<String, Set<String>>)? onFilterUpdate;
  final Map<String, Set<String>>? initialFilters;

  @override
  State<EmployeeFiltersSheet> createState() => _EmployeeFiltersSheetState();
}

class _EmployeeFiltersSheetState extends State<EmployeeFiltersSheet> {
  Map<String, dynamic> _filterData = _getFiltersData();
  Set<String> _sectionExpansionStates = {'contract'};
  late Map<String, Set<String>> _selectedFilters;

  @override
  void initState() {
    super.initState();
    _selectedFilters =
        Map<String, Set<String>>.from(widget.initialFilters ?? {});
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          strings.filter,
          style: theme.textStyles.textButton.copyWith(
            color: theme.colors.primaryVariantDark,
          ),
        ),
        const SizedBox(height: 12),
        Flexible(
          fit: FlexFit.tight,
          child: SingleChildScrollView(
            controller: widget.sc,
            child: Column(mainAxisSize: MainAxisSize.min, children: [
              _buildFilterSection('contract'),
              const SizedBox(height: 12),
              _buildFilterSection('department'),
              const SizedBox(height: 12),
              _buildFilterSection('title'),
            ]),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: FooterFormButton(
            onSubmit: () {
              if (widget.onFilterUpdate != null) {
                widget.onFilterUpdate!(_selectedFilters);
              }
              Navigator.pop(context);
            },
            onCancel: () {
              Navigator.pop(context);
            },
          ),
        )
      ],
    );
  }

  Widget _buildFilterSection(String filterKey) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    final expanded = _sectionExpansionStates.contains(filterKey);

    final title = switch (filterKey) {
// assumed these were dynamic. lets keep them predefined for now.

      'contract' => strings.contractType,
      'department' => strings.department,
      'title' => strings.jobTitle,
      // TODO: Handle this case.
      String() => "",
    };

    final options = _filterData[filterKey]['options'] as List<dynamic>;
    final icon = switch (filterKey) {
      'contract' => SolarIconsOutline.file,
      'department' => Iconsax.buildings_2_copy,
      'title' => SolarIconsOutline.usersGroupTwoRounded,
      String() => SolarIconsOutline.file,
    };
    final appliedCount = _selectedFilters[filterKey]?.length ?? 0;

    return AnimatedContainer(
      duration: 500.milliseconds,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: expanded ? theme.colors.primary : theme.colors.strokeColor,
          )),
      child: ExpansionTile(
        initiallyExpanded: _sectionExpansionStates.contains(filterKey),
        tilePadding: EdgeInsets.fromLTRB(12, 4, 12, expanded ? 0 : 4),
        dense: true,
        visualDensity: VisualDensity.compact,
        onExpansionChanged: (value) {
          setState(() {
            if (value) {
              _sectionExpansionStates.add(filterKey);
            } else {
              _sectionExpansionStates.remove(filterKey);
            }
          });
        },
        leading: Icon(
          icon,
          color: expanded ? theme.colors.primary : theme.colors.tertiaryText,
          size: 24,
        ),
        shape: const Border(),
        title: Row(
          children: [
            Flexible(
              child: Text(
                title,
                style: theme.textStyles.body.copyWith(
                    color: expanded
                        ? theme.colors.primary
                        : theme.colors.secondaryText),
              ),
            ),
            const SizedBox(width: 16),
            if (appliedCount > 0)
              Container(
                width: 24,
                height: 24,
                decoration: ShapeDecoration(
                  shape: CircleBorder(),
                  color: theme.colors.primary,
                ),
                child: Center(
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      appliedCount.toString(),
                      style: theme.textStyles.body2.copyWith(
                        color: Color(0xffffffff),
                      ),
                    ),
                  ),
                ),
              ).animate().fadeIn()
          ],
        ),
        trailing: Icon(
          expanded
              ? HugeIcons.strokeRoundedArrowUp01
              : HugeIcons.strokeRoundedArrowDown01,
          size: 24,
          color: theme.colors.primary,
        ),
        childrenPadding: EdgeInsets.only(bottom: 4),
        children: options.map<Widget>((option) {
          String optionId = option['id'];
          // localize
          String optionLabel = option['label_en'];
          final isSelected =
              _selectedFilters[filterKey]?.contains(optionId) ?? false;

          return CheckListTile(
            title: Text(optionLabel),
            value: isSelected,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            onChanged: (bool? newValue) {
              setState(() {
                if (newValue == true) {
                  _selectedFilters.update(
                    filterKey,
                    (set) => set..add(optionId),
                    ifAbsent: () => {optionId},
                  );
                } else {
                  _selectedFilters[filterKey]?.remove(optionId);
                  if (_selectedFilters[filterKey]?.isEmpty ?? false) {
                    _selectedFilters.remove(filterKey);
                  }
                }
              });
            },
          );
        }).toList(),
      ),
    );
  }
}
