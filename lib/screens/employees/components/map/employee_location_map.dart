import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/constants/map.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart' as themeProvider;
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/util/map/marker_utils.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'dart:math';
import 'dart:async';
import 'package:ako_basma/util/map/map_calculations.dart';
import 'package:ako_basma/util/map/map_ui_utils.dart';

class EmployeeLocationMap extends ConsumerStatefulWidget {
  final List<Employee>? employees;
  final String type;
  final Map<String, Employee>? employeeActivity;
  final EdgeInsets? padding;
  final void Function(Employee employee)? onMarkerTap;

  const EmployeeLocationMap({
    super.key,
    this.employees,
    required this.type,
    this.employeeActivity,
    this.onMarkerTap,
    this.padding,
  }) : assert(
          (type == 'track' && employees != null && employeeActivity == null) ||
              (type == 'activity' && employeeActivity != null),
          'For type "track", employees must be provided and employeeActivity must be null. For type "activity", employeeActivity must be provided.',
        );

  @override
  ConsumerState<EmployeeLocationMap> createState() =>
      _EmployeeLocationMapState();
}

class _EmployeeLocationMapState extends ConsumerState<EmployeeLocationMap>
    with SingleTickerProviderStateMixin {
  late GoogleMapController mapController;
  final Map<String, Marker> _markers = {};
  List<Widget> _arrowWidgets = [];
  bool _isLoadingMarkers = true;
  bool _isMapDark = HiveUtils.getPreferredTheme() == ThemeMode.dark;
  bool _isCameraMoving = false;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  Timer? _cameraMoveEndTimer;

  // Marker dimensions and circular image offsets
  static const double _markerWidth = 51.0;
  static const double _markerHeight = 63.0;
  static const double _circularImageDimension = 43.0;
  static const double _circularImageTopOffset = 8.0;
  static const double _circularImageLeftOffset = 7.0;

  // Calculate center of the circular image relative to the top-left of the marker box
  static const double _circularImageCenterXInBox =
      _circularImageLeftOffset + _circularImageDimension / 2;
  static const double _circularImageCenterYInBox =
      _circularImageTopOffset + _circularImageDimension / 2;

  // Marker anchor point (bottom-center of the icon) relative to the top-left of the marker box
  static const double _markerAnchorXInBox = _markerWidth / 2;
  static const double _markerAnchorYInBox = _markerHeight;

  // Offset of circular image center relative to the marker's anchor point
  static const double _offsetXRelativeToAnchor =
      _circularImageCenterXInBox - _markerAnchorXInBox;
  static const double _offsetYRelativeToAnchor =
      _circularImageCenterYInBox - _markerAnchorYInBox;

  // Radius of the circular image
  static const double _circularImageRadius = _circularImageDimension / 2;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_fadeController);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMarkers();
      final theme = ref.read(themeProvider.themeProvider);

      setState(() {
        if (theme == ThemeMode.dark && !_isMapDark) {
          _isMapDark = true;
        } else {
          if (theme == ThemeMode.light && _isMapDark) {
            _isMapDark = false;
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _cameraMoveEndTimer?.cancel();
    super.dispose();
  }

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
    if (widget.type == 'activity') {
      _loadArrows();

      if (widget.employeeActivity != null &&
          widget.employeeActivity!.isNotEmpty) {
        final List<LatLng> routePoints = [];
        final sortedEntries = widget.employeeActivity!.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key));

        for (var entry in sortedEntries) {
          routePoints.add(entry.value.location);
        }

        if (routePoints.isNotEmpty) {
          final LatLngBounds bounds = getBoundsForLatLngList(routePoints);
          mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 20));
        }
      }
    } else if (widget.type == 'track') {
      if (widget.employees != null && widget.employees!.isNotEmpty) {
        final List<LatLng> employeeLocations = [];
        for (var employee in widget.employees!) {
          employeeLocations.add(employee.location);
        }
        if (employeeLocations.isNotEmpty) {
          final LatLngBounds bounds = getBoundsForLatLngList(employeeLocations);
          print(bounds);
          mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 20));
        }
      }
    }
  }

  Future<void> _loadMarkers() async {
    setState(() {
      _isLoadingMarkers = true;
      _markers.clear();
    });

    final newMarkers = <String, Marker>{};

    if (widget.type == 'activity' && widget.employeeActivity != null) {
      for (var entry in widget.employeeActivity!.entries) {
        final timestamp = entry.key;
        final employee = entry.value;
        final MarkerId markerId = MarkerId('${employee.id}_$timestamp');
        final BitmapDescriptor customIcon =
            await _getCustomMarkerIcon(employee.photoUrl);
        final Marker marker = Marker(
          markerId: markerId,
          position: employee.location,
          icon: customIcon,
          onTap: () {
            if (widget.onMarkerTap != null) {
              widget.onMarkerTap!(employee);
            }
          },
          anchor: const Offset(0.5, 0.92),
          infoWindow: InfoWindow(title: '$timestamp'),
        );
        newMarkers[markerId.value] = marker;
      }
    } else if (widget.type == 'track' && widget.employees != null) {
      for (var employee in widget.employees!) {
        final MarkerId markerId = MarkerId(employee.id);
        final BitmapDescriptor customIcon =
            await _getCustomMarkerIcon(employee.photoUrl);
        final Marker marker = Marker(
          markerId: markerId,
          position: employee.location,
          icon: customIcon,
          onTap: () {
            if (widget.onMarkerTap != null) {
              widget.onMarkerTap!(employee);
            }
          },
          anchor: const Offset(0.5, 0.92),
          infoWindow: InfoWindow(title: employee.name),
        );
        newMarkers[employee.id] = marker;
      }
    }

    setState(() {
      _markers.addAll(newMarkers);
      _isLoadingMarkers = false;
    });
  }

  Future<void> _loadArrows() async {
    final theme = AppTheme.of(context);
    if (widget.type != 'activity' ||
        widget.employeeActivity == null ||
        widget.employeeActivity!.isEmpty ||
        mapController == null ||
        _isCameraMoving) {
      setState(() {
        _arrowWidgets.clear();
      });
      return;
    }

    final List<Widget> tempArrowWidgets = [];
    final List<LatLng> routePoints = [];
    final sortedEntries = widget.employeeActivity!.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    for (var entry in sortedEntries) {
      routePoints.add(entry.value.location);
    }

    if (routePoints.length < 2) {
      setState(() {
        _arrowWidgets.clear();
      });
      return;
    }

    for (int i = 0; i < routePoints.length - 1; i++) {
      final LatLng startLatLng = routePoints[i];
      final LatLng endLatLng = routePoints[i + 1];

      final ArrowPoints adjustedPoints = await calculateAdjustedArrowPoints(
          mapController, startLatLng, endLatLng);

      final Offset adjustedStartPoint = adjustedPoints.startPoint;
      final Offset adjustedEndPoint = adjustedPoints.endPoint;

      Color lineColor = theme.colors.primary;
      if (i == routePoints.length - 2) {
        lineColor = theme.colors.success;
      }

      final double minX = min(adjustedStartPoint.dx, adjustedEndPoint.dx);
      final double minY = min(adjustedStartPoint.dy, adjustedEndPoint.dy);
      final double maxX = max(adjustedStartPoint.dx, adjustedEndPoint.dx);
      final double maxY = max(adjustedStartPoint.dy, adjustedEndPoint.dy);

      final double width = maxX - minX > 0 ? maxX - minX : 1;
      final double height = maxY - minY > 0 ? maxY - minY : 1;

      tempArrowWidgets.add(
        Positioned(
          left: minX,
          top: minY,
          width: width,
          height: height,
          child: IgnorePointer(
            child: CustomPaint(
              painter: LineArrowPainter(
                startRelative: adjustedStartPoint - Offset(minX, minY),
                endRelative: adjustedEndPoint - Offset(minX, minY),
                color: lineColor,
              ),
            ),
          ),
        ),
      );
    }

    setState(() {
      _arrowWidgets = tempArrowWidgets;
    });
  }

  Future<BitmapDescriptor> _getCustomMarkerIcon(String photoUrl) async {
    final devicePixelRatio = MediaQuery.devicePixelRatioOf(context);
    const markerHeight = 63.0;
    const markerWidth = 51.0;
    // Load the image first, then use it in the ClipOval
    final ImageProvider imageProvider = NetworkImage(photoUrl);
    await precacheImage(imageProvider, context);
    return await SizedBox(
            height: markerHeight,
            width: markerWidth,
            child: Stack(
              children: [
                SvgPicture.asset(
                  'assets/icons/map_photo_marker_${ref.watch(themeProvider.themeProvider).name}.svg',
                  height: markerHeight,
                  width: markerWidth,
                ),
                Positioned(
                    top: 8,
                    left: 7,
                    right: 7,
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: SizedBox.square(
                        dimension: 43,
                        child: ClipOval(
                          child: Image(
                            image: imageProvider,
                            height: 43,
                            width: 43,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ))
              ],
            ))
        .toBitmapDescriptor(
            logicalSize: Size(51, 63),
            imageSize: Size(51 * devicePixelRatio, 63 * devicePixelRatio));
  }

  @override
  void didUpdateWidget(covariant EmployeeLocationMap oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.employees != oldWidget.employees ||
        widget.employeeActivity != oldWidget.employeeActivity ||
        widget.type != oldWidget.type) {
      _loadMarkers();
      if (widget.type != 'activity') {
        setState(() {
          _arrowWidgets.clear();
        });
      }
    }
  }

  void _onCameraMove(CameraPosition position) {
    if (!_isCameraMoving) {
      setState(() {
        _isCameraMoving = true;
      });
      _fadeController.reverse();
    }
    _cameraMoveEndTimer?.cancel();
  }

  void _onCameraIdle() {
    _cameraMoveEndTimer?.cancel();
    _cameraMoveEndTimer = Timer(const Duration(milliseconds: 150), () {
      setState(() {
        _isCameraMoving = false;
      });
      if (widget.type == 'activity') {
        _loadArrows().then((_) {
          _fadeController.forward();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(themeProvider.themeProvider, (_, next) {
      final theme = Theme.of(context).brightness;
      setState(() {
        if (theme == Brightness.dark && !_isMapDark) {
          _isMapDark = true;
        } else {
          if (theme == Brightness.light && _isMapDark) {
            _isMapDark = false;
          }
        }
      });
    });
    return Stack(
      children: [
        GoogleMap(
          style: _isMapDark ? mapNightStyleString : null,
          onMapCreated: _onMapCreated,
          initialCameraPosition: const CameraPosition(
            target: LatLng(0, 0),
            zoom: 10,
          ),
          myLocationButtonEnabled: false,
          mapToolbarEnabled: false,
          compassEnabled: false,
          markers: _markers.values.toSet(),
          onCameraMove: _onCameraMove,
          onCameraIdle: _onCameraIdle,
          padding: widget.padding ?? EdgeInsets.zero,
        ),
        if (_isLoadingMarkers)
          PositionedDirectional(
            top: 20,
            start: 16,
            child: DotsLoadingIndicator(),
          ),
        FadeTransition(
          opacity: _fadeAnimation,
          child: Stack(
            children: _arrowWidgets,
          ),
        ),
      ],
    );
  }
}
