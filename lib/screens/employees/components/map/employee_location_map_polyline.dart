import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/components/loading/dots_loading.dart';
import 'package:ako_basma/constants/map.dart';
import 'package:ako_basma/providers/theme/theme_provider.dart' as themeProvider;
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/util/map/marker_utils.dart';
import 'dart:math';
import 'package:ako_basma/util/map/map_calculations.dart' as mapCalculations;

class EmployeeLocationMapPolyline extends ConsumerStatefulWidget {
  final List<Employee>? employees;
  final String type;
  final Map<String, Employee>? employeeActivity;

  const EmployeeLocationMapPolyline({
    super.key,
    this.employees,
    required this.type,
    this.employeeActivity,
  }) : assert(
          (type == 'track' && employees != null && employeeActivity == null) ||
              (type == 'activity' && employeeActivity != null),
          'For type "track", employees must be provided and employeeActivity must be null. For type "activity", employeeActivity must be provided.',
        );

  @override
  ConsumerState<EmployeeLocationMapPolyline> createState() =>
      _EmployeeLocationMapPolylineState();
}

class _EmployeeLocationMapPolylineState
    extends ConsumerState<EmployeeLocationMapPolyline> {
  late GoogleMapController mapController;
  final Map<String, Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  bool _isLoadingMarkers = true;
  bool _isMapDark = HiveUtils.getPreferredTheme() == ThemeMode.dark;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMarkers();
      final theme = ref.read(themeProvider.themeProvider);

      setState(() {
        if (theme == ThemeMode.dark && !_isMapDark) {
          _isMapDark = true;
        } else {
          if (theme == ThemeMode.light && _isMapDark) {
            _isMapDark = false;
          }
        }
      });
    });
  }

  void _onMapCreated(GoogleMapController controller) async {
    mapController = controller;
    if (widget.type == 'activity') {
      _loadPolylines();
      if (widget.employeeActivity != null &&
          widget.employeeActivity!.isNotEmpty) {
        final List<LatLng> routePoints = [];
        final sortedEntries = widget.employeeActivity!.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key));

        for (var entry in sortedEntries) {
          routePoints.add(entry.value.location);
        }

        if (routePoints.isNotEmpty) {
          final LatLngBounds bounds =
              mapCalculations.getBoundsForLatLngList(routePoints);
          mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 20));
        }
      }
    } else if (widget.type == 'track') {
      if (widget.employees != null && widget.employees!.isNotEmpty) {
        final List<LatLng> employeeLocations = [];
        for (var employee in widget.employees!) {
          employeeLocations.add(employee.location);
        }
        if (employeeLocations.isNotEmpty) {
          final LatLngBounds bounds =
              mapCalculations.getBoundsForLatLngList(employeeLocations);
          mapController.animateCamera(CameraUpdate.newLatLngBounds(bounds, 20));
        }
      }
    }
  }

  Future<void> _loadMarkers() async {
    setState(() {
      _isLoadingMarkers = true;
      _markers.clear();
    });

    final newMarkers = <String, Marker>{};

    if (widget.type == 'activity' && widget.employeeActivity != null) {
      for (var entry in widget.employeeActivity!.entries) {
        final timestamp = entry.key;
        final employee = entry.value;
        final MarkerId markerId = MarkerId('${employee.id}_$timestamp');
        final BitmapDescriptor customIcon =
            await _getCustomMarkerIcon(employee.photoUrl);
        final Marker marker = Marker(
          markerId: markerId,
          position: employee.location,
          icon: customIcon,
          anchor: const Offset(0.5, 0.92),
          infoWindow: InfoWindow(title: '${employee.name} ($timestamp)'),
        );
        newMarkers[markerId.value] = marker;
      }
    } else if (widget.type == 'track' && widget.employees != null) {
      for (var employee in widget.employees!) {
        final MarkerId markerId = MarkerId(employee.id);
        final BitmapDescriptor customIcon =
            await _getCustomMarkerIcon(employee.photoUrl);
        final Marker marker = Marker(
          markerId: markerId,
          position: employee.location,
          icon: customIcon,
          anchor: const Offset(0.5, 0.92),
          infoWindow: InfoWindow(title: employee.name),
        );
        newMarkers[employee.id] = marker;
      }
    }

    setState(() {
      _markers.addAll(newMarkers);
      _isLoadingMarkers = false;
    });
  }

  void _loadPolylines() {
    if (widget.type != 'activity' ||
        widget.employeeActivity == null ||
        widget.employeeActivity!.isEmpty) {
      setState(() {
        _polylines.clear();
      });
      return;
    }

    final theme = AppTheme.of(context);
    final List<LatLng> routePoints = [];
    final sortedEntries = widget.employeeActivity!.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    for (var entry in sortedEntries) {
      routePoints.add(entry.value.location);
    }

    if (routePoints.length < 2) {
      setState(() {
        _polylines.clear();
      });
      return;
    }

    final Set<Polyline> newPolylines = {};
    const double arrowheadLength = 0.0005; // Adjust this value as needed
    const double arrowheadAngleOffset = pi / 6; // 30 degrees

    for (int i = 0; i < routePoints.length - 1; i++) {
      final LatLng startLatLng = routePoints[i];
      final LatLng endLatLng = routePoints[i + 1];
      final Color lineColor = i == routePoints.length - 2
          ? theme.colors.success
          : theme.colors.primary;

      // Add main segment polyline
      final Polyline mainSegmentPolyline = Polyline(
        polylineId: PolylineId('employee_route_main_$i'),
        points: [startLatLng, endLatLng],
        color: lineColor,
        width: 3,
        geodesic: true,
        startCap: Cap.roundCap,
        endCap: Cap.roundCap,
      );
      newPolylines.add(mainSegmentPolyline);

      // Add arrowhead for the last segment
      if (i == routePoints.length - 2) {
        final double angle = atan2(endLatLng.latitude - startLatLng.latitude,
            endLatLng.longitude - startLatLng.longitude);

        final LatLng arrowheadPoint1 = LatLng(
          endLatLng.latitude -
              arrowheadLength * sin(angle - arrowheadAngleOffset),
          endLatLng.longitude -
              arrowheadLength * cos(angle - arrowheadAngleOffset),
        );
        final LatLng arrowheadPoint2 = LatLng(
          endLatLng.latitude -
              arrowheadLength * sin(angle + arrowheadAngleOffset),
          endLatLng.longitude -
              arrowheadLength * cos(angle + arrowheadAngleOffset),
        );

        final Polyline arrowheadPolyline = Polyline(
          polylineId: PolylineId('employee_route_arrowhead_$i'),
          points: [arrowheadPoint1, endLatLng, arrowheadPoint2],
          color: lineColor,
          width: 3,
          geodesic: true,
          startCap: Cap.roundCap,
          endCap: Cap.roundCap,
        );
        newPolylines.add(arrowheadPolyline);
      }
    }

    setState(() {
      _polylines.clear();
      _polylines.addAll(newPolylines);
    });
  }

  Future<BitmapDescriptor> _getCustomMarkerIcon(String photoUrl) async {
    final devicePixelRatio = MediaQuery.devicePixelRatioOf(context);
    const markerRatio = 63 / 51;
    const markerHeight = 63.0;
    const markerWidth = 51.0;
    return await SizedBox(
            height: markerHeight,
            width: markerWidth,
            child: Stack(
              children: [
                SvgPicture.asset(
                  'assets/icons/map_photo_marker_${ref.watch(themeProvider.themeProvider).name}.svg',
                  height: markerHeight,
                  width: markerWidth,
                ),
                Positioned(
                    top: 8,
                    left: 4,
                    right: 4,
                    child: AspectRatio(
                      aspectRatio: 1,
                      child: SizedBox.square(
                        dimension: 43,
                        child: ClipOval(
                            child: ImageContainer(
                          url: photoUrl,
                          height: 43,
                          width: 43,
                          fit: BoxFit.cover,
                          placeholderAsset: 'assets/icons/user.png',
                        )),
                      ),
                    ))
              ],
            ))
        .toBitmapDescriptor(
            logicalSize: Size(51, 63),
            imageSize: Size(51 * devicePixelRatio, 63 * devicePixelRatio));
  }

  @override
  void didUpdateWidget(covariant EmployeeLocationMapPolyline oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.employees != oldWidget.employees ||
        widget.employeeActivity != oldWidget.employeeActivity ||
        widget.type != oldWidget.type) {
      _loadMarkers();
      if (widget.type == 'activity') {
        _loadPolylines();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(themeProvider.themeProvider, (_, next) {
      final theme = Theme.of(context).brightness;
      setState(() {
        if (theme == Brightness.dark && !_isMapDark) {
          _isMapDark = true;
        } else {
          if (theme == Brightness.light && _isMapDark) {
            _isMapDark = false;
          }
        }
      });
    });
    return Stack(
      children: [
        GoogleMap(
          style: _isMapDark ? mapNightStyleString : null,
          onMapCreated: _onMapCreated,
          initialCameraPosition: const CameraPosition(
            target: LatLng(0, 0),
            zoom: 2.0,
          ),
          markers: _markers.values.toSet(),
          polylines: _polylines,
        ),
        if (_isLoadingMarkers)
          PositionedDirectional(
            top: 20,
            start: 16,
            child: DotsLoadingIndicator(),
          ),
      ],
    );
  }
}
