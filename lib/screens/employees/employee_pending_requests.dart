import 'package:ako_basma/components/appbar/my_appbar.dart';
import 'package:ako_basma/components/button/action_icon_button.dart';
import 'package:ako_basma/components/button/section_heading.dart';
import 'package:ako_basma/data/dummy_data.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_brief_card.dart';
import 'package:ako_basma/screens/employees/components/cards/employee_timeoff_card.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:ako_basma/util/ui/formatting.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';

class EmployeePendingRequestsScreen extends StatefulWidget {
  const EmployeePendingRequestsScreen({super.key});

  @override
  State<EmployeePendingRequestsScreen> createState() =>
      _EmployeePendingRequestsScreenState();
}

class _EmployeePendingRequestsScreenState
    extends State<EmployeePendingRequestsScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: MyAppbar(
        title: strings.pendingRequest,
        actions: [
          ActionIconButton(
            // key: _contextMenuButtonKey,
            icon: SolarIconsOutline.menuDots,
            // onPressed: () {
            //   showLocalContextMenu(
            //       context: context,
            //       buttonKey: _contextMenuButtonKey,
            //       items: [
            //         {
            //           'icon': SolarIconsOutline.chatRoundUnread,
            //           'label': 'Performance',
            //           'onPressed': () {},
            //         },
            //         {
            //           'icon': SolarIconsOutline.handHeart,
            //           'label': 'Promotion',
            //           'onPressed': () {},
            //         },
            //         {
            //           'icon': SolarIconsOutline.archive,
            //           'label': 'Archive',
            //           'onPressed': () {},
            //         },
            //         {
            //           'icon': SolarIconsOutline.trashBinMinimalistic_2,
            //           'label': 'Delete employee',
            //           'color': theme.colors.error,
            //           'onPressed': () {},
            //         },
            //       ],
            //       // maxWidth: 225,

            //       positionShift: (offset, size) =>
            //           offset.translate(0, size.height + 8),
            //       showDivider: true);
            // },
          )
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 16),
        children: [
          SectionHeading(
            title: strings.leave,
            titleStyle: theme.textStyles.headline3,
          ),
          const SizedBox(height: 5),
          EmployeeBriefCard(
            date: DateTime.now(),
            status: 'pending',
            entries: [
              {
                'title': strings.duration,
                'value': '7 ${strings.days}',
              },
              {'title': strings.leaveType, 'value': 'Sick Leave'},
              {'title': strings.approvedBy, 'value': 'HR Department'},
              {
                'title': strings.notes,
                'value': 'End of year break',
              },
            ],
            hasFooterButtons: true,
          ),
          EmployeeBriefCard(
            date: DateTime.now(),
            status: 'pending',
            entries: [
              {
                'title': strings.duration,
                'value': '7 ${strings.days}',
              },
              {'title': strings.leaveType, 'value': 'Sick Leave'},
              {'title': strings.approvedBy, 'value': 'HR Department'},
              {
                'title': strings.notes,
                'value': 'End of year break',
              },
            ],
            hasFooterButtons: true,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: SectionHeading(
              title: strings.shiftEdit,
              titleStyle: theme.textStyles.headline3,
            ),
          ),
          EmployeeBriefCard(
            date: DateTime.now(),
            entries: [
              {
                'title': strings.workDayYesNo,
                'value': strings.yes,
                'valueColor': theme.colors.success
              },
              {
                'title': strings.checkInTime,
                'value': formatTime(DateTime.now(), context)
              },
              {
                'title': strings.checkOutTime,
                'value': formatTime(DateTime.now(), context)
              },
            ],
            footerEntries: [
              {
                'title': strings.workingHours,
                'value': strings.valueHours(8),
                'icon': SolarIconsOutline.clockCircle
              },
              {
                'title': strings.notes,
                'value': strings.noIssues,
                'icon': SolarIconsOutline.document
              },
            ],
            hasFooterButtons: true,
          ),
        ],
      ),
    );
  }
}
