import 'package:ako_basma/components/button/footer_form_button.dart';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/styles/theme_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class IconInfoPopup extends StatelessWidget {
  const IconInfoPopup({
    super.key,
    required this.title,
    this.description,
    this.body,
    this.iconData,
    this.svgIcon,
    this.iconColor,
    this.iconBgColor,
    this.iconPadding,
    this.footerText,
    this.footerTextColor,
    this.schemeColor,
    this.onCancelPressed,
    this.onCancelPressedAsync,
    this.onSubmitPressed,
    this.onSubmitPressedAsync,
    this.cancelLabel,
    this.submitLabel,
    this.cancelButtonStyle,
    this.submitButtonStyle,
    this.cancelTextStyle,
    this.submitTextStyle,
    this.cancelColor,
    this.cancelFlex,
    this.submitFlex,
    this.spacing,
  });

  final Widget? body;
  final IconData? iconData;
  final String? svgIcon;
  final Color? iconColor;
  final Color? iconBgColor;
  final EdgeInsetsGeometry? iconPadding;
  final String title;
  final String? description;
  final String? footerText;
  final Color? footerTextColor;
  final Color? schemeColor;

  final void Function()? onCancelPressed;
  final Future<void> Function()? onCancelPressedAsync;
  final void Function()? onSubmitPressed;
  final Future<void> Function()? onSubmitPressedAsync;

  // FooterFormButton params
  final String? cancelLabel;
  final String? submitLabel;
  final ButtonStyle? cancelButtonStyle;
  final ButtonStyle? submitButtonStyle;
  final TextStyle? cancelTextStyle;
  final TextStyle? submitTextStyle;
  final Color? cancelColor;
  final int? cancelFlex;
  final int? submitFlex;
  final double? spacing;

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme.of(context);
    final strings = AppLocalizations.of(context)!;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (iconData != null || svgIcon != null)
          Container(
            height: 64,
            width: 64,
            decoration: ShapeDecoration(
              shape: const CircleBorder(),
              color: iconBgColor ?? theme.colors.primaryVariant,
            ),
            padding: iconPadding,
            margin: const EdgeInsets.only(bottom: 16),
            child: iconData != null
                ? Icon(
                    iconData,
                    color: iconColor ?? schemeColor ?? theme.colors.primary,
                    size: 40,
                    applyTextScaling: false,
                  )
                : SvgPicture.asset(
                    svgIcon!,
                    height: 40,
                    width: 40,
                    colorFilter: ColorFilter.mode(
                        iconColor ?? schemeColor ?? theme.colors.primary,
                        BlendMode.srcIn),
                  ),
          ),
        Text(
          title,
          style: theme.textStyles.textButton.copyWith(
            color: theme.colors.primaryText,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: description == null ? 16 : 8),
        if (description != null)
          Text(
            description!,
            textAlign: TextAlign.center,
            style: theme.textStyles.body2.copyWith(
              color: theme.colors.secondaryText,
            ),
          ),
        if (body != null) body!,
        if (footerText != null)
          Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Text(
              footerText!,
              textAlign: TextAlign.center,
              style: theme.textStyles.body2.copyWith(
                color: footerTextColor ?? schemeColor ?? theme.colors.primary,
              ),
            ),
          ),
        Padding(
          padding: const EdgeInsets.only(top: 10),
          child: FooterFormButton(
            onCancel: onCancelPressed,
            onCancelAsync: onCancelPressedAsync,
            onSubmit: onSubmitPressed,
            onSubmitAsync: onSubmitPressedAsync,
            submitColor: schemeColor,
            // localize these labels
            cancelLabel: cancelLabel ?? strings.cancel,
            submitLabel: submitLabel ?? strings.confirm,
            cancelButtonStyle: cancelButtonStyle,
            submitButtonStyle: submitButtonStyle,
            cancelTextStyle: cancelTextStyle,
            submitTextStyle: submitTextStyle,
            cancelColor: cancelColor,
            cancelFlex: cancelFlex ?? 1,
            submitFlex: submitFlex ?? 2,
            spacing: spacing ?? 12,
          ),
        )
      ],
    );
  }
}
