import 'package:ako_basma/models/employee.dart';
import 'package:ako_basma/models/employee_data.dart';
import 'package:ako_basma/screens/auth/forgot.dart';
import 'package:ako_basma/screens/chat/chat_screen.dart';
import 'package:ako_basma/screens/chat/screens/ai_chat_screen.dart';
import 'package:ako_basma/screens/chat/screens/group_chat_screen.dart';
import 'package:ako_basma/screens/chat/screens/group_info_edit.dart';
import 'package:ako_basma/screens/chat/screens/group_members_edit.dart';
import 'package:ako_basma/screens/chat/screens/individual_chat_screen.dart';
import 'package:ako_basma/screens/employees/add_employee.dart';
import 'package:ako_basma/screens/employees/employee_activity_screen.dart';
import 'package:ako_basma/screens/employees/employee_archive.dart';
import 'package:ako_basma/screens/employees/employee_pending_requests.dart';
import 'package:ako_basma/screens/employees/employee_profile.dart';
import 'package:ako_basma/screens/employees/employee_resignation_req.dart';
import 'package:ako_basma/screens/employees/employees.dart';
import 'package:ako_basma/screens/home/<USER>/employee_leave_list.dart';
import 'package:ako_basma/screens/home/<USER>/employee_list.dart';
import 'package:ako_basma/screens/home/<USER>/notifications.dart';
import 'package:ako_basma/screens/home/<USER>/profile.dart';
import 'package:ako_basma/screens/home/<USER>/system_settings.dart';
import 'package:ako_basma/screens/offices/components/screens/add_department.dart';
import 'package:ako_basma/screens/offices/components/screens/add_office.dart';
import 'package:ako_basma/screens/offices/components/screens/article_details.dart';
import 'package:ako_basma/screens/offices/components/screens/article_edit.dart';
import 'package:ako_basma/screens/offices/components/screens/department_details.dart';
import 'package:ako_basma/screens/offices/components/screens/office_details.dart';
import 'package:ako_basma/screens/offices/offices.dart';
import 'package:ako_basma/screens/policy/policies_screen.dart';
import 'package:ako_basma/screens/policy/policy_form.dart';
import 'package:ako_basma/screens/policy/policy_view.dart';
import 'package:ako_basma/screens/salaries/salaries.dart';
import 'package:ako_basma/screens/salaries/salary_details.dart';
import 'package:ako_basma/screens/schedule/schedule_calendar.dart';
import 'package:ako_basma/screens/splash/splash.dart';
import 'package:ako_basma/screens/tasks/screens/task_details.dart';
import 'package:ako_basma/screens/tasks/screens/create_project.dart';
import 'package:ako_basma/screens/tasks/screens/create_task.dart';
import 'package:ako_basma/screens/tasks/tasks.dart';
import 'package:ako_basma/screens/account/account.dart';

import 'package:ako_basma/screens/verify/verify.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../components/scaffold/scaffold.dart';
import '../../providers/auth/login_state.dart';
import '../../screens/auth/auth.dart';
import '../../screens/home/<USER>';
part 'router.g.dart';

@riverpod
GoRouter router(RouterRef ref) {
  final rootNavigatorKey = GlobalKey<NavigatorState>();

  final authState = ValueNotifier<LoginStates>(ref.read(loginStateProvider));
  ref
    ..onDispose(authState.dispose)
    ..listen(
      loginStateProvider.select((value) => value),
      (_, next) {
        authState.value = next;
      },
    );

  final navigationItems = [
    //Modify the paths and body component.
    NavigationItem(
      path: '/home',
      body: (context, state) => const HomeScreen(),
      routes: [
        GoRoute(
          path: '/notifications',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) => const MaterialPage(
            child: NotificationsScreen(),
          ),
        ),
        GoRoute(
          path: '/settings',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) => const MaterialPage(
            child: SystemSettingsScreen(),
          ),
        ),
        GoRoute(
          path: '/ai',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final queryParams = state.uri.queryParameters;

            return const MaterialPage(
              child: AiChatScreen(),
            );
          },
        ),
        GoRoute(
          path: '/employeeList',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final queryParams = state.uri.queryParameters;

            return const MaterialPage(
              child: HomeEmployeeListScreen(),
            );
          },
        ),
        GoRoute(
          path: '/employeeLeaveList',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final queryParams = state.uri.queryParameters;

            return const MaterialPage(
              child: HomeEmployeeLeaveListScreen(),
            );
          },
        ),
        GoRoute(
            path: 'offices',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) {
              //  pass initial filters for a particular employee's archive.

              return const MaterialPage(
                child: OfficesScreen(),
              );
            },
            routes: [
              GoRoute(
                path: 'officeDetails',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  //  pass initial filters for a particular employee's archive.

                  return MaterialPage(
                    child: OfficeDetailsScreen(),
                  );
                },
              ),
              GoRoute(
                path: 'deptDetails',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  //  pass initial filters for a particular employee's archive.

                  return MaterialPage(
                    child: DepartmentDetailsScreen(),
                  );
                },
              ),
              GoRoute(
                  path: 'addOffice',
                  parentNavigatorKey: rootNavigatorKey,
                  pageBuilder: (context, state) {
                    final params = state.extra as Map?;
                    final Function(bool)? onSuccess =
                        params != null ? params['onSuccess'] : null;
                    return MaterialPage(
                      child: AddOfficeScreen(onSuccess: onSuccess),
                    );
                  }),
              GoRoute(
                  path: 'addDepartment',
                  parentNavigatorKey: rootNavigatorKey,
                  pageBuilder: (context, state) {
                    final params = state.extra as Map?;
                    final Function(bool)? onSuccess =
                        params != null ? params['onSuccess'] : null;
                    return MaterialPage(
                      child: AddDepartmentScreen(onSuccess: onSuccess),
                    );
                  }),
              GoRoute(
                path: 'viewArticle',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  final query = state.uri.queryParameters;
                  final params = state.extra as Map?;
                  final Function(bool)? onSuccess =
                      params != null ? params['onSuccess'] : null;
                  return MaterialPage(
                    child: ArticleDetailsScreen(
                      type: query['type'],
                      preview: false,
                    ),
                  );
                },
                routes: [
                  GoRoute(
                    path: 'edit',
                    parentNavigatorKey: rootNavigatorKey,
                    pageBuilder: (context, state) {
                      final query = state.uri.queryParameters;

                      final params = state.extra as Map?;
                      final Function(bool)? onSuccess =
                          params != null ? params['onSuccess'] : null;
                      // ..data
                      return MaterialPage(
                        child: ArticleEditScreen(
                          onSuccess: onSuccess,
                          type: query['type'],
                          isNew: false,
                          // ... prefilled data
                        ),
                      );
                    },
                  ),
                ],
              ),
              GoRoute(
                path: 'addArticle',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  final params = state.extra as Map?;
                  final query = state.uri.queryParameters;

                  final Function(bool)? onSuccess =
                      params != null ? params['onSuccess'] : null;
                  return MaterialPage(
                    child: ArticleEditScreen(
                      onSuccess: onSuccess,
                      isNew: true,
                      type: query['type'] ?? "announcement",
                    ),
                  );
                },
              ),
            ]),
        GoRoute(
            path: '/policies',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) => const MaterialPage(
                  child: PoliciesScreen(),
                ),
            routes: [
              GoRoute(
                path: '/view',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  final params = state.extra as Map?;
                  final title = params?['title'] as String?;
                  final desc = params?['desc'] as String?;
                  return MaterialPage(
                    child: PolicyViewScreen(
                      title: title,
                      desc: desc,
                    ),
                  );
                },
              ),
              GoRoute(
                path: '/add',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  final params = state.extra as Map?;
                  final Function(bool)? onSuccess =
                      params != null ? params['onSuccess'] : null;
                  return MaterialPage(
                    child: PolicyForm(
                      onSuccess: onSuccess,
                    ),
                  );
                },
              ),
              GoRoute(
                path: '/edit',
                parentNavigatorKey: rootNavigatorKey,
                pageBuilder: (context, state) {
                  final params = state.extra as Map?;
                  final title = params?['title'] as String?;
                  final desc = params?['desc'] as String?;
                  final Function(bool)? onSuccess = params?['onSuccess'];

                  return MaterialPage(
                    child: PolicyForm(
                      title: title,
                      desc: desc,
                      onSuccess: onSuccess,
                    ),
                  );
                },
              ),
            ]),
        GoRoute(
          path: '/schedule',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) => const MaterialPage(
            child: ScheduleCalendarScreen(),
          ),
        ),
        GoRoute(
          path: '/profile',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) => const MaterialPage(
            child: ProfileScreen(),
          ),
        ),
      ],
    ),
    NavigationItem(
      path: '/employees',
      body: (context, state) {
        final tab = state.uri.queryParameters['tab'];
        return EmployeesScreen(
          initialTab: tab,
        );
      },
      routes: [
        GoRoute(
          path: '/archive',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) {
            final filters = state.extra;
            //  pass initial filters for a particular employee's archive.

            return const MaterialPage(
              child: EmployeeArchiveScreen(),
            );
          },
        ),
        GoRoute(
          path: '/activity',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) {
            final data = state.extra as Employee?;
            return MaterialPage(
              child: EmployeeActivityScreen(
                data: data,
              ),
            );
            // return CustomTransitionPage(
            //   key: state.pageKey,
            //   child: EmployeeActivityScreen(
            //     data: data,
            //   ),
            //   transitionsBuilder:
            //       (context, animation, secondaryAnimation, child) {
            //     return ScaleTransition(
            //       scale: Tween<double>(begin: 0.8, end: 1.0).animate(
            //         CurvedAnimation(
            //           parent: animation,
            //           curve: Curves.easeOut,
            //         ),
            //       ),
            //       child: child,
            //     );
            //   },
            //   transitionDuration: const Duration(milliseconds: 200),
            // );
          },
        ),
        GoRoute(
          path: '/pending-requests',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) {
            return MaterialPage(
              child: EmployeePendingRequestsScreen(),
            );
          },
        ),
        GoRoute(
          path: '/resignation-request',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) {
            final data = state.extra as EmployeeData?;

            return MaterialPage(
              child: EmployeeResignationReqScreen(
                data: data,
              ),
            );
          },
        ),
        GoRoute(
            path: '/employee',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) {
              final data = state.extra as EmployeeData?;
              //  pass initial filters for a particular employee's archive.

              return MaterialPage(
                child: EmployeeProfileScreen(
                  data: data,
                ),
              );
            }),
        GoRoute(
          path: '/add',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) {
            // final data = state.extra as EmployeeData?;
            //  pass initial filters for a particular employee's archive.

            final onSuccess = state.extra as VoidCallback?;
            return MaterialPage(
              child: AddEmployeeScreen(onSuccess: onSuccess),
            );
          },
        ),
        GoRoute(
          path: '/edit',
          parentNavigatorKey: rootNavigatorKey,
          pageBuilder: (context, state) {
            final data = state.extra as Map<String, dynamic>?;
            final onSuccess = data?['onSuccess'] as VoidCallback?;
            return MaterialPage(
              child: AddEmployeeScreen(
                onSuccess: onSuccess,
                initialData: data?['data'] as EmployeeData?,
                initialTab: data?['tab'] as String?,
              ),
            );
          },
        ),
      ],
    ),
    NavigationItem(
      path: '/chat',
      body: (context, state) => const ChatScreen(),
      routes: [
        GoRoute(
          path: '/individual-chat',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final queryParams = state.uri.queryParameters;
            final userName = queryParams['userName'] ?? 'Unknown User';
            final userImage =
                queryParams['userImage'] ?? 'assets/images/person.png';
            final lastSeen = DateTime.tryParse(queryParams['lastSeen'] ?? "") ??
                DateTime.now();
            final isOnline = queryParams['isOnline'] == 'true';

            return MaterialPage(
              child: IndividualChatScreen(
                userName: userName,
                userImage: userImage,
                lastSeen: lastSeen,
                isOnline: isOnline,
              ),
            );
          },
        ),
        GoRoute(
          path: '/group-chat',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final queryParams = state.uri.queryParameters;
            final userName = queryParams['userName'] ?? 'Unknown User';
            final userImage =
                queryParams['userImage'] ?? 'assets/images/person.png';
            final extras = state.extra as Map<String, dynamic>?;

            // collective data class instead..
            final name = (extras?['name'] as String?) ?? 'Temp Group';
            final imgUrl = (extras?['image'] as String?) ??
                'https://i.pravatar.cc/150?img=3';
            final members =
                (extras?['members'] as List<EmployeeData>?) ?? <EmployeeData>[];
            return MaterialPage(
              child: GroupChatScreen(
                groupName: name,
                members: members,
                groupImage: imgUrl,
              ),
            );
          },
        ),
        GoRoute(
          path: '/groupMembersEdit',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final queryParams = state.uri.queryParameters;
            final newGroup = queryParams['create'] == 'true';
            return MaterialPage(
              child: GroupMembersEditScreen(
                newGroup: newGroup,
              ),
            );
          },
        ),
        GoRoute(
          path: '/groupInfoEdit',
          pageBuilder: (context, state) {
            // Extract parameters from route state
            final extras = state.extra as Map<String, dynamic>?;
            final members =
                (extras?['members'] as List<EmployeeData>?) ?? <EmployeeData>[];
            final queryParams = state.uri.queryParameters;
            final newGroup = queryParams['create'] == 'true';
            return MaterialPage(
              child: GroupInfoEditScreen(
                selectedEmployees: members,
                newGroup: newGroup,
              ),
            );
          },
        ),
      ],
    ),
    NavigationItem(
      path: '/tasks',
      body: (context, state) => const TasksScreen(),
      routes: [
        GoRoute(
            path: '/newProject',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) {
              final params = state.extra as Map?;
              final Function? onSuccess =
                  params != null ? params['onSuccess'] : null;
              return MaterialPage(
                child: CreateProjectScreen(
                  onSuccess: onSuccess,
                ),
              );
            }),
        GoRoute(
            path: '/newTask',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) {
              final params = state.extra as Map?;
              final Function? onSuccess =
                  params != null ? params['onSuccess'] : null;
              return MaterialPage(
                child: CreateTaskScreen(
                  onSuccess: onSuccess,
                ),
              );
            }),
        GoRoute(
            path: '/taskDetails',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) {
              // final params = state.extra as Map?;
              // final Function? onSuccess =
              //     params != null ? params['onSuccess'] : null;
              return MaterialPage(
                child: TaskDetailsScreen(),
              );
            }),
      ],
    ),
    NavigationItem(
      path: '/salaries',
      body: (context, state) => const SalariesScreen(),
      routes: [
        GoRoute(
            path: 'details',
            parentNavigatorKey: rootNavigatorKey,
            pageBuilder: (context, state) {
              final data = state.extra as EmployeeData?;
              //  pass initial filters for a particular employee's archive.

              return MaterialPage(
                child: SalaryDetailsScreen(
                  employeeData: data,
                ),
              );
            }),
      ],
    ),
  ];

  ///called at the top of redirector
  ///checks if the state.path is to be used as a deeplink.
  String? redirectLocation;

  final router = GoRouter(
    initialLocation: '/verify',
    navigatorKey: rootNavigatorKey,
    refreshListenable: authState,
    redirect: (context, state) {
      // if (state.fullPath == '/splash') {
      //   return null;
      // }
      if (kDebugMode) {
        print('full uri: ${state.uri}');
      }

      final loginState = authState.value;
      if ([
        LoginStates.waiting,
        LoginStates.notAllowed,
        LoginStates.expired,
        LoginStates.error,
        LoginStates.offline,
      ].contains(loginState)) {
        return '/verify';
      }

      final authenticated = authState.value == LoginStates.allowed;
      final verifyState = ['/verify'].contains(state.fullPath);

      List<String> allowedInitPaths = ['/login'];
      final initState = allowedInitPaths
          .any((allowedPath) => state.fullPath?.contains(allowedPath) ?? true);
      if (loginState == LoginStates.allowed && (verifyState || initState)) {
        if (authenticated && redirectLocation != null) {
          final location = redirectLocation;
          redirectLocation = null;
          return location;
        }
        return '/home';
      }
      if (!initState && !authenticated) return '/login';

      if (state.fullPath == '/' || (state.fullPath?.isEmpty ?? false)) {
        if (authenticated && redirectLocation != null) {
          final location = redirectLocation;
          redirectLocation = null;
          return location;
        }
        return authenticated ? '/home' : '/login';
      }

      // if (state.fullPath == '/' || (state.fullPath?.isEmpty ?? false)) {
      //   return '/home';
      // }
      // // if (!initState && !authenticated) return '/onboarding';
      // if (state.fullPath == '/' || (state.fullPath?.isEmpty ?? false)) {
      //   if (authenticated && redirectLocation != null) {
      //     final location = redirectLocation;
      //     redirectLocation = null;
      //     return location;
      //   }
      //   return authenticated
      //       ? '/home'
      //       : '/login'; // TODO: change to /onboarding
      // }

      return null;
    },
    routes: [
      // public routes
      // GoRoute(
      //   path: '/onboarding',
      //   pageBuilder: (context, state) => const MaterialPage(
      //     child: OnboardingScreen(),
      //   ),
      // ),
      // GoRoute(
      //   path: '/splash',
      //   pageBuilder: (context, state) => const MaterialPage(
      //     child: SplashScreen(),
      //   ),
      // ),
      GoRoute(
        path: '/verify',
        pageBuilder: (context, state) => const MaterialPage(
          child: VerifyScreen(),
        ),
      ),
      GoRoute(
        path: '/login',
        pageBuilder: (context, state) {
          return const MaterialPage(
            child: LoginScreen(),
          );
        },
        routes: [
          GoRoute(
            path: '/forgot',
            pageBuilder: (context, state) => const MaterialPage(
              child: ForgotPasswordScreen(),
            ),
          ),
        ],
      ),
      // Add a simple onboarding route to prevent errors
      // GoRoute(
      //   path: '/onboarding',
      //   pageBuilder: (context, state) => MaterialPage(
      //     child: Container(
      //       padding: const EdgeInsets.all(20),
      //       child: const Center(
      //         child: Text('Onboarding Screen'),
      //       ),
      //     ),
      //   ),
      // ),
      ShellRoute(
        builder: (_, __, child) => child,
        routes: [
          for (final (index, item) in navigationItems.indexed)
            GoRoute(
              path: item.path,
              pageBuilder: (context, _) {
                final bottomBarIndex = index;
                return CustomTransitionPage(
                  transitionsBuilder: (_, animation, __, child) {
                    return FadeTransition(
                      opacity: animation.drive(CurveTween(curve: Curves.ease)),
                      child: child,
                    );
                  },
                  child: ScaffoldWithNestedNavigation(
                    selectedIndex: bottomBarIndex,
                    navigationItems: navigationItems,
                    child: item.body(context, _),
                  ),
                );
              },
              routes: item.routes,
            ),
        ],
      ),
      // ShellRoute(
      //   builder: (context, state, child) {
      //     // Determine the selected index based on the current location
      //     int selectedIndex = 0;
      //     final location = state.uri.toString();

      //     if (location.startsWith('/home')) {
      //       selectedIndex = 0;
      //     } else if (location.startsWith('/chat')) {
      //       selectedIndex = 1;
      //     } else if (location.startsWith('/tasks')) {
      //       selectedIndex = 2;
      //     } else if (location.startsWith('/profile')) {
      //       selectedIndex = 3;
      //     }

      //     return ScaffoldWithNestedNavigation(
      //       selectedIndex: selectedIndex,
      //       navigationItems: navigationItems,
      //       child: child,
      //     );
      //   },
      //   routes: [
      //     for (final item in navigationItems)
      //       GoRoute(
      //         path: item.path,
      //         pageBuilder: (context, _) {
      //           return CustomTransitionPage(
      //             transitionsBuilder: (_, animation, __, child) {
      //               return FadeTransition(
      //                 opacity: animation.drive(CurveTween(curve: Curves.ease)),
      //                 child: child,
      //               );
      //             },
      //             child: item.body(context, _),
      //           );
      //         },
      //         routes: item.routes,
      //       ),
      //     GoRoute(
      //       path: '/clock-in-details',
      //       pageBuilder: (context, _) {
      //         return CustomTransitionPage(
      //           transitionsBuilder: (_, animation, __, child) {
      //             return FadeTransition(
      //               opacity: animation.drive(CurveTween(curve: Curves.ease)),
      //               child: child,
      //             );
      //           },
      //           child: const ClockInDetailsScreen(),
      //         );
      //       },
      //     ),
      //   ],
      // ),
    ],
  );

  ref.onDispose(router.dispose);

  return router;
}
