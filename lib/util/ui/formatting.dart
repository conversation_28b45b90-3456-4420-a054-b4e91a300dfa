import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:flutter/material.dart' as material;
import 'package:intl/intl.dart';

/// duration to hh:mm:ss
String formattedDuration(Duration duration) {
  String twoDigits(int n) => n.toString().padLeft(2, "0");
  String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
  String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
  bool hasHours = duration.inHours != 0;
  return "${(hasHours) ? ('${twoDigits(duration.inHours)}:') : ''}$twoDigitMinutes:$twoDigitSeconds";
}

/// Formats a date with ordinal suffix (e.g., "12th Mar 2024")
///
/// Parameters:
/// * [date] - The DateTime object to format
///
/// Returns a string in the format "DDth MMM YYYY" where:
/// * DD is the day with ordinal suffix (1st, 2nd, 3rd, etc.)
/// * MMM is the abbreviated month name
/// * YYYY is the 4-digit year
///
/// Date Format Patterns:
/// * d - day of month (1-31)
/// * dd - day of month with leading zero (01-31)
/// * M - month number (1-12)
/// * MM - month number with leading zero (01-12)
/// * MMM - abbreviated month name (Jan, Feb, etc.)
/// * MMMM - full month name (January, February, etc.)
/// * y - year (2 digits)
/// * yy - year (2 digits)
/// * yyyy - year (4 digits)
/// * h - hour in 12-hour format (1-12)
/// * hh - hour in 12-hour format with leading zero (01-12)
/// * H - hour in 24-hour format (0-23)
/// * HH - hour in 24-hour format with leading zero (00-23)
/// * m - minute (0-59)
/// * mm - minute with leading zero (00-59)
/// * s - second (0-59)
/// * ss - second with leading zero (00-59)
/// * a - am/pm marker
///
/// Example:
/// ```dart
/// final date = DateTime(2024, 3, 12, 14, 30);
/// final formatter = DateFormat('dd MMM yyyy HH:mm');
/// print(formatter.format(date)); // "12 Mar 2024 14:30"
/// ```
// String formatDateSpecific(DateTime? date,
//     {String format = 'dd/MM/yyyy', String? locale}) {
//   if (date == null) return '';
//   final formatter = DateFormat(format, locale);
//   return formatter.format(date);
// }

/// formats to specified - dd.mm.yyyy format
// String formatDate(DateTime date) {
//   final formatter = DateFormat('dd.MM.yyyy');
//   return formatter.format(date);
// }

/// formats to specified - yyyy-mm-dd format
String formatDateYMD(DateTime date) {
  final formatter = DateFormat('yyyy-MM-dd');
  return formatter.format(date);
}

/// formats from specified - dd.mm.yyyy format
DateTime? parseDate(String dateString) {
  final formatter = DateFormat('dd.MM.yyyy');
  try {
    return formatter.tryParse(dateString);
  } on FormatException catch (e) {
    // Handle parsing error (e.g., invalid date format)
    throw Exception("Invalid date format: $dateString");
  }
}

/// formats from specified - yyyy-mm-dd format
DateTime? parseDateYMD(String? date) {
  if (date == null) return null;
  final formatter = DateFormat('yyyy-MM-dd');

  return formatter.tryParse(date);
}

/// 12th Mar 2024
String formatDateWithOrdinalSuffix(DateTime date) {
  final day = date.day;
  // final suffix = getOrdinalSuffix(day);
  final formatter = DateFormat('MMM yyyy'); // Customize format as needed
  return '$day${getOrdinalSuffix(day)} ${formatter.format(date)}';
}

String getOrdinalSuffix(int day) {
  final ones = day % 10;
  final tens = (day ~/ 10) % 10;
  if (tens != 1) {
    switch (ones) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  } else {
    return 'th';
  }
}

/// formats to standard credit card format
String cardFormatName(String fullName, {int maxLength = 30}) {
  // Split the name by spaces
  List<String> nameParts = fullName.split(' ');

  // Handle cases where the name contains first, middle, and last names
  String firstName =
      toBeginningOfSentenceCase(nameParts.isNotEmpty ? nameParts.first : '');
  String lastName =
      toBeginningOfSentenceCase(nameParts.length > 1 ? nameParts.last : '');
  String middleInitials = toBeginningOfSentenceCase(nameParts.length > 2
      ? nameParts
          .sublist(1, nameParts.length - 1)
          .map((name) => name[0])
          .join(' ')
      : '');

  // Format the name using initials if needed
  String formattedName = '$firstName $middleInitials $lastName'.trim();

  // If the formatted name exceeds maxLength, reduce to initials
  if (formattedName.length > maxLength) {
    formattedName = '${firstName[0]} $middleInitials $lastName'.trim();
  }

  // If the name is still too long, truncate the last name
  if (formattedName.length > maxLength && lastName.isNotEmpty) {
    int remainingLength = maxLength - (formattedName.length - lastName.length);
    formattedName =
        '${firstName[0]} $middleInitials ${lastName.substring(0, remainingLength)}';
  }

  return formattedName.trim();
}

String capitalizeWords(String input) {
  if (input.isEmpty) return input;

  return input
      .split(' ')
      .map((word) {
        final cap = toBeginningOfSentenceCase(word.trim()).trim();
        return cap.isEmpty ? null : cap;
      })
      .nonNulls
      .join(" ");
}

String formatTime(
  DateTime? date,
  material.BuildContext context, {
  bool padHours = true,
  bool flipped = false,
}) {
  if (date == null) return '';

  final isLtr =
      material.Directionality.of(context) == material.TextDirection.ltr;
  final strings = AppLocalizations.of(context)!;
  final format = padHours ? 'hh:mm' : 'h:mm';
  final formatter = DateFormat(format, 'en'); // Always use 'en' for digits
  String formatted = formatter.format(date);

  // Determine AM/PM marker
  final isAm = date.hour < 12;
  final marker = isAm ? strings.am : strings.pm;

  if (!flipped) {
    return '$formatted \u202B$marker\u202C';
  } else {
    return '\u202B$marker\u202C $formatted';
  }
}

///'dd/MM/yyyy' format
String formatDateDmy(DateTime? date, material.BuildContext context,
    {String? locale}) {
  if (date == null) return "";
  final isRtl =
      material.Directionality.of(context) == material.TextDirection.rtl;

  if (isRtl) {
    // Reverse order: yyyy/MM/dd, wrap in RTL
    final formatted = DateFormat('yyyy/MM/dd', 'en').format(date);
    final x = embedRtl(formatted);
    return x;
  } else {
    // Normal order, wrap in LTR
    final formatted = DateFormat('dd/MM/yyyy', 'en').format(date);
    return embedLtr(formatted);
  }
}

/// default: dd MMM yyyy
/// padDay: d or dd
/// shortMonth: MMM or MMMM
String formatDateDmyText(
  DateTime? date,
  material.BuildContext context, {
  String? locale,
  bool padDay = true,
  bool shortMonth = true,
}) {
  if (date == null) return '';
  // final effectiveLocale =
  //     locale ?? material.Localizations.localeOf(context).languageCode;
  final isRtl =
      material.Directionality.of(context) == material.TextDirection.rtl;

  final months = getMonths(context, short: shortMonth);
  final day =
      padDay ? date.day.toString().padLeft(2, '0') : date.day.toString();
  final monthIndex = date.month - 1;
  final year = date.year.toString();

  String month = DateFormat(shortMonth ? 'MMM' : 'MMMM', 'en').format(date);
  if (isRtl) {
    month = months[monthIndex];
  }

  if (isRtl) {
    // year month day, wrap each part
    final x = embedRtl('${embedLtr(day)} ${embedRtl(month)} $year');
    return x;
  } else {
    // day month year, wrap in LTR
    return embedLtr('$day $month $year');
  }
}

List<String> getMonths(material.BuildContext context, {bool short = false}) {
  final strings = AppLocalizations.of(context)!;

  return short
      ? [
          strings.jan,
          strings.feb,
          strings.mar,
          strings.apr,
          strings.may,
          strings.jun,
          strings.jul,
          strings.aug,
          strings.sep,
          strings.oct,
          strings.nov,
          strings.dec,
        ]
      : [
          strings.january,
          strings.february,
          strings.march,
          strings.april,
          strings.may,
          strings.june,
          strings.july,
          strings.august,
          strings.september,
          strings.october,
          strings.november,
          strings.december,
        ];
}

/// Formats a long date time string with directionality and localization.
///
/// LTR: d MMMM yyyy hh:mm a
/// RTL: a hh:mm yyyy MMMM d
String formatLongDateTime(
  DateTime? date,
  material.BuildContext context,
) {
  if (date == null) return '';
  final isRtl =
      material.Directionality.of(context) == material.TextDirection.rtl;

  final dateStr =
      formatDateDmyText(date, context, padDay: false, shortMonth: false);
  final timeStr = formatTime(date, context);
  if (isRtl) {
    // a hh:mm yyyy MMMM d (time first, then date)
    return embedLtr('${embedRtl(timeStr)} $dateStr');
  } else {
    // d MMMM yyyy hh:mm a (date first, then time)
    return embedLtr('$dateStr $timeStr');
  }
}

String formatCurrency(double amount, material.BuildContext context) {
  final strings = AppLocalizations.of(context)!;
  return NumberFormat.currency(
    symbol: embedLtr('${strings.iqd} '),
    // customPattern: '#,##0.000',
    name: 'iqd',
    // locale: 'ar',
  ).format(amount);
}

/// Formats a compact date range string, localized and direction-aware.
///
/// Examples:
///   26 - 28 Jul 2025
///   26 Apr - 28 Jul 2025
///   24 Apr 2024 - 28 Jul 2025
///   3:45 PM - 4:35 PM 28 Jul 2025 (if same day)
/// RTL reverses order and formatting.
String formatCompactDateRangeText(
  DateTime? start,
  DateTime? end,
  material.BuildContext context,
) {
  if (start == null || end == null) return '';
  final isRtl =
      material.Directionality.of(context) == material.TextDirection.rtl;

  // Helper for day/month/year
  String day(DateTime dt) => dt.day.toString();
  String month(DateTime dt, {bool short = true}) {
    final months = getMonths(context, short: short);
    return months[dt.month - 1];
  }

  String year(DateTime dt) => dt.year.toString();

  bool sameDay = start.year == end.year &&
      start.month == end.month &&
      start.day == end.day;
  bool sameMonth = start.year == end.year && start.month == end.month;
  bool sameYear = start.year == end.year;

  String result;
  if (sameDay) {
    // 3:45 PM - 4:35 PM 28 Jul 2025 (LTR)
    // tested for both.
    final t1 = embedDirection(formatTime(start, context), isRtl);
    final t2 = embedDirection(formatTime(end, context), isRtl);
    final date = formatDateDmyText(start, context);
    result = '${t1} - ${t2} $date';
  } else if (sameMonth) {
    // 26 - 28 Jul 2025 (LTR)
    // tested for both.
    final d1 = day(start);
    final d2 = day(end);
    final m = month(end);
    final y = year(end);
    result = '$d1 - $d2 $m $y';
  } else if (sameYear) {
    // 26 Apr - 28 Jul 2025 (LTR)
    // tested for both.
    final d1 = day(start);
    final m1 = month(start);
    final d2 = day(end);
    final m2 = month(end);
    final y = year(end);

    result = '$d1 $m1 - $d2 $m2 $y';
  } else {
    // 24 Apr 2024 - 28 Jul 2025 (LTR)
    // tested for both.
    final d1 = embedDirection(formatDateDmyText(start, context), isRtl);
    final d2 = embedDirection(formatDateDmyText(end, context), isRtl);

    result = '$d1 - $d2';
  }

  return isRtl ? embedRtl(result) : embedLtr(result);
}

/// for time range like -  3:45 PM - 4:35 PM
String formatTimeRange(
  DateTime? start,
  DateTime? end,
  material.BuildContext context,
) {
  if (start == null || end == null) return '';
  final isRtl =
      material.Directionality.of(context) == material.TextDirection.rtl;

  final t1 = embedDirection(formatTime(start, context), isRtl);
  final t2 = embedDirection(formatTime(end, context), isRtl);
  final result = '$t1 - $t2';

  return isRtl ? embedRtl(result) : embedLtr(result);
}

String embedRtl(String? child) {
  if (child == null) return '';
  return '\u202B$child\u202C';
}

String embedLtr(String? child) {
  if (child == null) return '';
  return '\u202A$child\u202C';
}

String embedDirection(String? child, bool isRtl) {
  return isRtl ? embedRtl(child) : embedLtr(child);
}

/// Formats a datetime as a relative string (Today, Yesterday, or date) with time, direction-aware.
///
/// LTR: Today 08:00 AM
///      Yesterday 02:34 PM
///      dd/mm/yyyy 12:22 AM
/// RTL: AM 08:00 Today
///      PM 02:34 Yesterday
///      AM 12:22 yyyy/mm/dd
String formatDateTimeRelative(
  DateTime? date,
  material.BuildContext context,
) {
  if (date == null) return '';
  // final isRtl =
  //     material.Directionality.of(context) == material.TextDirection.rtl;
  final strings = AppLocalizations.of(context)!;
  final now = DateTime.now();
  final yesterday = now.subtract(const Duration(days: 1));

  String datePart;
  if (material.DateUtils.isSameDay(date, now)) {
    datePart = strings.today;
  } else if (material.DateUtils.isSameDay(date, yesterday)) {
    datePart = strings.yesterday;
  } else {
    datePart = formatDateDmy(date, context);
  }
  final timeStr = formatTime(date, context);
  return '$datePart $timeStr';
}

extension DateTimeExtensions on DateTime {
  /// Returns a [TimeOfDay] representing the time portion of this [DateTime].
  material.TimeOfDay get toTimeOfDay =>
      material.TimeOfDay(hour: hour, minute: minute);

  bool isSameDayAs([DateTime? compareDay]) {
    final other = compareDay ?? DateTime.now();
    return year == other.year && month == other.month && day == other.day;
  }

  /// Compares this DateTime with another DateTime, considering only the time portion (hours and minutes)
  bool sameTimeAs(DateTime? other) {
    if (other == null) return false;
    return hour == other.hour && minute == other.minute;
  }
}

extension TimeOfDayExtensions on material.TimeOfDay {
  /// Converts this [TimeOfDay] to a [DateTime] on the given [date].
  /// If [date] is not provided, uses today's date.
  DateTime toDateTime({DateTime? date}) {
    final baseDate = date ?? DateTime.now();
    return DateTime(baseDate.year, baseDate.month, baseDate.day, hour, minute);
  }
}
