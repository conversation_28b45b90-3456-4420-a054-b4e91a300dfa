import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter/material.dart';
import 'dart:math';

// Marker dimensions and circular image offsets
const double markerWidth = 51.0;
const double markerHeight = 63.0;
const double circularImageDimension = 43.0;
const double circularImageTopOffset = 8.0;
const double circularImageLeftOffset = 4.0;

// Calculate center of the circular image relative to the top-left of the marker box
const double circularImageCenterXInBox =
    circularImageLeftOffset + circularImageDimension / 2;
const double circularImageCenterYInBox =
    circularImageTopOffset + circularImageDimension / 2;

// Marker anchor point (bottom-center of the icon) relative to the top-left of the marker box
const double markerAnchorXInBox = markerWidth / 2;
const double markerAnchorYInBox = markerHeight * 0.92;

// Offset of circular image center relative to the marker's anchor point
const double offsetXRelativeToAnchor =
    circularImageCenterXInBox - markerAnchorXInBox;
const double offsetYRelativeToAnchor =
    circularImageCenterYInBox - markerAnchorYInBox;

// Radius of the circular image
const double circularImageRadius = circularImageDimension / 2;

// Visual correction for arrow alignment (adjust as needed)
const double horizontalVisualCorrection = -1.0;

class ArrowPoints {
  final Offset startPoint;
  final Offset endPoint;

  ArrowPoints(this.startPoint, this.endPoint);
}

Future<ArrowPoints> calculateAdjustedArrowPoints(
    GoogleMapController mapController,
    LatLng startLatLng,
    LatLng endLatLng) async {
  final Offset startScreenCoordTip = Offset(
      (await mapController.getScreenCoordinate(startLatLng)).x.toDouble(),
      (await mapController.getScreenCoordinate(startLatLng)).y.toDouble());
  final Offset endScreenCoordTip = Offset(
      (await mapController.getScreenCoordinate(endLatLng)).x.toDouble(),
      (await mapController.getScreenCoordinate(endLatLng)).y.toDouble());

  final Offset startMarkerCircleCenter = Offset(
      startScreenCoordTip.dx +
          offsetXRelativeToAnchor +
          horizontalVisualCorrection,
      startScreenCoordTip.dy + offsetYRelativeToAnchor);
  final Offset endMarkerCircleCenter = Offset(
      endScreenCoordTip.dx +
          offsetXRelativeToAnchor +
          horizontalVisualCorrection,
      endScreenCoordTip.dy + offsetYRelativeToAnchor);

  // Calculate the vector between the centers
  final Offset vector = endMarkerCircleCenter - startMarkerCircleCenter;
  final double distance = vector.distance;

  // Calculate the angle of the line connecting the two marker centers
  final double angle = atan2(vector.dy, vector.dx);

  // Adjust start and end points to be on the circular edge
  final Offset adjustedStartPoint = Offset(
    startMarkerCircleCenter.dx + circularImageRadius * cos(angle),
    startMarkerCircleCenter.dy + circularImageRadius * sin(angle),
  );
  final Offset adjustedEndPoint = Offset(
    endMarkerCircleCenter.dx - circularImageRadius * cos(angle),
    endMarkerCircleCenter.dy - circularImageRadius * sin(angle),
  );

  return ArrowPoints(adjustedStartPoint, adjustedEndPoint);
}

LatLngBounds getBoundsForLatLngList(List<LatLng> latLngs,
    {double padding = 0.05}) {
  if (latLngs.isEmpty) {
    return LatLngBounds(
        southwest: LatLng(0, 0),
        northeast: LatLng(0, 0)); // Or handle as per requirement
  }

  double minLat = latLngs.first.latitude;
  double minLon = latLngs.first.longitude;
  double maxLat = latLngs.first.latitude;
  double maxLon = latLngs.first.longitude;

  for (var latLng in latLngs) {
    if (latLng.latitude < minLat) minLat = latLng.latitude;
    if (latLng.latitude > maxLat) maxLat = latLng.latitude;
    if (latLng.longitude < minLon) minLon = latLng.longitude;
    if (latLng.longitude > maxLon) maxLon = latLng.longitude;
  }

  // Apply padding
  // minLat -= padding;
  // minLon -= padding;
  // maxLat += padding;
  // maxLon += padding;

  return LatLngBounds(
    southwest: LatLng(minLat, minLon),
    northeast: LatLng(maxLat, maxLon),
  );
}
