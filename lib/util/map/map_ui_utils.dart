import 'package:flutter/material.dart';
import 'dart:math';

class LineArrowPainter extends CustomPainter {
  final Offset startRelative;
  final Offset endRelative;
  final Color color;

  LineArrowPainter({
    required this.startRelative,
    required this.endRelative,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    canvas.drawLine(startRelative, endRelative, paint);

    final double angle = atan2(
        endRelative.dy - startRelative.dy, endRelative.dx - startRelative.dx);
    final double arrowSize = 12;

    Path path = Path();
    path.moveTo(endRelative.dx, endRelative.dy);
    path.lineTo(endRelative.dx - arrowSize * cos(angle - pi / 6),
        endRelative.dy - arrowSize * sin(angle - pi / 6));
    path.moveTo(endRelative.dx, endRelative.dy);
    path.lineTo(endRelative.dx - arrowSize * cos(angle + pi / 6),
        endRelative.dy - arrowSize * sin(angle + pi / 6));

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant LineArrowPainter oldDelegate) {
    return oldDelegate.startRelative != startRelative ||
        oldDelegate.endRelative != endRelative ||
        oldDelegate.color != color;
  }
}
