// import 'dart:async';
// import 'dart:io';

// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter_animate/flutter_animate.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';

// import '../../providers/api/api_service.dart';
// import '../hive/hive_util.dart';
// part 'push_notification.g.dart';

// @Riverpod(keepAlive: true)
// class PushNotificationService extends _$PushNotificationService {
//   final FirebaseMessaging _fcm = FirebaseMessaging.instance;
//   StreamSubscription? messageSubscription;
//   StreamSubscription? tokenSubscription;
//   final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

//   final _initializationSettingsAndroid =
//       const AndroidInitializationSettings('@mipmap/ic_launcher');

//   final _initializationSettingsIOS = const DarwinInitializationSettings(
//     requestAlertPermission: true,
//     requestBadgePermission: true,
//     requestSoundPermission: true,

//     // onDidReceiveLocalNotification: (id, title, body, payload) {},
//   );

//   late final InitializationSettings _initializationSettings;

//   static const AndroidNotificationChannel _channel = AndroidNotificationChannel(
//     'app_updates_channel', // id
//     'App updates', // title
//     description: 'Important notifications for the app.', // description

//     importance: Importance.max,
//   );
//   FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();

//   @override
//   bool build() {
//     _initializationSettings = InitializationSettings(
//         android: _initializationSettingsAndroid,
//         iOS: _initializationSettingsIOS);
//     if (HiveUtils.notficationPreference() != false) {
//       _initialize();
//     }

//     return false;
//   }

//   Future<void> enableNotifications(
//       void Function(String? message) onError) async {
//     HiveUtils.setNotificationPref(true);

//     final notificationSettings =
//         await FirebaseMessaging.instance.requestPermission();
//     if ([AuthorizationStatus.authorized, AuthorizationStatus.provisional]
//         .contains(notificationSettings.authorizationStatus)) {
//       state = true;
//     } else {
//       onError('Please allow the app to show notifications on the next screen.');
//       state = false;
//       return;
//     }
//     const errorMsg =
//         'We couln\'t turn on notifications at this moment. Please try again later.';
//     try {
//       if (messageSubscription == null || tokenSubscription == null) {
//         _setupListeners();
//       }
//       final token = await _getToken();
//       if (kDebugMode) {
//         print('obtained notification token');
//         print(token);
//       }
//       if (token != null) {
//         await _registerDeviceToken(token);
//       } else {
//         onError(errorMsg);
//         state = false;
//         return;
//       }
//     } catch (e) {
//       if (kDebugMode) {
//         print('error in enable notification - $e');
//       }
//       state = false;
//       onError(errorMsg);
//       return;
//     }
//   }

//   Future<void> disableNotifications({bool setPref = true}) async {
//     state = false;
//     if (setPref) {
//       await HiveUtils.setNotificationPref(false);
//     }

//     ///send some token clearing request...?
//     ///
//     messageSubscription?.cancel();
//     tokenSubscription?.cancel();
//     try {
//       _fcm.deleteToken();
//       _registerDeviceToken('');
//     } catch (e) {
//       if (kDebugMode) {
//         print('error while destroying fcm token - $e');
//       }
//     }
//   }

//   Future<void> _initialize() async {
//     await flutterLocalNotificationsPlugin.initialize(_initializationSettings);

//     final notificationSettings =
//         await FirebaseMessaging.instance.requestPermission();

//     if (![AuthorizationStatus.authorized, AuthorizationStatus.provisional]
//         .contains(notificationSettings.authorizationStatus)) {
//       if (kDebugMode) {
//         print(
//             'permissions to notifications arent granted. returning initialize');
//       }
//       return;
//     }
//     final token = await _setupListeners();
//     // final token = await _getToken();
//     if (kDebugMode) {
//       print('obtained token');
//       print(token);
//     }
//     if (token != null) {
//       await _registerDeviceToken(token);
//       state = true;
//     }
//   }

//   Future<String?> _setupListeners() async {
//     if (Platform.isAndroid) {
//       await _flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<
//               AndroidFlutterLocalNotificationsPlugin>()
//           ?.createNotificationChannel(_channel);
//     }

//     await FirebaseMessaging.instance
//         .setForegroundNotificationPresentationOptions(
//             alert: true, badge: true, sound: true);
//     final token = await FirebaseMessaging.instance.getToken();
//     messageSubscription =
//         FirebaseMessaging.onMessage.listen((RemoteMessage message) {
//       if (kDebugMode) {
//         print('Got a message whilst in the foreground!');
//         print('Message data: ${message.data}');
//       }
//       final notification = message.notification;
//       final android = message.notification?.android;

//       // If `onMessage` is triggered with a notification, construct our own
//       // local notification to show to users using the created channel.
//       if (notification != null && android != null) {
//         _flutterLocalNotificationsPlugin.show(
//             notification.hashCode,
//             notification.title,
//             notification.body,
//             NotificationDetails(
//               android: AndroidNotificationDetails(
//                 _channel.id,
//                 _channel.name,
//                 channelDescription: _channel.description,
//                 icon: android.smallIcon,
//                 // other properties...
//               ),
//             ));
//       }

//       if (message.notification != null) {
//         print('Message also contained a notification: ${message.notification}');
//       }
//     });
//     tokenSubscription =
//         FirebaseMessaging.instance.onTokenRefresh.listen((fcmToken) {
//       _registerDeviceToken(fcmToken);
//     })
//           ..onError((err) {
//             // Error getting token.
//           });

//     try {
//       FirebaseMessaging.onBackgroundMessage(((message) async {
//         await _backgroundHandler(message);
//       }));
//     } catch (e) {}
//     return token;
//   }

//   Future<String?> _getToken() async {
//     String? token = await _fcm.getToken();
//     if (kDebugMode) {
//       print('Token: $token');
//     }
//     return token;
//   }

//   Future<void> _registerDeviceToken(String token) async {
//     // make api call to send the token.
//     if (kDebugMode) {
//       print('sending token $token');
//     }
//     await ref.read(apiServiceProvider).updateProfile(fcmToken: token);
//     if (kDebugMode) {
//       print('sent token');
//     }
//   }

//   Future<void> _backgroundHandler(RemoteMessage message) async {
//     if (kDebugMode) {
//       print('Handling a background message ${message.messageId}');
//     }
//   }

//   Future<String?> getDeviceToken() async {
//     return await FirebaseMessaging.instance.getToken();
//   }
// }
