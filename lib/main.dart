import 'dart:io';
import 'package:ako_basma/l10n/app_localizations.dart';
import 'package:ako_basma/l10n/ku/kurdish_cupertino_localization_delegate.dart';
import 'package:ako_basma/l10n/ku/kurdish_material_localization_delegate.dart';
import 'package:ako_basma/l10n/ku/kurdish_widget_localization_delegate.dart';
import 'package:ako_basma/model/profile.dart';
import 'package:ako_basma/providers/locale/locale_provider.dart'
    show localeProvider;
import 'package:ako_basma/providers/theme/theme_provider.dart';
import 'package:ako_basma/screens/splash/splash.dart';
import 'package:ako_basma/util/hive/hive_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart'
    as libPhoneNumber;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'styles/theme.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'util/router/router.dart';

late final BaseDeviceInfo kDeviceInfo;
PackageInfo? kPackageInfo;
void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  FlutterNativeSplash.remove();

  Hive.registerAdapter(ProfileAdapter());
  await Hive.initFlutter();
  await Hive.openBox(HiveUtils.accBoxKey);
  runApp(const ProviderScope(child: MyApp()));

  // SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
  //   systemNavigationBarColor: AppColors.gray500,
  // ));

  // to get device ids if needed.
  DeviceInfoPlugin infoPlugin = DeviceInfoPlugin();
  kDeviceInfo = Platform.isAndroid
      ? await infoPlugin.androidInfo
      : await infoPlugin.iosInfo;

  // for firebase init
  // await Firebase.initializeApp(
  //   options: DefaultFirebaseOptions.currentPlatform,
  // );

  // for phone number formatter lib
  await libPhoneNumber.init();
  await initializeDateFormatting('en');

  // await initialize('en', null);
  // for package info, to get things like eg. app name and version
  try {
    kPackageInfo = await PackageInfo.fromPlatform();
  } catch (e) {
    print('Error in Package info - $e');
  }
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  bool _splashVisible = true;
  bool _contentVisible = false;
  bool _showBackgroundContent = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    MaterialTheme theme = const MaterialTheme();
    final router = ref.watch(routerProvider);
    return MaterialApp.router(
      title: 'Ako Basma Admin',
      // onGenerateTitle: (context) => AppLocalizations.of(context)?.,
      debugShowCheckedModeBanner: false,
      themeMode:
          // ThemeMode.dark,
          ref.watch(themeProvider),
      darkTheme: theme.dark(),
      theme: theme.light(),
      locale: //
          // const Locale('en', 'gb'),
          // const Locale('ar'),
          // const Locale('ku'),
          ref.watch(localeProvider),
      routerConfig: router,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        KurdishMaterialLocalizations.delegate,
        KurdishCupertinoLocalizations.delegate,
        KurdishWidgetLocalizations.delegate,
      ],
      builder: (context, child) {
        return DecoratedBox(
          decoration: BoxDecoration(
              color: ref.watch(themeProvider) == ThemeMode.dark
                  ? Colors.black
                  : Colors.white),
          child: Stack(
            children: [
              // orig...
              if (child != null)
                // Opacity(
                AnimatedOpacity(
                  opacity: _contentVisible ? 1 : 0,
                  duration: 1000.milliseconds,
                  child: child,
                ),
              if (_splashVisible)
                SplashScreen(
                  onComplete: () {
                    setState(() {
                      _splashVisible = false;
                    });
                  },
                  onFadeStart: () {
                    setState(() {
                      _contentVisible = true;
                    });
                  },
                  onContentReady: () {
                    setState(() {
                      _showBackgroundContent = true;
                    });
                  },
                ),

              // if (child != null)
              //   // Opacity(
              //   Opacity(opacity: _showBackgroundContent ? 1 : 0, child: child),

              // if (_splashVisible)
              //   AnimatedOpacity(
              //     opacity: _contentVisible ? 0 : 1,
              //     duration: 1000.milliseconds,
              //     child: SplashScreen(
              //       onContentReady: () {
              //         setState(() {
              //           _showBackgroundContent = true;
              //         });
              //       },
              //       onFadeStart: () {
              //         setState(() {
              //           _contentVisible = true;
              //         });
              //       },
              //       onComplete: () {
              //         setState(() {
              //           _splashVisible = false;
              //         });
              //       },
              //     ),
              //   ),
            ],
          ),
        );
      },
      supportedLocales: const <Locale>[
        Locale('ar'),
        Locale('en'),
        Locale('ku')
      ],
    );
  }
}
