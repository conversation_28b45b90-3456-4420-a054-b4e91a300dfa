# flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  image_path: "assets/icons/logo/logo_blue.png"

  android: "launcher_icon"
  image_path_android: "assets/icons/logo/logo_blue.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  adaptive_icon_background: "#066a96"
  adaptive_icon_foreground: "assets/icons/logo/logo_blue.png"
  adaptive_icon_foreground_inset: 0
  # adaptive_icon_monochrome: "assets/icon/monochrome.png"

  ios: true
  image_path_ios: "assets/icons/logo/logo_blue.png"
  remove_alpha_ios: true
  image_path_ios_dark_transparent: "assets/icons/logo/logo_blue.png"
  # image_path_ios_tinted_grayscale: "assets/icon/icon_tinted.png"
  # desaturate_tinted_to_grayscale_ios: true
  background_color_ios: "#066a96"

  web:
    generate: false
    # image_path: "path/to/image.png"
    # background_color: "#hexcode"
    # theme_color: "#hexcode"

  windows:
    generate: false
    # image_path: "path/to/image.png"
    # icon_size: 48 # min:48, max:256, default: 48

  macos:
    generate: false
    # image_path: "path/to/image.png"
