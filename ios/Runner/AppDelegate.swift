import Flutter
import UIKit
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    let userDefaults = UserDefaults.standard
    let theme = userDefaults.string(forKey: "preferred_theme") ?? "light"
    if theme == "dark" {
        self.window?.backgroundColor = UIColor.black
    } else {
        self.window?.backgroundColor = UIColor.white
    }
    GMSServices.provideAPIKey("AIzaSyC50qwbF7IQIWlu6_blLYbqxhXhHr8WDLk")    
    GeneratedPluginRegistrant.register(with: self)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
